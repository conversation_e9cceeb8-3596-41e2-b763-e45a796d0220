package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.*
import com.cadence.domain.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 学习交互ViewModel
 * 管理学习会话的状态和逻辑
 */
@HiltViewModel
class StudyViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(StudyUiState())
    val uiState: StateFlow<StudyUiState> = _uiState.asStateFlow()
    
    private val _currentWord = MutableStateFlow<Word?>(null)
    val currentWord: StateFlow<Word?> = _currentWord.asStateFlow()
    
    private val _studySession = MutableStateFlow<StudySession?>(null)
    val studySession: StateFlow<StudySession?> = _studySession.asStateFlow()
    
    private var words: List<Word> = emptyList()
    private var currentStudyMode: StudyMode = StudyMode.FLASHCARD
    
    /**
     * 开始学习会话
     */
    fun startStudySession(wordIds: List<String>, studyMode: StudyMode) {
        currentStudyMode = studyMode
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 加载单词数据
                words = wordIds.mapNotNull { wordId ->
                    learningRepository.getWordById(wordId)
                }
                
                if (words.isEmpty()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "没有找到要学习的单词"
                    )
                    return@launch
                }
                
                // 创建学习会话
                val session = learningRepository.startStudySession(
                    userId = "current_user",
                    wordIds = wordIds,
                    sessionType = studyMode.toSessionType()
                )
                _studySession.value = session
                
                // 初始化UI状态
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    currentIndex = 0,
                    totalWords = words.size,
                    currentOptions = if (studyMode == StudyMode.MULTIPLE_CHOICE) {
                        generateMultipleChoiceOptions(words[0])
                    } else emptyList()
                )
                
                // 设置当前单词
                _currentWord.value = words[0]
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "开始学习会话失败"
                )
            }
        }
    }
    
    /**
     * 提交答案（闪卡模式）
     */
    fun submitAnswer(isCorrect: Boolean) {
        viewModelScope.launch {
            updateSessionStats(isCorrect)
            updateLearningProgress(isCorrect)
            moveToNextWord()
        }
    }
    
    /**
     * 提交选择题答案
     */
    fun submitMultipleChoiceAnswer(selectedOption: String) {
        val currentWordData = _currentWord.value
        val isCorrect = currentWordData?.translation == selectedOption
        
        viewModelScope.launch {
            updateSessionStats(isCorrect)
            updateLearningProgress(isCorrect)
            moveToNextWord()
        }
    }
    
    /**
     * 提交拼写答案
     */
    fun submitTypingAnswer(userInput: String) {
        val currentWordData = _currentWord.value
        val isCorrect = currentWordData?.text?.equals(userInput.trim(), ignoreCase = true) == true
        
        viewModelScope.launch {
            updateSessionStats(isCorrect)
            updateLearningProgress(isCorrect)
            moveToNextWord()
        }
    }
    
    /**
     * 提交听力答案
     */
    fun submitListeningAnswer(userInput: String) {
        val currentWordData = _currentWord.value
        val isCorrect = currentWordData?.text?.equals(userInput.trim(), ignoreCase = true) == true
        
        viewModelScope.launch {
            updateSessionStats(isCorrect)
            updateLearningProgress(isCorrect)
            moveToNextWord()
        }
    }
    
    /**
     * 更新会话统计
     */
    private suspend fun updateSessionStats(isCorrect: Boolean) {
        val currentSession = _studySession.value ?: return
        val updatedSession = currentSession.copy(
            correctAnswers = if (isCorrect) currentSession.correctAnswers + 1 else currentSession.correctAnswers,
            totalQuestions = currentSession.totalQuestions + 1
        )
        _studySession.value = updatedSession
        learningRepository.updateStudySession(updatedSession)
    }
    
    /**
     * 更新学习进度
     */
    private suspend fun updateLearningProgress(isCorrect: Boolean) {
        val currentWordData = _currentWord.value ?: return
        
        try {
            learningRepository.markReviewCompleted(
                userId = "current_user",
                wordId = currentWordData.id,
                success = isCorrect
            )
        } catch (e: Exception) {
            // 记录错误但不中断流程
        }
    }
    
    /**
     * 移动到下一个单词
     */
    private fun moveToNextWord() {
        val currentIndex = _uiState.value.currentIndex
        val nextIndex = currentIndex + 1
        
        if (nextIndex >= words.size) {
            // 学习完成
            completeStudySession()
        } else {
            // 移动到下一个单词
            _currentWord.value = words[nextIndex]
            _uiState.value = _uiState.value.copy(
                currentIndex = nextIndex,
                currentOptions = if (currentStudyMode == StudyMode.MULTIPLE_CHOICE) {
                    generateMultipleChoiceOptions(words[nextIndex])
                } else emptyList()
            )
        }
    }
    
    /**
     * 完成学习会话
     */
    private fun completeStudySession() {
        viewModelScope.launch {
            try {
                val session = _studySession.value
                if (session != null) {
                    learningRepository.completeStudySession(
                        sessionId = session.id,
                        correctAnswers = session.correctAnswers,
                        totalQuestions = session.totalQuestions
                    )
                }
                
                _uiState.value = _uiState.value.copy(isCompleted = true)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "完成学习会话失败"
                )
            }
        }
    }
    
    /**
     * 生成选择题选项
     */
    private suspend fun generateMultipleChoiceOptions(targetWord: Word): List<String> {
        try {
            // 获取随机单词作为干扰项
            val randomWords = learningRepository.getRandomWords(10)
            val distractors = randomWords
                .filter { it.id != targetWord.id && it.translation != targetWord.translation }
                .map { it.translation }
                .take(3)
            
            // 组合正确答案和干扰项
            val options = (distractors + targetWord.translation).shuffled()
            return options
        } catch (e: Exception) {
            // 如果获取随机单词失败，返回默认选项
            return listOf(targetWord.translation, "选项A", "选项B", "选项C").shuffled()
        }
    }
}

/**
 * 学习界面UI状态
 */
data class StudyUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val currentIndex: Int = 0,
    val totalWords: Int = 0,
    val currentOptions: List<String> = emptyList(),
    val isCompleted: Boolean = false
)