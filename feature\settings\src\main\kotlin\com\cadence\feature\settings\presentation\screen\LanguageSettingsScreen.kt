package com.cadence.feature.settings.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import com.cadence.feature.settings.presentation.component.*
import com.cadence.feature.settings.presentation.viewmodel.SettingsViewModel
import timber.log.Timber

/**
 * 语言偏好设置界面
 * 允许用户设置默认的源语言和目标语言
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSettingsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val userPreference by viewModel.userPreference.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var showSourceLanguageDialog by remember { mutableStateOf(false) }
    var showTargetLanguageDialog by remember { mutableStateOf(false) }

    // 支持的语言列表
    val supportedLanguages = remember {
        listOf(
            Language(
                code = "zh",
                name = "中文",
                region = Region(code = "CN", name = "大陆", dialectName = "普通话")
            ),
            Language(
                code = "en",
                name = "English",
                region = Region(code = "US", name = "美国", dialectName = "美式英语")
            ),
            Language(
                code = "ja",
                name = "日本語",
                region = Region(code = "JP", name = "日本", dialectName = "标准日语")
            ),
            Language(
                code = "ko",
                name = "한국어",
                region = Region(code = "KR", name = "韩国", dialectName = "标准韩语")
            ),
            Language(
                code = "fr",
                name = "Français",
                region = Region(code = "FR", name = "法国", dialectName = "标准法语")
            ),
            Language(
                code = "de",
                name = "Deutsch",
                region = Region(code = "DE", name = "德国", dialectName = "标准德语")
            ),
            Language(
                code = "es",
                name = "Español",
                region = Region(code = "ES", name = "西班牙", dialectName = "标准西班牙语")
            ),
            Language(
                code = "it",
                name = "Italiano",
                region = Region(code = "IT", name = "意大利", dialectName = "标准意大利语")
            ),
            Language(
                code = "pt",
                name = "Português",
                region = Region(code = "PT", name = "葡萄牙", dialectName = "标准葡萄牙语")
            ),
            Language(
                code = "ru",
                name = "Русский",
                region = Region(code = "RU", name = "俄罗斯", dialectName = "标准俄语")
            )
        )
    }

    // 显示错误提示
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            Timber.e("语言设置错误: $error")
            viewModel.clearError()
        }
    }

    // 显示成功消息
    LaunchedEffect(uiState.message) {
        uiState.message?.let { message ->
            Timber.d("语言设置消息: $message")
            viewModel.clearMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "语言偏好",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            // 语言设置说明
            item {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Language,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "语言偏好设置",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "设置您常用的翻译语言，这些设置将作为翻译时的默认选项。",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))
            }

            // 默认语言设置
            item {
                SettingsGroupTitle(title = "默认语言")
            }

            item {
                SettingsSelector(
                    title = "默认源语言",
                    subtitle = "翻译时的默认输入语言",
                    icon = Icons.Default.Input,
                    selectedValue = userPreference?.defaultSourceLanguage?.name ?: "中文",
                    onClick = { showSourceLanguageDialog = true }
                )
            }

            item {
                SettingsSelector(
                    title = "默认目标语言",
                    subtitle = "翻译时的默认输出语言",
                    icon = Icons.Default.Output,
                    selectedValue = userPreference?.defaultTargetLanguage?.name ?: "English",
                    onClick = { showTargetLanguageDialog = true }
                )
            }

            item {
                SettingsDivider()
            }

            // 语言检测设置
            item {
                SettingsGroupTitle(title = "语言检测")
            }

            item {
                SettingsSwitch(
                    title = "自动检测语言",
                    subtitle = "自动识别输入文本的语言类型",
                    icon = Icons.Default.AutoAwesome,
                    checked = userPreference?.autoDetectLanguage ?: true,
                    onCheckedChange = { viewModel.toggleAutoDetectLanguage() }
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // 源语言选择对话框
    if (showSourceLanguageDialog) {
        LanguageSelectionDialog(
            title = "选择默认源语言",
            languages = supportedLanguages,
            selectedLanguage = userPreference?.defaultSourceLanguage,
            onLanguageSelected = { language ->
                viewModel.updateDefaultSourceLanguage(language)
                showSourceLanguageDialog = false
            },
            onDismiss = { showSourceLanguageDialog = false }
        )
    }

    // 目标语言选择对话框
    if (showTargetLanguageDialog) {
        LanguageSelectionDialog(
            title = "选择默认目标语言",
            languages = supportedLanguages,
            selectedLanguage = userPreference?.defaultTargetLanguage,
            onLanguageSelected = { language ->
                viewModel.updateDefaultTargetLanguage(language)
                showTargetLanguageDialog = false
            },
            onDismiss = { showTargetLanguageDialog = false }
        )
    }

    // 加载指示器
    if (uiState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}

/**
 * 语言选择对话框
 */
@Composable
private fun LanguageSelectionDialog(
    title: String,
    languages: List<Language>,
    selectedLanguage: Language?,
    onLanguageSelected: (Language) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn(
                modifier = Modifier.selectableGroup()
            ) {
                items(languages) { language ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = selectedLanguage?.code == language.code,
                                onClick = { onLanguageSelected(language) },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = selectedLanguage?.code == language.code,
                            onClick = null
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = language.name,
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = if (selectedLanguage?.code == language.code) {
                                    FontWeight.SemiBold
                                } else {
                                    FontWeight.Normal
                                }
                            )
                            Text(
                                text = language.region.dialectName,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}
