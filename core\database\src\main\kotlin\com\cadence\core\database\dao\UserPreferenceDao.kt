package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.UserPreferenceEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户偏好设置数据访问对象
 * 提供用户设置的CRUD操作
 */
@Dao
interface UserPreferenceDao {
    
    /**
     * 插入或更新用户偏好设置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreference(userPreference: UserPreferenceEntity)
    
    /**
     * 更新用户偏好设置
     */
    @Update
    suspend fun updateUserPreference(userPreference: UserPreferenceEntity)
    
    /**
     * 获取用户偏好设置
     */
    @Query("SELECT * FROM user_preferences WHERE id = :id")
    suspend fun getUserPreference(id: String = "default_user"): UserPreferenceEntity?
    
    /**
     * 获取用户偏好设置（Flow）
     */
    @Query("SELECT * FROM user_preferences WHERE id = :id")
    fun getUserPreferenceFlow(id: String = "default_user"): Flow<UserPreferenceEntity?>
    
    /**
     * 更新默认源语言
     */
    @Query("UPDATE user_preferences SET default_source_language = :language, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateDefaultSourceLanguage(
        id: String = "default_user",
        language: String,
        updatedAt: Long
    )
    
    /**
     * 更新默认目标语言
     */
    @Query("UPDATE user_preferences SET default_target_language = :language, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateDefaultTargetLanguage(
        id: String = "default_user",
        language: String,
        updatedAt: Long
    )
    
    /**
     * 更新默认源区域
     */
    @Query("UPDATE user_preferences SET default_source_region = :region, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateDefaultSourceRegion(
        id: String = "default_user",
        region: String,
        updatedAt: Long
    )
    
    /**
     * 更新默认目标区域
     */
    @Query("UPDATE user_preferences SET default_target_region = :region, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateDefaultTargetRegion(
        id: String = "default_user",
        region: String,
        updatedAt: Long
    )
    
    /**
     * 更新自动检测语言设置
     */
    @Query("UPDATE user_preferences SET auto_detect_language = :enabled, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateAutoDetectLanguage(
        id: String = "default_user",
        enabled: Boolean,
        updatedAt: Long
    )
    
    /**
     * 更新保存翻译历史设置
     */
    @Query("UPDATE user_preferences SET save_translation_history = :enabled, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateSaveTranslationHistory(
        id: String = "default_user",
        enabled: Boolean,
        updatedAt: Long
    )
    
    /**
     * 更新文化背景解释设置
     */
    @Query("UPDATE user_preferences SET enable_cultural_context = :enabled, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateEnableCulturalContext(
        id: String = "default_user",
        enabled: Boolean,
        updatedAt: Long
    )
    
    /**
     * 更新主题模式
     */
    @Query("UPDATE user_preferences SET theme_mode = :themeMode, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateThemeMode(
        id: String = "default_user",
        themeMode: String,
        updatedAt: Long
    )
    
    /**
     * 更新字体大小
     */
    @Query("UPDATE user_preferences SET font_size = :fontSize, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateFontSize(
        id: String = "default_user",
        fontSize: String,
        updatedAt: Long
    )

    /**
     * 更新应用界面语言
     */
    @Query("UPDATE user_preferences SET app_language = :appLanguage, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateAppLanguage(
        id: String = "default_user",
        appLanguage: String,
        updatedAt: Long
    )

    /**
     * 更新主题色彩
     */
    @Query("UPDATE user_preferences SET theme_color = :themeColor, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateThemeColor(
        id: String = "default_user",
        themeColor: String,
        updatedAt: Long
    )

    /**
     * 更新动态颜色设置
     */
    @Query("UPDATE user_preferences SET enable_dynamic_color = :enabled, updated_at = :updatedAt WHERE id = :id")
    suspend fun updateEnableDynamicColor(
        id: String = "default_user",
        enabled: Boolean,
        updatedAt: Long
    )

    /**
     * 删除用户偏好设置
     */
    @Query("DELETE FROM user_preferences WHERE id = :id")
    suspend fun deleteUserPreference(id: String = "default_user")
}
