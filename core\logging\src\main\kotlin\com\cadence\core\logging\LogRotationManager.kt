package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志轮转管理器
 * 负责日志文件的自动轮转、归档和清理
 */
@Singleton
class LogRotationManager @Inject constructor(
    private val context: Context,
    private val logCompressionManager: LogCompressionManager
) {
    
    companion object {
        private const val LOG_DIR = "logs"
        private const val ARCHIVE_DIR = "logs/archive"
        
        // 轮转策略配置
        private const val MAX_FILE_SIZE_MB = 10
        private const val MAX_FILES_PER_LEVEL = 5
        private const val ROTATION_CHECK_INTERVAL_MS = 300000L // 5分钟
        
        // 时间轮转配置
        private const val DAILY_ROTATION_HOUR = 0 // 每天0点轮转
        private const val WEEKLY_ROTATION_DAY = Calendar.MONDAY // 每周一轮转
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    private val dayFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    // 日志目录
    private val logDir: File by lazy {
        File(context.filesDir, LOG_DIR).apply { if (!exists()) mkdirs() }
    }
    
    private val archiveDir: File by lazy {
        File(context.filesDir, ARCHIVE_DIR).apply { if (!exists()) mkdirs() }
    }
    
    // 轮转状态管理
    private val _rotationState = MutableStateFlow(RotationState())
    val rotationState: StateFlow<RotationState> = _rotationState.asStateFlow()
    
    // 文件大小跟踪
    private val fileSizes = ConcurrentHashMap<String, Long>()
    
    // 最后轮转时间
    private val lastRotationTimes = ConcurrentHashMap<RotationType, Long>()
    
    init {
        startRotationMonitor()
        Timber.d("日志轮转管理器已初始化")
    }
    
    /**
     * 检查是否需要轮转
     */
    suspend fun checkRotationNeeded(filePath: String, currentSize: Long): Boolean = withContext(Dispatchers.IO) {
        val file = File(filePath)
        
        // 更新文件大小跟踪
        fileSizes[filePath] = currentSize
        
        // 检查大小轮转
        if (needsSizeRotation(currentSize)) {
            performSizeRotation(file)
            return@withContext true
        }
        
        // 检查时间轮转
        if (needsTimeRotation(file)) {
            performTimeRotation(file)
            return@withContext true
        }
        
        false
    }
    
    /**
     * 强制轮转指定文件
     */
    suspend fun forceRotation(filePath: String, rotationType: RotationType = RotationType.MANUAL) = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                Timber.w("尝试轮转不存在的文件: $filePath")
                return@withContext
            }
            
            when (rotationType) {
                RotationType.SIZE -> performSizeRotation(file)
                RotationType.TIME -> performTimeRotation(file)
                RotationType.MANUAL -> performManualRotation(file)
            }
            
            updateRotationState(rotationType, file.name)
            
        } catch (e: Exception) {
            Timber.e(e, "强制轮转文件失败: $filePath")
        }
    }
    
    /**
     * 轮转所有日志文件
     */
    suspend fun rotateAllLogs() = withContext(Dispatchers.IO) {
        try {
            val logFiles = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log") && !file.name.contains("_rotated_")
            }
            
            logFiles?.forEach { file ->
                performTimeRotation(file)
            }
            
            updateRotationState(RotationType.BATCH, "all_logs")
            
        } catch (e: Exception) {
            Timber.e(e, "批量轮转日志文件失败")
        }
    }
    
    /**
     * 检查是否需要大小轮转
     */
    private fun needsSizeRotation(currentSize: Long): Boolean {
        return currentSize > MAX_FILE_SIZE_MB * 1024 * 1024
    }
    
    /**
     * 检查是否需要时间轮转
     */
    private fun needsTimeRotation(file: File): Boolean {
        val now = System.currentTimeMillis()
        val fileDate = Date(file.lastModified())
        val currentDate = Date(now)
        
        // 检查是否跨天
        val fileDayString = dayFormat.format(fileDate)
        val currentDayString = dayFormat.format(currentDate)
        
        if (fileDayString != currentDayString) {
            return true
        }
        
        // 检查是否到达每日轮转时间
        val calendar = Calendar.getInstance()
        calendar.time = currentDate
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        
        val lastDailyRotation = lastRotationTimes[RotationType.TIME] ?: 0
        val timeSinceLastRotation = now - lastDailyRotation
        
        return currentHour == DAILY_ROTATION_HOUR && timeSinceLastRotation > 23 * 60 * 60 * 1000L
    }
    
    /**
     * 执行大小轮转
     */
    private suspend fun performSizeRotation(file: File) = withContext(Dispatchers.IO) {
        try {
            val timestamp = dateFormat.format(Date())
            val baseName = file.nameWithoutExtension
            val extension = file.extension
            val rotatedName = "${baseName}_size_rotated_$timestamp.$extension"
            val rotatedFile = File(file.parent, rotatedName)
            
            if (file.renameTo(rotatedFile)) {
                Timber.d("大小轮转完成: ${file.name} -> ${rotatedFile.name}")
                
                // 异步压缩
                scope.launch {
                    logCompressionManager.compressLogFile(rotatedFile)
                }
                
                // 清理旧文件
                cleanupOldRotatedFiles(baseName, RotationType.SIZE)
                
                // 更新状态
                lastRotationTimes[RotationType.SIZE] = System.currentTimeMillis()
                
            } else {
                Timber.e("大小轮转失败: ${file.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "执行大小轮转失败: ${file.name}")
        }
    }
    
    /**
     * 执行时间轮转
     */
    private suspend fun performTimeRotation(file: File) = withContext(Dispatchers.IO) {
        try {
            val timestamp = dateFormat.format(Date())
            val baseName = file.nameWithoutExtension
            val extension = file.extension
            val rotatedName = "${baseName}_time_rotated_$timestamp.$extension"
            val rotatedFile = File(archiveDir, rotatedName)
            
            if (file.renameTo(rotatedFile)) {
                Timber.d("时间轮转完成: ${file.name} -> ${rotatedFile.name}")
                
                // 异步压缩
                scope.launch {
                    logCompressionManager.compressLogFile(rotatedFile)
                }
                
                // 清理旧文件
                cleanupOldRotatedFiles(baseName, RotationType.TIME)
                
                // 更新状态
                lastRotationTimes[RotationType.TIME] = System.currentTimeMillis()
                
            } else {
                Timber.e("时间轮转失败: ${file.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "执行时间轮转失败: ${file.name}")
        }
    }
    
    /**
     * 执行手动轮转
     */
    private suspend fun performManualRotation(file: File) = withContext(Dispatchers.IO) {
        try {
            val timestamp = dateFormat.format(Date())
            val baseName = file.nameWithoutExtension
            val extension = file.extension
            val rotatedName = "${baseName}_manual_rotated_$timestamp.$extension"
            val rotatedFile = File(archiveDir, rotatedName)
            
            if (file.renameTo(rotatedFile)) {
                Timber.d("手动轮转完成: ${file.name} -> ${rotatedFile.name}")
                
                // 异步压缩
                scope.launch {
                    logCompressionManager.compressLogFile(rotatedFile)
                }
                
                // 更新状态
                lastRotationTimes[RotationType.MANUAL] = System.currentTimeMillis()
                
            } else {
                Timber.e("手动轮转失败: ${file.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "执行手动轮转失败: ${file.name}")
        }
    }
    
    /**
     * 清理旧的轮转文件
     */
    private suspend fun cleanupOldRotatedFiles(baseName: String, rotationType: RotationType) = withContext(Dispatchers.IO) {
        try {
            val searchDir = if (rotationType == RotationType.SIZE) logDir else archiveDir
            val rotationPrefix = when (rotationType) {
                RotationType.SIZE -> "${baseName}_size_rotated_"
                RotationType.TIME -> "${baseName}_time_rotated_"
                else -> "${baseName}_rotated_"
            }
            
            val rotatedFiles = searchDir.listFiles { _, name ->
                name.startsWith(rotationPrefix)
            }?.sortedByDescending { it.lastModified() }
            
            // 保留最新的文件，删除超出限制的文件
            rotatedFiles?.drop(MAX_FILES_PER_LEVEL)?.forEach { file ->
                if (file.delete()) {
                    Timber.d("删除旧轮转文件: ${file.name}")
                } else {
                    Timber.w("删除旧轮转文件失败: ${file.name}")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "清理旧轮转文件失败: $baseName")
        }
    }
    
    /**
     * 启动轮转监控
     */
    private fun startRotationMonitor() {
        scope.launch {
            while (isActive) {
                try {
                    delay(ROTATION_CHECK_INTERVAL_MS)
                    performPeriodicCheck()
                } catch (e: Exception) {
                    Timber.e(e, "轮转监控任务失败")
                }
            }
        }
    }
    
    /**
     * 执行定期检查
     */
    private suspend fun performPeriodicCheck() = withContext(Dispatchers.IO) {
        try {
            // 检查所有活跃的日志文件
            val activeLogFiles = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log") && !file.name.contains("_rotated_")
            }
            
            activeLogFiles?.forEach { file ->
                val currentSize = file.length()
                checkRotationNeeded(file.absolutePath, currentSize)
            }
            
            // 检查是否需要每周轮转
            checkWeeklyRotation()
            
        } catch (e: Exception) {
            Timber.e(e, "定期轮转检查失败")
        }
    }
    
    /**
     * 检查每周轮转
     */
    private suspend fun checkWeeklyRotation() {
        val now = System.currentTimeMillis()
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = now
        
        val currentDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        val lastWeeklyRotation = lastRotationTimes[RotationType.WEEKLY] ?: 0
        val timeSinceLastRotation = now - lastWeeklyRotation
        
        // 如果是周一且距离上次周轮转超过6天
        if (currentDayOfWeek == WEEKLY_ROTATION_DAY && timeSinceLastRotation > 6 * 24 * 60 * 60 * 1000L) {
            rotateAllLogs()
            lastRotationTimes[RotationType.WEEKLY] = now
        }
    }
    
    /**
     * 获取轮转统计信息
     */
    suspend fun getRotationStatistics(): RotationStatistics = withContext(Dispatchers.IO) {
        try {
            val totalLogFiles = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log")
            }?.size ?: 0
            
            val totalArchiveFiles = archiveDir.listFiles { file ->
                file.isFile && (file.name.endsWith(".log") || file.name.endsWith(".gz"))
            }?.size ?: 0
            
            val totalLogSize = calculateDirectorySize(logDir)
            val totalArchiveSize = calculateDirectorySize(archiveDir)
            
            val rotationCounts = mutableMapOf<RotationType, Int>()
            
            // 统计各种类型的轮转文件数量
            archiveDir.listFiles()?.forEach { file ->
                when {
                    file.name.contains("_size_rotated_") -> {
                        rotationCounts[RotationType.SIZE] = rotationCounts.getOrDefault(RotationType.SIZE, 0) + 1
                    }
                    file.name.contains("_time_rotated_") -> {
                        rotationCounts[RotationType.TIME] = rotationCounts.getOrDefault(RotationType.TIME, 0) + 1
                    }
                    file.name.contains("_manual_rotated_") -> {
                        rotationCounts[RotationType.MANUAL] = rotationCounts.getOrDefault(RotationType.MANUAL, 0) + 1
                    }
                }
            }
            
            RotationStatistics(
                totalLogFiles = totalLogFiles,
                totalArchiveFiles = totalArchiveFiles,
                totalLogSize = totalLogSize,
                totalArchiveSize = totalArchiveSize,
                rotationCounts = rotationCounts,
                lastRotationTimes = lastRotationTimes.toMap()
            )
            
        } catch (e: Exception) {
            Timber.e(e, "获取轮转统计信息失败")
            RotationStatistics(0, 0, 0, 0, emptyMap(), emptyMap())
        }
    }
    
    /**
     * 计算目录大小
     */
    private fun calculateDirectorySize(directory: File): Long {
        return try {
            directory.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * 更新轮转状态
     */
    private fun updateRotationState(rotationType: RotationType, fileName: String) {
        val currentState = _rotationState.value
        _rotationState.value = currentState.copy(
            lastRotationType = rotationType,
            lastRotationTime = System.currentTimeMillis(),
            lastRotatedFile = fileName,
            totalRotations = currentState.totalRotations + 1
        )
    }
    
    /**
     * 获取轮转配置
     */
    fun getRotationConfig(): RotationConfig {
        return RotationConfig(
            maxFileSizeMB = MAX_FILE_SIZE_MB,
            maxFilesPerLevel = MAX_FILES_PER_LEVEL,
            dailyRotationHour = DAILY_ROTATION_HOUR,
            weeklyRotationDay = WEEKLY_ROTATION_DAY,
            rotationCheckIntervalMs = ROTATION_CHECK_INTERVAL_MS
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        fileSizes.clear()
        lastRotationTimes.clear()
        Timber.d("日志轮转管理器已清理")
    }
}

/**
 * 轮转类型枚举
 */
enum class RotationType {
    SIZE,       // 基于大小的轮转
    TIME,       // 基于时间的轮转
    MANUAL,     // 手动轮转
    BATCH,      // 批量轮转
    WEEKLY      // 每周轮转
}

/**
 * 轮转状态数据类
 */
data class RotationState(
    val lastRotationType: RotationType? = null,
    val lastRotationTime: Long = 0,
    val lastRotatedFile: String = "",
    val totalRotations: Long = 0
)

/**
 * 轮转统计数据类
 */
data class RotationStatistics(
    val totalLogFiles: Int,
    val totalArchiveFiles: Int,
    val totalLogSize: Long,
    val totalArchiveSize: Long,
    val rotationCounts: Map<RotationType, Int>,
    val lastRotationTimes: Map<RotationType, Long>
)

/**
 * 轮转配置数据类
 */
data class RotationConfig(
    val maxFileSizeMB: Int,
    val maxFilesPerLevel: Int,
    val dailyRotationHour: Int,
    val weeklyRotationDay: Int,
    val rotationCheckIntervalMs: Long
)
