package com.cadence.domain.model.learning

import com.cadence.domain.model.Language

/**
 * 单词领域模型
 * 表示学习系统中的单词实体
 */
data class Word(
    val id: String,
    val text: String,
    val language: Language,
    val pronunciation: String? = null,
    val definition: String,
    val example: String? = null,
    val translation: String,
    val translationLanguage: Language,
    val difficulty: WordDifficulty = WordDifficulty.MEDIUM,
    val category: WordCategory = WordCategory.GENERAL,
    val tags: List<String> = emptyList(),
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 单词难度等级
 */
enum class WordDifficulty(val displayName: String, val level: Int) {
    EASY("简单", 1),
    MEDIUM("中等", 2),
    HARD("困难", 3),
    EXPERT("专家", 4)
}

/**
 * 单词分类
 */
enum class WordCategory(val displayName: String) {
    GENERAL("通用"),
    BUSINESS("商务"),
    ACADEMIC("学术"),
    DAILY("日常"),
    TECHNICAL("技术"),
    MEDICAL("医学"),
    LEGAL("法律"),
    TRAVEL("旅行"),
    FOOD("美食"),
    CULTURE("文化")
}