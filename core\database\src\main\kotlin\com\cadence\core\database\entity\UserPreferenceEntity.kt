package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 用户偏好设置数据实体
 * 存储用户的个性化设置
 */
@Entity(tableName = "user_preferences")
@Serializable
data class UserPreferenceEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val userId: String = "default_user",

    @ColumnInfo(name = "default_source_language_code")
    val defaultSourceLanguageCode: String = "zh",

    @ColumnInfo(name = "default_source_language_name")
    val defaultSourceLanguageName: String = "中文",

    @ColumnInfo(name = "default_source_region_code")
    val defaultSourceRegionCode: String? = "CN",

    @ColumnInfo(name = "default_source_region_name")
    val defaultSourceRegionName: String? = "大陆",

    @ColumnInfo(name = "default_source_dialect_name")
    val defaultSourceDialectName: String? = "普通话",

    @ColumnInfo(name = "default_target_language_code")
    val defaultTargetLanguageCode: String = "en",

    @ColumnInfo(name = "default_target_language_name")
    val defaultTargetLanguageName: String = "English",

    @ColumnInfo(name = "default_target_region_code")
    val defaultTargetRegionCode: String? = "US",

    @ColumnInfo(name = "default_target_region_name")
    val defaultTargetRegionName: String? = "美国",

    @ColumnInfo(name = "default_target_dialect_name")
    val defaultTargetDialectName: String? = "美式英语",
    
    @ColumnInfo(name = "auto_detect_language")
    val autoDetectLanguage: Boolean = true,
    
    @ColumnInfo(name = "save_translation_history")
    val saveTranslationHistory: Boolean = true,
    
    @ColumnInfo(name = "enable_cultural_context")
    val enableCulturalContext: Boolean = true,
    
    @ColumnInfo(name = "theme_mode")
    val themeMode: String = "system", // light, dark, system
    
    @ColumnInfo(name = "font_size")
    val fontSize: String = "medium", // small, medium, large

    @ColumnInfo(name = "app_language")
    val appLanguage: String = "system", // system, zh, en, ja, ko

    @ColumnInfo(name = "theme_color")
    val themeColor: String = "default", // default, classic_blue, warm_orange, nature_green, elegant_purple, modern_teal, vibrant_pink

    @ColumnInfo(name = "enable_dynamic_color")
    val enableDynamicColor: Boolean = true,

    @ColumnInfo(name = "enable_voice_input")
    val enableVoiceInput: Boolean = true,
    
    @ColumnInfo(name = "enable_voice_output")
    val enableVoiceOutput: Boolean = true,
    
    @ColumnInfo(name = "enable_ocr")
    val enableOcr: Boolean = true,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)
