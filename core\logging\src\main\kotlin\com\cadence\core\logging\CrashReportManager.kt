package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 崩溃报告管理器
 * 负责生成、存储和上报崩溃报告
 */
@Singleton
class CrashReportManager @Inject constructor(
    private val context: Context,
    private val structuredLogger: StructuredLogger
) {
    
    companion object {
        private const val CRASH_REPORTS_DIR = "crash_reports"
        private const val MAX_CRASH_REPORTS = 100
        private const val CRASH_REPORT_RETENTION_DAYS = 30
        private const val MAX_REPORT_SIZE_MB = 10
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val json = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    // 崩溃报告目录
    private val crashReportsDir: File by lazy {
        File(context.filesDir, CRASH_REPORTS_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    /**
     * 生成崩溃报告
     */
    suspend fun generateCrashReport(exceptionReport: ExceptionReport) = withContext(Dispatchers.IO) {
        try {
            val crashReport = createCrashReport(exceptionReport)
            val reportFile = saveCrashReport(crashReport)
            
            structuredLogger.error(
                message = "Crash report generated",
                context = mapOf(
                    "crash_id" to crashReport.id,
                    "exception_id" to exceptionReport.id,
                    "report_file" to reportFile.name,
                    "severity" to exceptionReport.classification.severity.name
                ),
                exception = exceptionReport.exception
            )
            
            // 清理旧的崩溃报告
            cleanupOldReports()
            
            // 如果是严重崩溃，尝试立即上报
            if (exceptionReport.classification.severity == ErrorSeverity.CRITICAL) {
                uploadCrashReport(crashReport)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "生成崩溃报告失败")
        }
    }
    
    /**
     * 创建崩溃报告
     */
    private fun createCrashReport(exceptionReport: ExceptionReport): CrashReport {
        return CrashReport(
            id = generateCrashId(),
            timestamp = System.currentTimeMillis(),
            exceptionId = exceptionReport.id,
            appInfo = collectAppInfo(),
            deviceInfo = exceptionReport.deviceInfo,
            exceptionInfo = ExceptionInfo(
                type = exceptionReport.exception.javaClass.name,
                message = exceptionReport.exception.message ?: "No message",
                stackTrace = exceptionReport.stackTrace,
                cause = exceptionReport.exception.cause?.let { cause ->
                    ExceptionInfo(
                        type = cause.javaClass.name,
                        message = cause.message ?: "No message",
                        stackTrace = getStackTrace(cause)
                    )
                }
            ),
            classification = exceptionReport.classification,
            context = CrashContext(
                threadName = exceptionReport.threadName,
                isFatal = exceptionReport.isFatal,
                context = exceptionReport.context,
                additionalData = exceptionReport.additionalData
            ),
            systemInfo = collectSystemInfo(),
            userActions = collectRecentUserActions(),
            memoryInfo = collectMemoryInfo(),
            networkInfo = collectNetworkInfo()
        )
    }
    
    /**
     * 保存崩溃报告
     */
    private suspend fun saveCrashReport(crashReport: CrashReport): File = withContext(Dispatchers.IO) {
        val fileName = "crash_${crashReport.id}_${dateFormat.format(Date(crashReport.timestamp))}.json"
        val reportFile = File(crashReportsDir, fileName)
        
        try {
            val reportJson = json.encodeToString(crashReport)
            
            // 检查报告大小
            if (reportJson.length > MAX_REPORT_SIZE_MB * 1024 * 1024) {
                Timber.w("崩溃报告过大，进行压缩")
                val compressedReport = compressCrashReport(crashReport)
                val compressedJson = json.encodeToString(compressedReport)
                reportFile.writeText(compressedJson)
            } else {
                reportFile.writeText(reportJson)
            }
            
            Timber.d("崩溃报告已保存: ${reportFile.name}")
            reportFile
        } catch (e: Exception) {
            Timber.e(e, "保存崩溃报告失败")
            throw e
        }
    }
    
    /**
     * 压缩崩溃报告
     */
    private fun compressCrashReport(crashReport: CrashReport): CrashReport {
        return crashReport.copy(
            exceptionInfo = crashReport.exceptionInfo.copy(
                stackTrace = truncateStackTrace(crashReport.exceptionInfo.stackTrace, 100)
            ),
            userActions = crashReport.userActions.takeLast(10), // 只保留最近10个用户操作
            additionalData = emptyMap() // 移除额外数据以减小大小
        )
    }
    
    /**
     * 截断堆栈跟踪
     */
    private fun truncateStackTrace(stackTrace: String, maxLines: Int): String {
        val lines = stackTrace.split("\n")
        return if (lines.size > maxLines) {
            lines.take(maxLines).joinToString("\n") + "\n... (truncated ${lines.size - maxLines} lines)"
        } else {
            stackTrace
        }
    }
    
    /**
     * 上传崩溃报告
     */
    private suspend fun uploadCrashReport(crashReport: CrashReport) = withContext(Dispatchers.IO) {
        try {
            // 这里应该实现实际的上传逻辑
            // 例如：发送到Firebase Crashlytics、Bugsnag或自定义服务器
            
            Timber.d("崩溃报告上传成功: ${crashReport.id}")
            
            structuredLogger.info(
                message = "Crash report uploaded",
                context = mapOf(
                    "crash_id" to crashReport.id,
                    "upload_timestamp" to System.currentTimeMillis().toString()
                )
            )
        } catch (e: Exception) {
            Timber.e(e, "崩溃报告上传失败: ${crashReport.id}")
        }
    }
    
    /**
     * 清理旧的崩溃报告
     */
    private suspend fun cleanupOldReports() = withContext(Dispatchers.IO) {
        try {
            val reportFiles = crashReportsDir.listFiles()?.sortedByDescending { it.lastModified() }
            
            // 删除超过数量限制的文件
            reportFiles?.drop(MAX_CRASH_REPORTS)?.forEach { file ->
                file.delete()
                Timber.d("删除旧崩溃报告: ${file.name}")
            }
            
            // 删除超过保留期的文件
            val retentionTime = System.currentTimeMillis() - (CRASH_REPORT_RETENTION_DAYS * 24 * 60 * 60 * 1000L)
            reportFiles?.filter { it.lastModified() < retentionTime }?.forEach { file ->
                file.delete()
                Timber.d("删除过期崩溃报告: ${file.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "清理旧崩溃报告失败")
        }
    }
    
    /**
     * 生成崩溃ID
     */
    private fun generateCrashId(): String {
        return "CRASH_${System.currentTimeMillis()}_${UUID.randomUUID().toString().take(8)}"
    }
    
    /**
     * 收集应用信息
     */
    private fun collectAppInfo(): AppInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            AppInfo(
                packageName = context.packageName,
                versionName = packageInfo.versionName ?: "unknown",
                versionCode = packageInfo.longVersionCode,
                buildType = if (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE != 0) "debug" else "release",
                installTime = packageInfo.firstInstallTime,
                updateTime = packageInfo.lastUpdateTime
            )
        } catch (e: Exception) {
            AppInfo(
                packageName = context.packageName,
                versionName = "unknown",
                versionCode = 0,
                buildType = "unknown",
                installTime = 0,
                updateTime = 0
            )
        }
    }
    
    /**
     * 收集系统信息
     */
    private fun collectSystemInfo(): SystemInfo {
        return SystemInfo(
            osVersion = android.os.Build.VERSION.RELEASE,
            apiLevel = android.os.Build.VERSION.SDK_INT,
            kernelVersion = System.getProperty("os.version") ?: "unknown",
            locale = Locale.getDefault().toString(),
            timezone = TimeZone.getDefault().id,
            uptime = android.os.SystemClock.elapsedRealtime(),
            bootTime = System.currentTimeMillis() - android.os.SystemClock.elapsedRealtime()
        )
    }
    
    /**
     * 收集最近的用户操作
     */
    private fun collectRecentUserActions(): List<UserAction> {
        // 这里应该从用户操作跟踪器获取最近的操作
        // 目前返回空列表
        return emptyList()
    }
    
    /**
     * 收集内存信息
     */
    private fun collectMemoryInfo(): MemoryInfo {
        return try {
            val runtime = Runtime.getRuntime()
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val memInfo = android.app.ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memInfo)
            
            MemoryInfo(
                totalMemory = memInfo.totalMem,
                availableMemory = memInfo.availMem,
                usedMemory = memInfo.totalMem - memInfo.availMem,
                lowMemory = memInfo.lowMemory,
                threshold = memInfo.threshold,
                heapSize = runtime.totalMemory(),
                heapUsed = runtime.totalMemory() - runtime.freeMemory(),
                heapMax = runtime.maxMemory()
            )
        } catch (e: Exception) {
            MemoryInfo(0, 0, 0, false, 0, 0, 0, 0)
        }
    }
    
    /**
     * 收集网络信息
     */
    private fun collectNetworkInfo(): NetworkInfo {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            
            NetworkInfo(
                isConnected = activeNetwork?.isConnected ?: false,
                networkType = activeNetwork?.typeName ?: "unknown",
                networkSubtype = activeNetwork?.subtypeName ?: "unknown",
                isRoaming = activeNetwork?.isRoaming ?: false
            )
        } catch (e: Exception) {
            NetworkInfo(false, "unknown", "unknown", false)
        }
    }
    
    /**
     * 获取堆栈跟踪
     */
    private fun getStackTrace(exception: Throwable): String {
        return try {
            val stringWriter = java.io.StringWriter()
            val printWriter = java.io.PrintWriter(stringWriter)
            exception.printStackTrace(printWriter)
            stringWriter.toString()
        } catch (e: Exception) {
            "Unable to get stack trace"
        }
    }
    
    /**
     * 获取所有崩溃报告
     */
    suspend fun getAllCrashReports(): List<CrashReportSummary> = withContext(Dispatchers.IO) {
        try {
            crashReportsDir.listFiles()
                ?.filter { it.name.endsWith(".json") }
                ?.sortedByDescending { it.lastModified() }
                ?.mapNotNull { file ->
                    try {
                        val content = file.readText()
                        val crashReport = json.decodeFromString<CrashReport>(content)
                        CrashReportSummary(
                            id = crashReport.id,
                            timestamp = crashReport.timestamp,
                            exceptionType = crashReport.exceptionInfo.type,
                            message = crashReport.exceptionInfo.message,
                            severity = crashReport.classification.severity,
                            isFatal = crashReport.context.isFatal,
                            fileName = file.name
                        )
                    } catch (e: Exception) {
                        Timber.e(e, "解析崩溃报告失败: ${file.name}")
                        null
                    }
                } ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "获取崩溃报告列表失败")
            emptyList()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
    }
}

// 数据类定义
@Serializable
data class CrashReport(
    val id: String,
    val timestamp: Long,
    val exceptionId: String,
    val appInfo: AppInfo,
    val deviceInfo: DeviceInfo,
    val exceptionInfo: ExceptionInfo,
    val classification: ErrorClassification,
    val context: CrashContext,
    val systemInfo: SystemInfo,
    val userActions: List<UserAction>,
    val memoryInfo: MemoryInfo,
    val networkInfo: NetworkInfo,
    val additionalData: Map<String, String> = emptyMap()
)

@Serializable
data class AppInfo(
    val packageName: String,
    val versionName: String,
    val versionCode: Long,
    val buildType: String,
    val installTime: Long,
    val updateTime: Long
)

@Serializable
data class ExceptionInfo(
    val type: String,
    val message: String,
    val stackTrace: String,
    val cause: ExceptionInfo? = null
)

@Serializable
data class CrashContext(
    val threadName: String,
    val isFatal: Boolean,
    val context: String,
    val additionalData: Map<String, Any>
)

@Serializable
data class SystemInfo(
    val osVersion: String,
    val apiLevel: Int,
    val kernelVersion: String,
    val locale: String,
    val timezone: String,
    val uptime: Long,
    val bootTime: Long
)

@Serializable
data class UserAction(
    val timestamp: Long,
    val action: String,
    val target: String,
    val details: Map<String, String> = emptyMap()
)

@Serializable
data class MemoryInfo(
    val totalMemory: Long,
    val availableMemory: Long,
    val usedMemory: Long,
    val lowMemory: Boolean,
    val threshold: Long,
    val heapSize: Long,
    val heapUsed: Long,
    val heapMax: Long
)

@Serializable
data class NetworkInfo(
    val isConnected: Boolean,
    val networkType: String,
    val networkSubtype: String,
    val isRoaming: Boolean
)

data class CrashReportSummary(
    val id: String,
    val timestamp: Long,
    val exceptionType: String,
    val message: String,
    val severity: ErrorSeverity,
    val isFatal: Boolean,
    val fileName: String
)
