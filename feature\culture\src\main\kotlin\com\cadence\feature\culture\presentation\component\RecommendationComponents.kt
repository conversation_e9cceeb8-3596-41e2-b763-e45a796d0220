package com.cadence.feature.culture.presentation.component

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.culture.*

/**
 * 推荐卡片组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecommendationCard(
    recommendation: CulturalRecommendation,
    onClick: () -> Unit,
    onLike: () -> Unit,
    onBookmark: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = recommendation.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                // 类型标签
                TypeChip(type = recommendation.type)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述
            Text(
                text = recommendation.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标签
            if (recommendation.tags.isNotEmpty()) {
                TagRow(
                    tags = recommendation.tags.take(3),
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // 底部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 地区和难度信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.LocationOn,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = recommendation.region,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    DifficultyChip(
                        difficulty = recommendation.difficulty,
                        compact = true
                    )
                }
                
                // 操作按钮
                Row {
                    IconButton(
                        onClick = onLike,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ThumbUp,
                            contentDescription = "点赞",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    IconButton(
                        onClick = onBookmark,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.BookmarkBorder,
                            contentDescription = "收藏",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 热门推荐卡片（横向滚动用）
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PopularRecommendationCard(
    recommendation: CulturalRecommendation,
    onClick: () -> Unit,
    onLike: () -> Unit,
    onBookmark: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(280.dp)
            .clip(RoundedCornerShape(12.dp))
            .clickable { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 热门标识
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Surface(
                    shape = RoundedCornerShape(12.dp),
                    color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                ) {
                    Row(
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Whatshot,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(14.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "热门",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
                
                Text(
                    text = "${recommendation.popularity}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 标题
            Text(
                text = recommendation.title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 描述
            Text(
                text = recommendation.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 底部信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                TypeChip(type = recommendation.type, compact = true)
                
                Row {
                    IconButton(
                        onClick = onLike,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ThumbUp,
                            contentDescription = "点赞",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(14.dp)
                        )
                    }
                    
                    IconButton(
                        onClick = onBookmark,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.BookmarkBorder,
                            contentDescription = "收藏",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(14.dp)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 推荐筛选器组件
 */
@Composable
fun RecommendationFilters(
    selectedType: CulturalKnowledgeType?,
    selectedDifficulty: CulturalDifficulty?,
    selectedRegion: String?,
    onTypeChange: (CulturalKnowledgeType?) -> Unit,
    onDifficultyChange: (CulturalDifficulty?) -> Unit,
    onRegionChange: (String?) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 类型筛选
        Text(
            text = "知识类型",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            item {
                FilterChip(
                    selected = selectedType == null,
                    onClick = { onTypeChange(null) },
                    label = { Text("全部") }
                )
            }
            
            items(CulturalKnowledgeType.values()) { type ->
                FilterChip(
                    selected = selectedType == type,
                    onClick = { onTypeChange(type) },
                    label = { Text(getTypeDisplayName(type)) }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 难度筛选
        Text(
            text = "难度等级",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer(modifier = Modifier.height(8.dp))
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            item {
                FilterChip(
                    selected = selectedDifficulty == null,
                    onClick = { onDifficultyChange(null) },
                    label = { Text("全部") }
                )
            }
            
            items(CulturalDifficulty.values()) { difficulty ->
                FilterChip(
                    selected = selectedDifficulty == difficulty,
                    onClick = { onDifficultyChange(difficulty) },
                    label = { Text(getDifficultyText(difficulty)) }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 地区筛选
        Text(
            text = "地区",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer(modifier = Modifier.height(8.dp))
        
        val commonRegions = listOf("中国", "美国", "英国", "日本", "韩国", "法国", "德国")
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            item {
                FilterChip(
                    selected = selectedRegion == null,
                    onClick = { onRegionChange(null) },
                    label = { Text("全部") }
                )
            }
            
            items(commonRegions) { region ->
                FilterChip(
                    selected = selectedRegion == region,
                    onClick = { onRegionChange(region) },
                    label = { Text(region) }
                )
            }
        }
    }
}

/**
 * 类型标签组件
 */
@Composable
private fun TypeChip(
    type: CulturalKnowledgeType,
    compact: Boolean = false,
    modifier: Modifier = Modifier
) {
    val color = getTypeColor(type)
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(if (compact) 8.dp else 12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = getTypeDisplayName(type),
            style = if (compact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.labelMedium,
            color = color,
            modifier = Modifier.padding(
                horizontal = if (compact) 6.dp else 8.dp,
                vertical = if (compact) 2.dp else 4.dp
            )
        )
    }
}

/**
 * 难度标签组件
 */
@Composable
private fun DifficultyChip(
    difficulty: CulturalDifficulty,
    compact: Boolean = false,
    modifier: Modifier = Modifier
) {
    val (text, color) = when (difficulty) {
        CulturalDifficulty.BEGINNER -> "初级" to MaterialTheme.colorScheme.primary
        CulturalDifficulty.INTERMEDIATE -> "中级" to MaterialTheme.colorScheme.secondary
        CulturalDifficulty.ADVANCED -> "高级" to MaterialTheme.colorScheme.tertiary
        CulturalDifficulty.EXPERT -> "专家" to MaterialTheme.colorScheme.error
    }
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(if (compact) 8.dp else 12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            style = if (compact) MaterialTheme.typography.labelSmall else MaterialTheme.typography.labelMedium,
            color = color,
            modifier = Modifier.padding(
                horizontal = if (compact) 6.dp else 8.dp,
                vertical = if (compact) 2.dp else 4.dp
            )
        )
    }
}

/**
 * 标签行组件
 */
@Composable
private fun TagRow(
    tags: List<String>,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        items(tags) { tag ->
            Surface(
                shape = RoundedCornerShape(6.dp),
                color = MaterialTheme.colorScheme.surfaceVariant
            ) {
                Text(
                    text = tag,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }
    }
}

// 辅助函数

private fun getTypeDisplayName(type: CulturalKnowledgeType): String {
    return when (type) {
        CulturalKnowledgeType.IDIOM -> "习语"
        CulturalKnowledgeType.PROVERB -> "谚语"
        CulturalKnowledgeType.SLANG -> "俚语"
        CulturalKnowledgeType.ETIQUETTE -> "礼仪"
        CulturalKnowledgeType.TRADITION -> "传统"
        CulturalKnowledgeType.HISTORY -> "历史"
        CulturalKnowledgeType.RELIGION -> "宗教"
        CulturalKnowledgeType.FOOD -> "饮食"
        CulturalKnowledgeType.FESTIVAL -> "节日"
        CulturalKnowledgeType.ART -> "艺术"
        CulturalKnowledgeType.LITERATURE -> "文学"
        CulturalKnowledgeType.BUSINESS -> "商务"
        CulturalKnowledgeType.SOCIAL -> "社交"
        CulturalKnowledgeType.TABOO -> "禁忌"
        CulturalKnowledgeType.HUMOR -> "幽默"
    }
}

@Composable
private fun getTypeColor(type: CulturalKnowledgeType): androidx.compose.ui.graphics.Color {
    return when (type) {
        CulturalKnowledgeType.IDIOM -> MaterialTheme.colorScheme.primary
        CulturalKnowledgeType.PROVERB -> MaterialTheme.colorScheme.secondary
        CulturalKnowledgeType.SLANG -> MaterialTheme.colorScheme.tertiary
        CulturalKnowledgeType.ETIQUETTE -> MaterialTheme.colorScheme.error
        CulturalKnowledgeType.TRADITION -> MaterialTheme.colorScheme.primary
        CulturalKnowledgeType.HISTORY -> MaterialTheme.colorScheme.secondary
        CulturalKnowledgeType.RELIGION -> MaterialTheme.colorScheme.tertiary
        CulturalKnowledgeType.FOOD -> MaterialTheme.colorScheme.error
        CulturalKnowledgeType.FESTIVAL -> MaterialTheme.colorScheme.primary
        CulturalKnowledgeType.ART -> MaterialTheme.colorScheme.secondary
        CulturalKnowledgeType.LITERATURE -> MaterialTheme.colorScheme.tertiary
        CulturalKnowledgeType.BUSINESS -> MaterialTheme.colorScheme.error
        CulturalKnowledgeType.SOCIAL -> MaterialTheme.colorScheme.primary
        CulturalKnowledgeType.TABOO -> MaterialTheme.colorScheme.error
        CulturalKnowledgeType.HUMOR -> MaterialTheme.colorScheme.secondary
    }
}

private fun getDifficultyText(difficulty: CulturalDifficulty): String {
    return when (difficulty) {
        CulturalDifficulty.BEGINNER -> "初级"
        CulturalDifficulty.INTERMEDIATE -> "中级"
        CulturalDifficulty.ADVANCED -> "高级"
        CulturalDifficulty.EXPERT -> "专家"
    }
}