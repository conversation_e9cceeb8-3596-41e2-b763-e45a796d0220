package com.cadence.feature.offline

import kotlinx.serialization.Serializable

/**
 * 网络状态枚举
 */
enum class NetworkStatus {
    CONNECTED,      // 已连接
    DISCONNECTED,   // 已断开
    LIMITED,        // 连接受限
    UNKNOWN         // 未知状态
}

/**
 * 离线模式状态
 */
@Serializable
data class OfflineModeState(
    val isOffline: Boolean = false,
    val hasOfflineCapability: Boolean = false,
    val availableLanguagePairs: Int = 0,
    val offlineFeatures: List<Pair<String, Boolean>> = emptyList(),
    val lastOnlineTime: Long = System.currentTimeMillis()
) {
    val offlineDuration: Long
        get() = if (isOffline) {
            System.currentTimeMillis() - lastOnlineTime
        } else {
            0L
        }
    
    val isFullyOfflineCapable: Boolean
        get() = hasOfflineCapability && availableLanguagePairs > 0
    
    val enabledOfflineFeatures: List<String>
        get() = offlineFeatures.filter { it.second }.map { it.first }
    
    val disabledOfflineFeatures: List<String>
        get() = offlineFeatures.filter { !it.second }.map { it.first }
}

/**
 * 功能可用性状态
 */
@Serializable
data class FeatureAvailability(
    val onlineTranslation: Boolean = false,
    val offlineTranslation: Boolean = false,
    val languagePackDownload: Boolean = false,
    val dataSync: Boolean = false,
    val cloudBackup: Boolean = false,
    val realTimeCollaboration: Boolean = false,
    val voiceInput: Boolean = true,
    val textToSpeech: Boolean = true,
    val historyAccess: Boolean = true,
    val favoritesAccess: Boolean = true,
    val offlineLanguagePairs: List<String> = emptyList(),
    val limitedFeatures: List<String> = emptyList()
) {
    val availableFeatureCount: Int
        get() = listOf(
            onlineTranslation,
            offlineTranslation,
            languagePackDownload,
            dataSync,
            cloudBackup,
            realTimeCollaboration,
            voiceInput,
            textToSpeech,
            historyAccess,
            favoritesAccess
        ).count { it }
    
    val totalFeatureCount: Int = 10
    
    val availabilityPercentage: Float
        get() = (availableFeatureCount.toFloat() / totalFeatureCount) * 100f
    
    val hasBasicTranslationCapability: Boolean
        get() = onlineTranslation || offlineTranslation
    
    val hasOfflineCapability: Boolean
        get() = offlineTranslation && offlineLanguagePairs.isNotEmpty()
}

/**
 * 离线功能枚举
 */
enum class OfflineFeature {
    ONLINE_TRANSLATION,
    OFFLINE_TRANSLATION,
    LANGUAGE_PACK_DOWNLOAD,
    DATA_SYNC,
    CLOUD_BACKUP,
    REAL_TIME_COLLABORATION,
    VOICE_INPUT,
    TEXT_TO_SPEECH,
    HISTORY_ACCESS,
    FAVORITES_ACCESS
}

/**
 * 用户提示类型
 */
enum class PromptType {
    INFO,               // 信息提示
    WARNING,            // 警告提示
    ERROR,              // 错误提示
    OFFLINE_WARNING,    // 离线警告
    FEATURE_LIMITATION, // 功能限制提示
    SUGGESTION          // 建议提示
}

/**
 * 提示动作类型
 */
enum class PromptAction {
    DISMISS,                    // 关闭
    VIEW_OFFLINE_FEATURES,      // 查看离线功能
    DOWNLOAD_MODELS,            // 下载模型
    RETRY_CONNECTION,           // 重试连接
    ENABLE_OFFLINE_MODE         // 启用离线模式
}

/**
 * 提示优先级
 */
enum class PromptPriority {
    LOW,
    MEDIUM,
    HIGH,
    CRITICAL
}

/**
 * 离线提示
 */
@Serializable
data class OfflinePrompt(
    val id: String,
    val type: PromptType,
    val title: String,
    val message: String,
    val action: PromptAction,
    val priority: PromptPriority,
    val autoHide: Boolean = false,
    val hideDelay: Long = 5000L,
    val timestamp: Long = System.currentTimeMillis()
) {
    val isExpired: Boolean
        get() = autoHide && (System.currentTimeMillis() - timestamp) > hideDelay
    
    val ageInSeconds: Long
        get() = (System.currentTimeMillis() - timestamp) / 1000
}

/**
 * 建议优先级
 */
enum class SuggestionPriority {
    LOW,
    MEDIUM,
    HIGH
}

/**
 * 建议动作类型
 */
enum class SuggestionAction {
    DOWNLOAD_LANGUAGE_PACKS,
    VIEW_HISTORY,
    VIEW_FAVORITES,
    ENABLE_OFFLINE_MODE,
    CHECK_NETWORK_SETTINGS,
    CONTACT_SUPPORT
}

/**
 * 离线建议
 */
@Serializable
data class OfflineSuggestion(
    val id: String,
    val title: String,
    val description: String,
    val priority: SuggestionPriority,
    val action: SuggestionAction,
    val isActionable: Boolean = true,
    val timestamp: Long = System.currentTimeMillis()
) {
    val ageInMinutes: Long
        get() = (System.currentTimeMillis() - timestamp) / (1000 * 60)
}

/**
 * 离线模式配置
 */
@Serializable
data class OfflineModeConfig(
    val autoEnableOfflineMode: Boolean = true,
    val showOfflinePrompts: Boolean = true,
    val autoDownloadLanguagePacks: Boolean = false,
    val offlinePromptDelay: Long = 3000L,
    val maxOfflinePrompts: Int = 3,
    val enableNetworkMonitoring: Boolean = true,
    val networkCheckInterval: Long = 5000L,
    val retryConnectionAttempts: Int = 3,
    val retryConnectionDelay: Long = 2000L
) {
    fun isValidConfig(): Boolean {
        return offlinePromptDelay >= 0 &&
               maxOfflinePrompts > 0 &&
               networkCheckInterval > 0 &&
               retryConnectionAttempts > 0 &&
               retryConnectionDelay > 0
    }
}

/**
 * 网络质量指标
 */
@Serializable
data class NetworkQuality(
    val status: NetworkStatus,
    val latency: Long = -1L,
    val bandwidth: Long = -1L,
    val stability: Float = 0f,
    val lastChecked: Long = System.currentTimeMillis()
) {
    val isGoodQuality: Boolean
        get() = status == NetworkStatus.CONNECTED && 
                latency in 0..500 && 
                stability > 0.8f
    
    val isAcceptableQuality: Boolean
        get() = status == NetworkStatus.CONNECTED && 
                latency in 0..1000 && 
                stability > 0.5f
    
    val qualityScore: Float
        get() = when {
            status != NetworkStatus.CONNECTED -> 0f
            isGoodQuality -> 1f
            isAcceptableQuality -> 0.7f
            else -> 0.3f
        }
}

/**
 * 离线使用统计
 */
@Serializable
data class OfflineUsageStats(
    val totalOfflineTime: Long = 0L,
    val offlineTranslationCount: Int = 0,
    val offlineSessionCount: Int = 0,
    val lastOfflineSession: Long = 0L,
    val averageOfflineSessionDuration: Long = 0L,
    val mostUsedOfflineFeatures: Map<String, Int> = emptyMap(),
    val offlineLanguagePairUsage: Map<String, Int> = emptyMap()
) {
    val hasOfflineUsage: Boolean
        get() = totalOfflineTime > 0 || offlineTranslationCount > 0
    
    val averageTranslationsPerSession: Float
        get() = if (offlineSessionCount > 0) {
            offlineTranslationCount.toFloat() / offlineSessionCount
        } else {
            0f
        }
    
    val mostUsedOfflineFeature: String?
        get() = mostUsedOfflineFeatures.maxByOrNull { it.value }?.key
    
    val mostUsedLanguagePair: String?
        get() = offlineLanguagePairUsage.maxByOrNull { it.value }?.key
}

/**
 * 离线模式事件
 */
sealed class OfflineModeEvent {
    object NetworkConnected : OfflineModeEvent()
    object NetworkDisconnected : OfflineModeEvent()
    object NetworkLimited : OfflineModeEvent()
    data class OfflineTranslationUsed(val languagePair: String) : OfflineModeEvent()
    data class LanguagePackDownloaded(val languageCode: String) : OfflineModeEvent()
    data class FeatureBlocked(val feature: OfflineFeature, val reason: String) : OfflineModeEvent()
    data class PromptShown(val promptId: String, val type: PromptType) : OfflineModeEvent()
    data class PromptDismissed(val promptId: String) : OfflineModeEvent()
    data class SuggestionAccepted(val suggestionId: String, val action: SuggestionAction) : OfflineModeEvent()
}

/**
 * 离线模式状态快照
 */
@Serializable
data class OfflineModeSnapshot(
    val timestamp: Long = System.currentTimeMillis(),
    val networkStatus: NetworkStatus,
    val offlineModeState: OfflineModeState,
    val featureAvailability: FeatureAvailability,
    val activePrompts: List<OfflinePrompt>,
    val usageStats: OfflineUsageStats,
    val config: OfflineModeConfig
) {
    val isHealthy: Boolean
        get() = networkStatus == NetworkStatus.CONNECTED || 
                (networkStatus == NetworkStatus.DISCONNECTED && offlineModeState.hasOfflineCapability)
    
    val criticalIssues: List<String>
        get() = mutableListOf<String>().apply {
            if (networkStatus == NetworkStatus.DISCONNECTED && !offlineModeState.hasOfflineCapability) {
                add("无网络连接且无离线翻译能力")
            }
            if (activePrompts.any { it.type == PromptType.ERROR }) {
                add("存在错误提示")
            }
            if (!featureAvailability.hasBasicTranslationCapability) {
                add("无基本翻译功能")
            }
        }
}
