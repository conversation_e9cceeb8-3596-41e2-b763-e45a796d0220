package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.TranslationCacheEntity
import kotlinx.coroutines.flow.Flow

/**
 * 翻译缓存数据访问对象
 * 提供翻译结果缓存的CRUD操作
 */
@Dao
interface TranslationCacheDao {
    
    /**
     * 插入缓存记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCache(cache: TranslationCacheEntity)
    
    /**
     * 批量插入缓存记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCaches(caches: List<TranslationCacheEntity>)
    
    /**
     * 更新缓存记录
     */
    @Update
    suspend fun updateCache(cache: TranslationCacheEntity)
    
    /**
     * 删除缓存记录
     */
    @Delete
    suspend fun deleteCache(cache: TranslationCacheEntity)
    
    /**
     * 根据缓存键获取缓存
     */
    @Query("SELECT * FROM translation_cache WHERE cache_key = :cacheKey")
    suspend fun getCacheByKey(cacheKey: String): TranslationCacheEntity?
    
    /**
     * 根据源文本和语言对查找缓存
     */
    @Query("""
        SELECT * FROM translation_cache
        WHERE source_text = :sourceText
        AND source_language_code = :sourceLanguage
        AND target_language_code = :targetLanguage
        AND (source_region_code = :sourceRegion OR source_region_code IS NULL)
        AND (target_region_code = :targetRegion OR target_region_code IS NULL)
        AND expires_at > :currentTime
        ORDER BY last_accessed_at DESC
        LIMIT 1
    """)
    suspend fun findCache(
        sourceText: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String?,
        targetRegion: String?,
        currentTime: Long
    ): TranslationCacheEntity?

    /**
     * 根据缓存键获取缓存的翻译（检查过期时间）
     */
    @Query("SELECT * FROM translation_cache WHERE cache_key = :cacheKey AND expires_at > :currentTime")
    suspend fun getCachedTranslation(cacheKey: String, currentTime: Long = System.currentTimeMillis()): TranslationCacheEntity?
    
    /**
     * 更新缓存访问信息
     */
    @Query("""
        UPDATE translation_cache
        SET access_count = access_count + 1,
            last_accessed_at = :accessTime,
            updated_at = :accessTime
        WHERE cache_key = :cacheKey
    """)
    suspend fun updateCacheAccess(cacheKey: String, accessTime: Long = System.currentTimeMillis())

    /**
     * 更新缓存收藏状态
     */
    @Query("""
        UPDATE translation_cache
        SET is_favorite = :isFavorite,
            cache_priority = CASE WHEN :isFavorite THEN 2 ELSE cache_priority END,
            expires_at = CASE WHEN :isFavorite THEN :favoriteExpiresAt ELSE expires_at END,
            updated_at = :currentTime
        WHERE cache_key = :cacheKey
    """)
    suspend fun updateCacheFavoriteStatus(
        cacheKey: String,
        isFavorite: Boolean,
        favoriteExpiresAt: Long = System.currentTimeMillis() + TranslationCacheEntity.FAVORITE_CACHE_DURATION_MS,
        currentTime: Long = System.currentTimeMillis()
    )

    /**
     * 更新缓存固定状态
     */
    @Query("""
        UPDATE translation_cache
        SET is_pinned = :isPinned,
            cache_priority = CASE WHEN :isPinned THEN 3 ELSE cache_priority END,
            updated_at = :currentTime
        WHERE cache_key = :cacheKey
    """)
    suspend fun updateCachePinnedStatus(
        cacheKey: String,
        isPinned: Boolean,
        currentTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 删除指定缓存
     */
    @Query("DELETE FROM translation_cache WHERE cache_key = :cacheKey")
    suspend fun deleteCache(cacheKey: String)

    /**
     * 删除过期的缓存
     */
    @Query("DELETE FROM translation_cache WHERE expires_at <= :currentTime AND is_pinned = 0")
    suspend fun deleteExpiredCaches(currentTime: Long = System.currentTimeMillis()): Int

    /**
     * 删除最少使用的缓存（LRU清理）
     */
    @Query("""
        DELETE FROM translation_cache
        WHERE id IN (
            SELECT id FROM translation_cache
            WHERE is_pinned = 0 AND is_favorite = 0
            ORDER BY
                cache_priority ASC,
                access_count ASC,
                last_accessed_at ASC
            LIMIT :count
        )
    """)
    suspend fun deleteLeastUsedCaches(count: Int): Int

    /**
     * 清空非固定缓存
     */
    @Query("DELETE FROM translation_cache WHERE is_pinned = 0")
    suspend fun clearNonPinnedCaches()
    
    /**
     * 获取缓存总数
     */
    @Query("SELECT COUNT(*) FROM translation_cache")
    suspend fun getCacheCount(): Int
    
    /**
     * 获取有效缓存总数（未过期）
     */
    @Query("SELECT COUNT(*) FROM translation_cache WHERE expires_at > :currentTime")
    suspend fun getValidCacheCount(currentTime: Long): Int
    
    /**
     * 清空所有缓存
     */
    @Query("DELETE FROM translation_cache")
    suspend fun clearAllCaches()
    
    /**
     * 获取缓存统计信息
     */
    @Query("""
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN expires_at > :currentTime THEN 1 END) as valid_count,
            COUNT(CASE WHEN is_favorite = 1 THEN 1 END) as favorite_count,
            COUNT(CASE WHEN is_pinned = 1 THEN 1 END) as pinned_count,
            SUM(cache_size_bytes) as total_size_bytes,
            AVG(access_count) as avg_access_count,
            MAX(access_count) as max_access_count
        FROM translation_cache
    """)
    suspend fun getCacheStatistics(currentTime: Long = System.currentTimeMillis()): CacheStatistics
    
    /**
     * 获取语言对的缓存数量
     */
    @Query("""
        SELECT source_language_code, target_language_code, COUNT(*) as count
        FROM translation_cache
        WHERE expires_at > :currentTime
        GROUP BY source_language_code, target_language_code
        ORDER BY count DESC
    """)
    suspend fun getCacheCountByLanguagePair(currentTime: Long = System.currentTimeMillis()): List<LanguagePairCacheCount>

    /**
     * 获取指定语言对的缓存
     */
    @Query("""
        SELECT * FROM translation_cache
        WHERE source_language_code = :sourceLanguage
        AND target_language_code = :targetLanguage
        AND expires_at > :currentTime
        ORDER BY last_accessed_at DESC
        LIMIT :limit
    """)
    suspend fun getCachesByLanguagePair(
        sourceLanguage: String,
        targetLanguage: String,
        limit: Int = 100,
        currentTime: Long = System.currentTimeMillis()
    ): List<TranslationCacheEntity>

    /**
     * 获取收藏的缓存
     */
    @Query("""
        SELECT * FROM translation_cache
        WHERE is_favorite = 1 AND expires_at > :currentTime
        ORDER BY last_accessed_at DESC
    """)
    suspend fun getFavoriteCaches(currentTime: Long = System.currentTimeMillis()): List<TranslationCacheEntity>

    /**
     * 获取固定的缓存
     */
    @Query("SELECT * FROM translation_cache WHERE is_pinned = 1 ORDER BY created_at DESC")
    suspend fun getPinnedCaches(): List<TranslationCacheEntity>

    /**
     * 搜索缓存
     */
    @Query("""
        SELECT * FROM translation_cache
        WHERE (source_text LIKE '%' || :query || '%' OR translated_text LIKE '%' || :query || '%')
        AND expires_at > :currentTime
        ORDER BY access_count DESC, last_accessed_at DESC
        LIMIT :limit
    """)
    suspend fun searchCaches(
        query: String,
        limit: Int = 50,
        currentTime: Long = System.currentTimeMillis()
    ): List<TranslationCacheEntity>

    /**
     * 监听缓存变化
     */
    @Query("SELECT * FROM translation_cache ORDER BY last_accessed_at DESC LIMIT 100")
    fun observeCaches(): Flow<List<TranslationCacheEntity>>

    /**
     * 监听收藏缓存变化
     */
    @Query("SELECT * FROM translation_cache WHERE is_favorite = 1 ORDER BY last_accessed_at DESC")
    fun observeFavoriteCaches(): Flow<List<TranslationCacheEntity>>
}

/**
 * 缓存统计信息数据类
 */
data class CacheStatistics(
    val totalCount: Int,
    val validCount: Int,
    val favoriteCount: Int,
    val pinnedCount: Int,
    val totalSizeBytes: Long,
    val avgAccessCount: Float,
    val maxAccessCount: Int
) {
    val expiredCount: Int get() = totalCount - validCount
    val hitRate: Float get() = if (totalCount > 0) validCount.toFloat() / totalCount else 0f
    val averageSizeBytes: Float get() = if (totalCount > 0) totalSizeBytes.toFloat() / totalCount else 0f
}

/**
 * 语言对缓存数量数据类
 */
data class LanguagePairCacheCount(
    val sourceLanguageCode: String,
    val targetLanguageCode: String,
    val count: Int
)
