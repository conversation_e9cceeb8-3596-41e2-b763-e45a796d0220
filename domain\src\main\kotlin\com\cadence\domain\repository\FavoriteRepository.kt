package com.cadence.domain.repository

import com.cadence.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 收藏功能数据访问接口
 * 定义收藏夹和收藏项的数据操作契约
 */
interface FavoriteRepository {
    
    // ========== 收藏夹管理 ==========
    
    /**
     * 获取所有收藏夹
     * @return 收藏夹列表的Flow
     */
    fun getAllFolders(): Flow<List<FavoriteFolder>>
    
    /**
     * 根据ID获取收藏夹
     * @param folderId 收藏夹ID
     * @return 收藏夹信息
     */
    suspend fun getFolderById(folderId: String): Result<FavoriteFolder?>
    
    /**
     * 创建收藏夹
     * @param folder 收藏夹信息
     * @return 创建结果
     */
    suspend fun createFolder(folder: FavoriteFolder): Result<String>
    
    /**
     * 更新收藏夹
     * @param folder 收藏夹信息
     * @return 更新结果
     */
    suspend fun updateFolder(folder: FavoriteFolder): Result<Unit>
    
    /**
     * 删除收藏夹
     * @param folderId 收藏夹ID
     * @param moveItemsToFolderId 将收藏项移动到指定收藏夹（可选）
     * @return 删除结果
     */
    suspend fun deleteFolder(folderId: String, moveItemsToFolderId: String? = null): Result<Unit>
    
    /**
     * 获取默认收藏夹
     * @return 默认收藏夹
     */
    suspend fun getDefaultFolder(): Result<FavoriteFolder>
    
    /**
     * 设置默认收藏夹
     * @param folderId 收藏夹ID
     * @return 设置结果
     */
    suspend fun setDefaultFolder(folderId: String): Result<Unit>
    
    // ========== 收藏项管理 ==========
    
    /**
     * 获取收藏夹中的所有项目
     * @param folderId 收藏夹ID
     * @return 收藏项列表的Flow
     */
    fun getFolderItems(folderId: String): Flow<List<FavoriteItem>>
    
    /**
     * 根据查询条件获取收藏项
     * @param query 查询条件
     * @return 收藏项列表的Flow
     */
    fun getFavoriteItems(query: FavoriteQuery): Flow<List<FavoriteItem>>
    
    /**
     * 根据ID获取收藏项
     * @param itemId 收藏项ID
     * @return 收藏项信息
     */
    suspend fun getFavoriteItemById(itemId: String): Result<FavoriteItem?>
    
    /**
     * 添加收藏项
     * @param item 收藏项信息
     * @return 添加结果
     */
    suspend fun addFavoriteItem(item: FavoriteItem): Result<String>
    
    /**
     * 更新收藏项
     * @param item 收藏项信息
     * @return 更新结果
     */
    suspend fun updateFavoriteItem(item: FavoriteItem): Result<Unit>
    
    /**
     * 移除收藏项
     * @param itemId 收藏项ID
     * @return 移除结果
     */
    suspend fun removeFavoriteItem(itemId: String): Result<Unit>
    
    /**
     * 移动收藏项到其他收藏夹
     * @param itemId 收藏项ID
     * @param targetFolderId 目标收藏夹ID
     * @return 移动结果
     */
    suspend fun moveFavoriteItem(itemId: String, targetFolderId: String): Result<Unit>
    
    /**
     * 批量操作收藏项
     * @param itemIds 收藏项ID列表
     * @param operation 操作类型
     * @param targetFolderId 目标收藏夹ID（移动操作时需要）
     * @return 操作结果
     */
    suspend fun batchOperateFavoriteItems(
        itemIds: List<String>,
        operation: FavoriteOperationType,
        targetFolderId: String? = null
    ): Result<Int>
    
    // ========== 翻译记录收藏状态管理 ==========
    
    /**
     * 检查翻译记录是否已收藏
     * @param translationId 翻译记录ID
     * @return 是否已收藏
     */
    suspend fun isTranslationFavorited(translationId: String): Result<Boolean>
    
    /**
     * 添加翻译记录到收藏夹
     * @param translationId 翻译记录ID
     * @param folderId 收藏夹ID（可选，默认使用默认收藏夹）
     * @param note 备注（可选）
     * @param tags 标签列表（可选）
     * @param priority 优先级（可选）
     * @return 添加结果
     */
    suspend fun addTranslationToFavorites(
        translationId: String,
        folderId: String? = null,
        note: String? = null,
        tags: List<String> = emptyList(),
        priority: FavoritePriority = FavoritePriority.NORMAL
    ): Result<String>
    
    /**
     * 从收藏夹移除翻译记录
     * @param translationId 翻译记录ID
     * @return 移除结果
     */
    suspend fun removeTranslationFromFavorites(translationId: String): Result<Unit>
    
    // ========== 搜索和筛选 ==========
    
    /**
     * 搜索收藏项
     * @param searchQuery 搜索关键词
     * @param folderId 限制在指定收藏夹内搜索（可选）
     * @return 搜索结果的Flow
     */
    fun searchFavoriteItems(searchQuery: String, folderId: String? = null): Flow<List<FavoriteItem>>
    
    /**
     * 根据标签获取收藏项
     * @param tags 标签列表
     * @return 收藏项列表的Flow
     */
    fun getFavoriteItemsByTags(tags: List<String>): Flow<List<FavoriteItem>>
    
    /**
     * 获取所有使用的标签
     * @return 标签列表的Flow
     */
    fun getAllUsedTags(): Flow<List<String>>
    
    /**
     * 获取搜索建议
     * @param query 查询关键词
     * @return 搜索建议列表
     */
    suspend fun getSearchSuggestions(query: String): Result<List<FavoriteSearchSuggestion>>
    
    // ========== 统计信息 ==========
    
    /**
     * 获取收藏夹统计信息
     * @return 统计信息
     */
    suspend fun getFavoriteStatistics(): Result<FavoriteStatistics>
    
    /**
     * 获取收藏夹使用统计
     * @param folderId 收藏夹ID
     * @return 使用统计信息
     */
    suspend fun getFolderUsageStats(folderId: String): Result<FavoriteUsageStats>
    
    // ========== 数据导入导出 ==========
    
    /**
     * 导出收藏夹数据
     * @param folderIds 要导出的收藏夹ID列表（空表示导出所有）
     * @return 导出数据
     */
    suspend fun exportFavoriteData(folderIds: List<String> = emptyList()): Result<FavoriteExportData>
    
    /**
     * 导入收藏夹数据
     * @param exportData 导入数据
     * @param mergeStrategy 合并策略（覆盖、跳过、合并）
     * @return 导入结果
     */
    suspend fun importFavoriteData(
        exportData: FavoriteExportData,
        mergeStrategy: String = "skip"
    ): Result<FavoriteImportResult>
    
    // ========== 配置管理 ==========
    
    /**
     * 获取收藏夹配置
     * @return 配置信息的Flow
     */
    fun getFavoriteConfig(): Flow<FavoriteConfig>
    
    /**
     * 更新收藏夹配置
     * @param config 配置信息
     * @return 更新结果
     */
    suspend fun updateFavoriteConfig(config: FavoriteConfig): Result<Unit>
    
    // ========== 同步功能 ==========
    
    /**
     * 获取同步状态
     * @return 同步信息列表的Flow
     */
    fun getSyncInfo(): Flow<List<FavoriteSyncInfo>>
    
    /**
     * 同步收藏夹数据
     * @return 同步结果
     */
    suspend fun syncFavoriteData(): Result<Unit>
    
    /**
     * 标记项目为待同步
     * @param itemId 项目ID
     * @param itemType 项目类型
     * @return 标记结果
     */
    suspend fun markForSync(itemId: String, itemType: String): Result<Unit>
}
