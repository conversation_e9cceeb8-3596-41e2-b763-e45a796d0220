package com.cadence.feature.offline

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.core.logging.StructuredLogger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 冲突解决界面ViewModel
 */
@HiltViewModel
class ConflictResolutionViewModel @Inject constructor(
    private val syncManager: OfflineDataSyncManager,
    private val structuredLogger: StructuredLogger
) : ViewModel() {
    
    // 冲突列表
    private val _conflicts = MutableStateFlow<List<ConflictLog>>(emptyList())
    val conflicts: StateFlow<List<ConflictLog>> = _conflicts.asStateFlow()
    
    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    // 错误状态
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // 操作状态
    private val _isResolving = MutableStateFlow(false)
    val isResolving: StateFlow<Boolean> = _isResolving.asStateFlow()
    
    init {
        loadConflicts()
    }
    
    /**
     * 加载冲突列表
     */
    fun loadConflicts() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val conflicts = syncManager.getConflicts()
                _conflicts.value = conflicts
                
                structuredLogger.logInfo(
                    message = "加载冲突列表",
                    context = mapOf(
                        "conflict_count" to conflicts.size.toString()
                    )
                )
                
                Timber.d("加载冲突列表完成，共${conflicts.size}个冲突")
                
            } catch (e: Exception) {
                Timber.e(e, "加载冲突列表失败")
                structuredLogger.logError(
                    message = "加载冲突列表失败",
                    error = e
                )
                
                _error.value = e.message ?: "加载冲突列表失败"
                
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 刷新冲突列表
     */
    fun refresh() {
        loadConflicts()
    }
    
    /**
     * 解决冲突
     */
    fun resolveConflict(conflictId: String, resolution: ConflictResolution) {
        viewModelScope.launch {
            try {
                _isResolving.value = true
                _error.value = null
                
                val success = syncManager.resolveConflict(conflictId, resolution)
                
                if (success) {
                    // 从列表中移除已解决的冲突
                    _conflicts.value = _conflicts.value.filter { it.id != conflictId }
                    
                    structuredLogger.logInfo(
                        message = "冲突解决成功",
                        context = mapOf(
                            "conflict_id" to conflictId,
                            "resolution" to resolution.name
                        )
                    )
                    
                    Timber.d("冲突解决成功: $conflictId, 解决方案: $resolution")
                    
                } else {
                    _error.value = "解决冲突失败"
                    
                    structuredLogger.logError(
                        message = "解决冲突失败",
                        context = mapOf(
                            "conflict_id" to conflictId,
                            "resolution" to resolution.name
                        )
                    )
                }
                
            } catch (e: Exception) {
                Timber.e(e, "解决冲突失败")
                structuredLogger.logError(
                    message = "解决冲突失败",
                    error = e,
                    context = mapOf(
                        "conflict_id" to conflictId,
                        "resolution" to resolution.name
                    )
                )
                
                _error.value = e.message ?: "解决冲突失败"
                
            } finally {
                _isResolving.value = false
            }
        }
    }
    
    /**
     * 批量解决冲突
     */
    fun resolveAllConflicts(resolution: ConflictResolution) {
        viewModelScope.launch {
            try {
                _isResolving.value = true
                _error.value = null
                
                val currentConflicts = _conflicts.value
                var successCount = 0
                var failureCount = 0
                
                for (conflict in currentConflicts) {
                    try {
                        val success = syncManager.resolveConflict(conflict.id, resolution)
                        if (success) {
                            successCount++
                        } else {
                            failureCount++
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "批量解决冲突失败: ${conflict.id}")
                        failureCount++
                    }
                }
                
                // 刷新冲突列表
                loadConflicts()
                
                structuredLogger.logInfo(
                    message = "批量解决冲突完成",
                    context = mapOf(
                        "total_conflicts" to currentConflicts.size.toString(),
                        "success_count" to successCount.toString(),
                        "failure_count" to failureCount.toString(),
                        "resolution" to resolution.name
                    )
                )
                
                if (failureCount > 0) {
                    _error.value = "部分冲突解决失败：成功${successCount}个，失败${failureCount}个"
                }
                
            } catch (e: Exception) {
                Timber.e(e, "批量解决冲突失败")
                structuredLogger.logError(
                    message = "批量解决冲突失败",
                    error = e,
                    context = mapOf("resolution" to resolution.name)
                )
                
                _error.value = e.message ?: "批量解决冲突失败"
                
            } finally {
                _isResolving.value = false
            }
        }
    }
    
    /**
     * 获取冲突统计信息
     */
    fun getConflictStatistics(): ConflictStatistics {
        val conflicts = _conflicts.value
        val dataTypeCounts = conflicts.groupBy { it.dataType }.mapValues { it.value.size }
        val oldestConflict = conflicts.minByOrNull { it.detectedTime }
        val newestConflict = conflicts.maxByOrNull { it.detectedTime }
        
        return ConflictStatistics(
            totalCount = conflicts.size,
            dataTypeCounts = dataTypeCounts,
            oldestConflictTime = oldestConflict?.detectedTime,
            newestConflictTime = newestConflict?.detectedTime
        )
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _error.value = null
    }
    
    /**
     * 获取冲突详情
     */
    fun getConflictDetails(conflictId: String): ConflictLog? {
        return _conflicts.value.find { it.id == conflictId }
    }
    
    /**
     * 检查是否有冲突
     */
    fun hasConflicts(): Boolean {
        return _conflicts.value.isNotEmpty()
    }
    
    /**
     * 获取按数据类型分组的冲突
     */
    fun getConflictsByDataType(): Map<SyncDataType, List<ConflictLog>> {
        return _conflicts.value.groupBy { it.dataType }
    }
    
    /**
     * 获取最近的冲突
     */
    fun getRecentConflicts(limit: Int = 5): List<ConflictLog> {
        return _conflicts.value
            .sortedByDescending { it.detectedTime }
            .take(limit)
    }
    
    /**
     * 搜索冲突
     */
    fun searchConflicts(query: String): List<ConflictLog> {
        if (query.isBlank()) return _conflicts.value
        
        return _conflicts.value.filter { conflict ->
            conflict.itemId.contains(query, ignoreCase = true) ||
            conflict.localData.contains(query, ignoreCase = true) ||
            conflict.serverData.contains(query, ignoreCase = true) ||
            getDataTypeDisplayName(conflict.dataType).contains(query, ignoreCase = true)
        }
    }
    
    /**
     * 获取数据类型显示名称
     */
    private fun getDataTypeDisplayName(dataType: SyncDataType): String {
        return when (dataType) {
            SyncDataType.TRANSLATION_HISTORY -> "翻译历史"
            SyncDataType.FAVORITES -> "收藏夹"
            SyncDataType.LEARNING_PROGRESS -> "学习进度"
            SyncDataType.USER_SETTINGS -> "用户设置"
            SyncDataType.CUSTOM_DICTIONARY -> "自定义词典"
        }
    }
}

/**
 * 冲突统计信息
 */
data class ConflictStatistics(
    val totalCount: Int,
    val dataTypeCounts: Map<SyncDataType, Int>,
    val oldestConflictTime: Long?,
    val newestConflictTime: Long?
) {
    val averageConflictsPerDataType: Double
        get() = if (dataTypeCounts.isNotEmpty()) {
            totalCount.toDouble() / dataTypeCounts.size
        } else {
            0.0
        }
    
    val mostConflictedDataType: SyncDataType?
        get() = dataTypeCounts.maxByOrNull { it.value }?.key
    
    val conflictTimeSpan: Long?
        get() = if (oldestConflictTime != null && newestConflictTime != null) {
            newestConflictTime - oldestConflictTime
        } else {
            null
        }
}
