package com.cadence.core.security.di

import android.content.Context
import com.cadence.core.security.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 安全模块的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object SecurityModule {
    
    /**
     * 提供加密管理器
     */
    @Provides
    @Singleton
    fun provideCryptoManager(
        @ApplicationContext context: Context
    ): CryptoManager {
        return CryptoManager(context)
    }
    
    /**
     * 提供安全存储管理器
     */
    @Provides
    @Singleton
    fun provideSecureStorageManager(
        @ApplicationContext context: Context,
        cryptoManager: CryptoManager
    ): SecureStorageManager {
        return SecureStorageManager(context, cryptoManager)
    }
    
    /**
     * 提供安全审计日志管理器
     */
    @Provides
    @Singleton
    fun provideSecurityAuditLogger(
        @ApplicationContext context: Context,
        cryptoManager: CryptoManager
    ): SecurityAuditLogger {
        return SecurityAuditLogger(context, cryptoManager)
    }
    
    /**
     * 提供网络安全管理器
     */
    @Provides
    @Singleton
    fun provideNetworkSecurityManager(
        @ApplicationContext context: Context,
        cryptoManager: CryptoManager
    ): NetworkSecurityManager {
        return NetworkSecurityManager(context, cryptoManager)
    }
    
    /**
     * 提供隐私管理器
     */
    @Provides
    @Singleton
    fun providePrivacyManager(
        @ApplicationContext context: Context,
        secureStorageManager: SecureStorageManager,
        auditLogger: SecurityAuditLogger
    ): PrivacyManager {
        return PrivacyManager(context, secureStorageManager, auditLogger)
    }
    
    /**
     * 提供安全的OkHttpClient
     */
    @Provides
    @Singleton
    @SecureHttpClient
    fun provideSecureOkHttpClient(
        networkSecurityManager: NetworkSecurityManager
    ): OkHttpClient {
        return networkSecurityManager.createSecureOkHttpClient()
    }
}

/**
 * 安全HTTP客户端限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SecureHttpClient
