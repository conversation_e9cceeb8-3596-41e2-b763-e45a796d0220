package com.cadence.ui.favorites.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.cadence.R
import com.cadence.domain.model.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * 收藏夹列表项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FolderListItem(
    folder: FavoriteFolder,
    isSelected: <PERSON><PERSON>an,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    
    Card(
        onClick = onClick,
        modifier = modifier.padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 4.dp else 1.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 收藏夹图标
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(CircleShape)
                    .background(Color(android.graphics.Color.parseColor(folder.color))),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getFolderIcon(folder.icon),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 收藏夹信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = folder.name,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    if (folder.isDefault) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = stringResource(R.string.default_folder),
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
                
                folder.description?.let { description ->
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 更多操作按钮
            Box {
                IconButton(
                    onClick = { showMenu = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = stringResource(R.string.more_options)
                    )
                }
                
                DropdownMenu(
                    expanded = showMenu,
                    onDismissRequest = { showMenu = false }
                ) {
                    if (!folder.isDefault) {
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.delete_folder)) },
                            onClick = {
                                onDelete()
                                showMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = null
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 收藏项卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoriteItemCard(
    favoriteItem: FavoriteItem,
    translation: Translation?,
    onClick: () -> Unit,
    onRemove: () -> Unit,
    onMove: (String) -> Unit,
    folders: List<FavoriteFolder>,
    modifier: Modifier = Modifier
) {
    var showMenu by remember { mutableStateOf(false) }
    var showMoveDialog by remember { mutableStateOf(false) }
    
    Card(
        onClick = onClick,
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 翻译内容
            translation?.let { trans ->
                Text(
                    text = trans.sourceText,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = trans.translatedText,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 语言信息
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${trans.sourceLanguage} → ${trans.targetLanguage}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 优先级标识
                    if (favoriteItem.priority != FavoritePriority.NORMAL) {
                        PriorityChip(priority = favoriteItem.priority)
                    }
                }
            }
            
            // 备注
            favoriteItem.note?.let { note ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = note,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 标签
            if (favoriteItem.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                TagChips(
                    tags = favoriteItem.tags,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            // 底部信息和操作
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = formatDate(favoriteItem.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.weight(1f))
                
                // 更多操作
                Box {
                    IconButton(
                        onClick = { showMenu = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.MoreVert,
                            contentDescription = stringResource(R.string.more_options)
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showMenu,
                        onDismissRequest = { showMenu = false }
                    ) {
                        if (folders.isNotEmpty()) {
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.move_to_folder)) },
                                onClick = {
                                    showMoveDialog = true
                                    showMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.DriveFileMove,
                                        contentDescription = null
                                    )
                                }
                            )
                        }
                        
                        DropdownMenuItem(
                            text = { Text(stringResource(R.string.remove_from_favorites)) },
                            onClick = {
                                onRemove()
                                showMenu = false
                            },
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Delete,
                                    contentDescription = null
                                )
                            }
                        )
                    }
                }
            }
        }
    }
    
    // 移动到收藏夹对话框
    if (showMoveDialog) {
        MoveFavoriteDialog(
            folders = folders,
            onDismiss = { showMoveDialog = false },
            onConfirm = { folderId ->
                onMove(folderId)
                showMoveDialog = false
            }
        )
    }
}

/**
 * 优先级标识
 */
@Composable
fun PriorityChip(
    priority: FavoritePriority,
    modifier: Modifier = Modifier
) {
    val (color, text) = when (priority) {
        FavoritePriority.HIGH -> MaterialTheme.colorScheme.error to stringResource(R.string.priority_high)
        FavoritePriority.MEDIUM -> MaterialTheme.colorScheme.primary to stringResource(R.string.priority_medium)
        FavoritePriority.LOW -> MaterialTheme.colorScheme.outline to stringResource(R.string.priority_low)
        FavoritePriority.NORMAL -> MaterialTheme.colorScheme.outline to stringResource(R.string.priority_normal)
    }
    
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

/**
 * 标签组件
 */
@Composable
fun TagChips(
    tags: List<String>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        tags.take(3).forEach { tag ->
            Surface(
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.secondaryContainer
            ) {
                Text(
                    text = tag,
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }
        
        if (tags.size > 3) {
            Surface(
                shape = RoundedCornerShape(8.dp),
                color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
            ) {
                Text(
                    text = "+${tags.size - 3}",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                )
            }
        }
    }
}

/**
 * 获取收藏夹图标
 */
private fun getFolderIcon(iconName: String?): ImageVector {
    return when (iconName) {
        "star" -> Icons.Default.Star
        "work" -> Icons.Default.Work
        "school" -> Icons.Default.School
        "home" -> Icons.Default.Home
        "favorite" -> Icons.Default.Favorite
        else -> Icons.Default.Folder
    }
}

/**
 * 格式化日期
 */
private fun formatDate(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM/dd HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}
