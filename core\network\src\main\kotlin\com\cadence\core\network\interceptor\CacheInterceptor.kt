package com.cadence.core.network.interceptor

import com.cadence.core.network.monitor.NetworkMonitor
import okhttp3.CacheControl
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络缓存拦截器
 * 根据网络状态智能管理HTTP缓存策略
 */
@Singleton
class CacheInterceptor @Inject constructor(
    private val networkMonitor: NetworkMonitor
) : Interceptor {
    
    companion object {
        // 缓存时间配置（秒）
        private const val CACHE_MAX_AGE_ONLINE = 60 * 5      // 在线时缓存5分钟
        private const val CACHE_MAX_STALE_OFFLINE = 60 * 60 * 24 * 7  // 离线时使用7天内的缓存
        private const val CACHE_MAX_AGE_TRANSLATION = 60 * 60 * 24    // 翻译结果缓存1天
        private const val CACHE_MAX_AGE_STATIC = 60 * 60 * 24 * 30    // 静态资源缓存30天
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url.toString()
        
        // 根据网络状态调整请求
        val modifiedRequest = if (networkMonitor.isConnected()) {
            // 在线状态：使用正常缓存策略
            request.newBuilder()
                .cacheControl(getOnlineCacheControl(url))
                .build()
        } else {
            // 离线状态：强制使用缓存
            request.newBuilder()
                .cacheControl(getOfflineCacheControl())
                .build()
        }
        
        Timber.d("缓存策略 - URL: $url, 网络状态: ${if (networkMonitor.isConnected()) "在线" else "离线"}")
        
        val response = chain.proceed(modifiedRequest)
        
        // 根据响应类型设置缓存头
        return if (networkMonitor.isConnected()) {
            response.newBuilder()
                .header("Cache-Control", getResponseCacheControl(url))
                .removeHeader("Pragma") // 移除可能干扰缓存的头
                .build()
        } else {
            response
        }
    }
    
    /**
     * 获取在线状态的缓存控制
     */
    private fun getOnlineCacheControl(url: String): CacheControl {
        return when {
            isTranslationRequest(url) -> {
                // 翻译请求：优先使用缓存，但允许网络验证
                CacheControl.Builder()
                    .maxAge(CACHE_MAX_AGE_TRANSLATION, TimeUnit.SECONDS)
                    .build()
            }
            isStaticResource(url) -> {
                // 静态资源：长时间缓存
                CacheControl.Builder()
                    .maxAge(CACHE_MAX_AGE_STATIC, TimeUnit.SECONDS)
                    .build()
            }
            else -> {
                // 其他请求：短时间缓存
                CacheControl.Builder()
                    .maxAge(CACHE_MAX_AGE_ONLINE, TimeUnit.SECONDS)
                    .build()
            }
        }
    }
    
    /**
     * 获取离线状态的缓存控制
     */
    private fun getOfflineCacheControl(): CacheControl {
        return CacheControl.Builder()
            .onlyIfCached()
            .maxStale(CACHE_MAX_STALE_OFFLINE, TimeUnit.SECONDS)
            .build()
    }
    
    /**
     * 获取响应缓存控制头
     */
    private fun getResponseCacheControl(url: String): String {
        return when {
            isTranslationRequest(url) -> {
                "public, max-age=$CACHE_MAX_AGE_TRANSLATION"
            }
            isStaticResource(url) -> {
                "public, max-age=$CACHE_MAX_AGE_STATIC, immutable"
            }
            else -> {
                "public, max-age=$CACHE_MAX_AGE_ONLINE"
            }
        }
    }
    
    /**
     * 判断是否为翻译请求
     */
    private fun isTranslationRequest(url: String): Boolean {
        return url.contains("generateContent") || 
               url.contains("translate") ||
               url.contains("translation")
    }
    
    /**
     * 判断是否为静态资源
     */
    private fun isStaticResource(url: String): Boolean {
        return url.contains("models") ||
               url.contains("config") ||
               url.contains("static") ||
               url.endsWith(".json") ||
               url.endsWith(".xml")
    }
}

/**
 * 离线缓存拦截器
 * 专门处理离线模式下的缓存逻辑
 */
@Singleton
class OfflineCacheInterceptor @Inject constructor(
    private val networkMonitor: NetworkMonitor
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        
        // 如果没有网络连接，强制使用缓存
        if (!networkMonitor.isConnected()) {
            val cacheControl = CacheControl.Builder()
                .onlyIfCached()
                .maxStale(7, TimeUnit.DAYS) // 最多使用7天前的缓存
                .build()
            
            request = request.newBuilder()
                .cacheControl(cacheControl)
                .build()
            
            Timber.d("离线模式：强制使用缓存 - ${request.url}")
        }
        
        return chain.proceed(request)
    }
}

/**
 * 缓存策略配置
 */
data class CacheConfig(
    val maxAgeOnline: Int = 300,        // 在线缓存时间（秒）
    val maxStaleOffline: Int = 604800,  // 离线缓存时间（秒）
    val maxCacheSize: Long = 50 * 1024 * 1024, // 最大缓存大小（字节）
    val enableOfflineCache: Boolean = true,     // 是否启用离线缓存
    val enableResponseCache: Boolean = true     // 是否启用响应缓存
)

/**
 * 缓存策略枚举
 */
enum class CacheStrategy {
    /**
     * 网络优先：优先从网络获取，失败时使用缓存
     */
    NETWORK_FIRST,
    
    /**
     * 缓存优先：优先使用缓存，缓存不存在时从网络获取
     */
    CACHE_FIRST,
    
    /**
     * 仅网络：只从网络获取，不使用缓存
     */
    NETWORK_ONLY,
    
    /**
     * 仅缓存：只使用缓存，不访问网络
     */
    CACHE_ONLY,
    
    /**
     * 智能策略：根据网络状态和内容类型自动选择
     */
    SMART
}

/**
 * 缓存策略扩展函数
 */
fun CacheStrategy.getDisplayName(): String = when (this) {
    CacheStrategy.NETWORK_FIRST -> "网络优先"
    CacheStrategy.CACHE_FIRST -> "缓存优先"
    CacheStrategy.NETWORK_ONLY -> "仅网络"
    CacheStrategy.CACHE_ONLY -> "仅缓存"
    CacheStrategy.SMART -> "智能策略"
}

fun CacheStrategy.getDescription(): String = when (this) {
    CacheStrategy.NETWORK_FIRST -> "优先从网络获取最新数据，网络不可用时使用缓存"
    CacheStrategy.CACHE_FIRST -> "优先使用本地缓存，缓存不存在时从网络获取"
    CacheStrategy.NETWORK_ONLY -> "总是从网络获取数据，不使用缓存"
    CacheStrategy.CACHE_ONLY -> "只使用本地缓存，不访问网络"
    CacheStrategy.SMART -> "根据网络状态和数据类型智能选择最佳策略"
}
