package com.cadence.feature.offline

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle

/**
 * 离线提示界面
 */
@Composable
fun OfflinePromptScreen(
    modifier: Modifier = Modifier,
    viewModel: OfflineStatusViewModel = hiltViewModel()
) {
    val userPrompts by viewModel.userPrompts.collectAsStateWithLifecycle()
    val networkStatus by viewModel.networkStatus.collectAsStateWithLifecycle()
    val offlineMode by viewModel.offlineMode.collectAsStateWithLifecycle()
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        if (userPrompts.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(
                    items = userPrompts,
                    key = { it.id }
                ) { prompt ->
                    AnimatedVisibility(
                        visible = true,
                        enter = slideInVertically() + fadeIn(),
                        exit = slideOutVertically() + fadeOut()
                    ) {
                        OfflinePromptCard(
                            prompt = prompt,
                            onDismiss = { viewModel.dismissPrompt(prompt.id) },
                            onAction = { action -> 
                                viewModel.handlePromptAction(prompt.id, action)
                            }
                        )
                    }
                }
                
                // 离线模式建议
                if (networkStatus == NetworkStatus.DISCONNECTED) {
                    item {
                        OfflineModeSuggestions(
                            suggestions = viewModel.getOfflineModeSuggestions(),
                            onSuggestionClick = { suggestion ->
                                // TODO: 处理建议点击
                            }
                        )
                    }
                }
            }
        } else {
            // 无提示状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Text(
                        text = "一切正常",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Text(
                        text = viewModel.getNetworkStatusDescription(),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

/**
 * 离线提示卡片
 */
@Composable
private fun OfflinePromptCard(
    prompt: OfflinePrompt,
    onDismiss: () -> Unit,
    onAction: (PromptAction) -> Unit,
    modifier: Modifier = Modifier
) {
    val promptInfo = getPromptInfo(prompt.type)
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = promptInfo.backgroundColor
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Icon(
                        imageVector = promptInfo.icon,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = promptInfo.iconColor
                    )
                    
                    Column {
                        Text(
                            text = prompt.title,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold,
                            color = promptInfo.textColor
                        )
                        
                        Text(
                            text = getPriorityText(prompt.priority),
                            style = MaterialTheme.typography.labelSmall,
                            color = promptInfo.textColor.copy(alpha = 0.7f)
                        )
                    }
                }
                
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        modifier = Modifier.size(16.dp),
                        tint = promptInfo.textColor.copy(alpha = 0.6f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 消息内容
            Text(
                text = prompt.message,
                style = MaterialTheme.typography.bodyMedium,
                color = promptInfo.textColor.copy(alpha = 0.9f)
            )
            
            // 动作按钮
            if (prompt.action != PromptAction.DISMISS) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Text("稍后处理")
                    }
                    
                    Button(
                        onClick = { onAction(prompt.action) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(getActionText(prompt.action))
                    }
                }
            }
            
            // 自动隐藏倒计时
            if (prompt.autoHide) {
                Spacer(modifier = Modifier.height(8.dp))
                
                LinearProgressIndicator(
                    progress = { 1f - (prompt.ageInSeconds.toFloat() / (prompt.hideDelay / 1000f)) },
                    modifier = Modifier.fillMaxWidth(),
                    color = promptInfo.iconColor.copy(alpha = 0.6f),
                    trackColor = promptInfo.iconColor.copy(alpha = 0.2f)
                )
            }
        }
    }
}

/**
 * 离线模式建议
 */
@Composable
private fun OfflineModeSuggestions(
    suggestions: List<OfflineSuggestion>,
    onSuggestionClick: (OfflineSuggestion) -> Unit,
    modifier: Modifier = Modifier
) {
    if (suggestions.isEmpty()) return
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = "离线模式建议",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            suggestions.forEach { suggestion ->
                SuggestionItem(
                    suggestion = suggestion,
                    onClick = { onSuggestionClick(suggestion) }
                )
                
                if (suggestion != suggestions.last()) {
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        }
    }
}

/**
 * 建议项目
 */
@Composable
private fun SuggestionItem(
    suggestion: OfflineSuggestion,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        onClick = if (suggestion.isActionable) onClick else { {} },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Icon(
                imageVector = getSuggestionIcon(suggestion.action),
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = getSuggestionColor(suggestion.priority)
            )
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = suggestion.title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = suggestion.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (suggestion.isActionable) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 获取提示信息
 */
private fun getPromptInfo(type: PromptType): PromptInfo {
    return when (type) {
        PromptType.INFO -> PromptInfo(
            icon = Icons.Default.Info,
            iconColor = Color(0xFF2196F3),
            backgroundColor = Color(0xFFE3F2FD),
            textColor = Color(0xFF1565C0)
        )
        PromptType.WARNING -> PromptInfo(
            icon = Icons.Default.Warning,
            iconColor = Color(0xFFFF9800),
            backgroundColor = Color(0xFFFFF3E0),
            textColor = Color(0xFFE65100)
        )
        PromptType.ERROR -> PromptInfo(
            icon = Icons.Default.Error,
            iconColor = Color(0xFFF44336),
            backgroundColor = Color(0xFFFFEBEE),
            textColor = Color(0xFFC62828)
        )
        PromptType.OFFLINE_WARNING -> PromptInfo(
            icon = Icons.Default.CloudOff,
            iconColor = Color(0xFFFF9800),
            backgroundColor = Color(0xFFFFF3E0),
            textColor = Color(0xFFE65100)
        )
        PromptType.FEATURE_LIMITATION -> PromptInfo(
            icon = Icons.Default.Block,
            iconColor = Color(0xFF9E9E9E),
            backgroundColor = Color(0xFFF5F5F5),
            textColor = Color(0xFF424242)
        )
        PromptType.SUGGESTION -> PromptInfo(
            icon = Icons.Default.Lightbulb,
            iconColor = Color(0xFF4CAF50),
            backgroundColor = Color(0xFFE8F5E8),
            textColor = Color(0xFF2E7D32)
        )
    }
}

/**
 * 获取优先级文本
 */
private fun getPriorityText(priority: PromptPriority): String {
    return when (priority) {
        PromptPriority.LOW -> "低优先级"
        PromptPriority.MEDIUM -> "中优先级"
        PromptPriority.HIGH -> "高优先级"
        PromptPriority.CRITICAL -> "紧急"
    }
}

/**
 * 获取动作文本
 */
private fun getActionText(action: PromptAction): String {
    return when (action) {
        PromptAction.DISMISS -> "关闭"
        PromptAction.VIEW_OFFLINE_FEATURES -> "查看离线功能"
        PromptAction.DOWNLOAD_MODELS -> "下载模型"
        PromptAction.RETRY_CONNECTION -> "重试连接"
        PromptAction.ENABLE_OFFLINE_MODE -> "启用离线模式"
    }
}

/**
 * 获取建议图标
 */
private fun getSuggestionIcon(action: SuggestionAction): ImageVector {
    return when (action) {
        SuggestionAction.DOWNLOAD_LANGUAGE_PACKS -> Icons.Default.Download
        SuggestionAction.VIEW_HISTORY -> Icons.Default.History
        SuggestionAction.VIEW_FAVORITES -> Icons.Default.Favorite
        SuggestionAction.ENABLE_OFFLINE_MODE -> Icons.Default.CloudOff
        SuggestionAction.CHECK_NETWORK_SETTINGS -> Icons.Default.Settings
        SuggestionAction.CONTACT_SUPPORT -> Icons.Default.Support
    }
}

/**
 * 获取建议颜色
 */
private fun getSuggestionColor(priority: SuggestionPriority): Color {
    return when (priority) {
        SuggestionPriority.LOW -> Color(0xFF9E9E9E)
        SuggestionPriority.MEDIUM -> Color(0xFF2196F3)
        SuggestionPriority.HIGH -> Color(0xFFFF9800)
    }
}

/**
 * 提示信息数据类
 */
private data class PromptInfo(
    val icon: ImageVector,
    val iconColor: Color,
    val backgroundColor: Color,
    val textColor: Color
)
