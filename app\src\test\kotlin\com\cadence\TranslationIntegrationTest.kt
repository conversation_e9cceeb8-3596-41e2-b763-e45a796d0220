package com.cadence

import com.cadence.domain.model.*
import com.cadence.domain.usecase.TranslateTextUseCase
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Assert.*
import org.mockito.kotlin.*

/**
 * 翻译功能集成测试
 * 验证核心翻译功能的完整流程
 */
class TranslationIntegrationTest {
    
    @Test
    fun `测试基本翻译功能`() = runTest {
        // 创建翻译请求
        val request = TranslationRequest(
            text = "Hello, world!",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文"),
            sourceRegion = Region("US", "美国"),
            targetRegion = Region("CN", "中国大陆"),
            includeCulturalContext = true,
            saveToHistory = true
        )
        
        // 验证请求参数
        assertEquals("Hello, world!", request.text)
        assertEquals("en", request.sourceLanguage.code)
        assertEquals("zh", request.targetLanguage.code)
        assertTrue(request.includeCulturalContext)
        assertTrue(request.saveToHistory)
    }
    
    @Test
    fun `测试缓存键生成`() {
        val request1 = TranslationRequest(
            text = "Hello",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文")
        )
        
        val request2 = TranslationRequest(
            text = "Hello",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文")
        )
        
        val request3 = TranslationRequest(
            text = "Hi",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文")
        )
        
        // 相同请求应该生成相同的缓存键
        // 不同请求应该生成不同的缓存键
        // 这里只是验证对象创建，实际缓存键生成在mapper中
        assertNotNull(request1)
        assertNotNull(request2)
        assertNotNull(request3)
    }
    
    @Test
    fun `测试翻译结果模型`() {
        val translation = Translation(
            id = "test-id",
            sourceText = "Hello",
            translatedText = "你好",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文"),
            confidenceScore = 0.95f,
            isFavorite = false,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            translationType = TranslationType.ONLINE,
            culturalContext = "这是一个常见的问候语",
            tags = emptyList(),
            notes = null
        )
        
        val result = TranslationResult(
            translation = translation,
            isFromCache = false,
            processingTimeMs = 1500L
        )
        
        // 验证翻译结果
        assertEquals("Hello", result.translation.sourceText)
        assertEquals("你好", result.translation.translatedText)
        assertEquals(0.95f, result.translation.confidenceScore, 0.01f)
        assertFalse(result.isFromCache)
        assertEquals(1500L, result.processingTimeMs)
    }
    
    @Test
    fun `测试语言检测模型`() {
        val detection = LanguageDetection(
            detectedLanguage = Language("en", "English"),
            confidence = 0.98f,
            isReliable = true,
            alternatives = listOf(
                LanguageAlternative(
                    language = Language("es", "Español"),
                    confidence = 0.15f
                )
            )
        )
        
        // 验证语言检测结果
        assertEquals("en", detection.detectedLanguage.code)
        assertEquals(0.98f, detection.confidence, 0.01f)
        assertTrue(detection.isReliable)
        assertEquals(1, detection.alternatives.size)
        assertEquals("es", detection.alternatives[0].language.code)
    }
    
    @Test
    fun `测试翻译历史查询`() {
        val query = TranslationHistoryQuery(
            languagePair = Pair(
                Language("en", "English"),
                Language("zh", "中文")
            ),
            searchQuery = "hello",
            favoritesOnly = false,
            translationType = TranslationType.ONLINE,
            limit = 20,
            offset = 0
        )
        
        // 验证查询参数
        assertNotNull(query.languagePair)
        assertEquals("en", query.languagePair?.first?.code)
        assertEquals("zh", query.languagePair?.second?.code)
        assertEquals("hello", query.searchQuery)
        assertFalse(query.favoritesOnly)
        assertEquals(TranslationType.ONLINE, query.translationType)
        assertEquals(20, query.limit)
        assertEquals(0, query.offset)
    }
    
    @Test
    fun `测试翻译类型枚举`() {
        // 验证所有翻译类型
        val types = TranslationType.values()
        assertTrue(types.contains(TranslationType.ONLINE))
        assertTrue(types.contains(TranslationType.OFFLINE))
        assertTrue(types.contains(TranslationType.HYBRID))
        
        // 验证枚举转换
        assertEquals(TranslationType.ONLINE, TranslationType.valueOf("ONLINE"))
        assertEquals(TranslationType.OFFLINE, TranslationType.valueOf("OFFLINE"))
        assertEquals(TranslationType.HYBRID, TranslationType.valueOf("HYBRID"))
    }
    
    @Test
    fun `测试区域模型`() {
        val region = Region(
            code = "CN",
            name = "中国大陆",
            dialectName = "普通话",
            culturalInfo = "中华文化背景"
        )
        
        // 验证区域信息
        assertEquals("CN", region.code)
        assertEquals("中国大陆", region.name)
        assertEquals("普通话", region.dialectName)
        assertEquals("中华文化背景", region.culturalInfo)
    }
    
    @Test
    fun `测试标签模型`() {
        val tag = Tag(
            id = "tag-1",
            name = "商务",
            color = "#FF5722",
            createdAt = System.currentTimeMillis()
        )
        
        // 验证标签信息
        assertEquals("tag-1", tag.id)
        assertEquals("商务", tag.name)
        assertEquals("#FF5722", tag.color)
        assertTrue(tag.createdAt > 0)
    }
    
    @Test
    fun `测试翻译请求验证`() {
        // 测试空文本
        val emptyTextRequest = TranslationRequest(
            text = "",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文")
        )
        assertEquals("", emptyTextRequest.text)
        
        // 测试长文本
        val longText = "A".repeat(10000)
        val longTextRequest = TranslationRequest(
            text = longText,
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文")
        )
        assertEquals(10000, longTextRequest.text.length)
        
        // 测试相同语言翻译
        val sameLanguageRequest = TranslationRequest(
            text = "Hello",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("en", "English")
        )
        assertEquals("en", sameLanguageRequest.sourceLanguage.code)
        assertEquals("en", sameLanguageRequest.targetLanguage.code)
    }
    
    @Test
    fun `测试翻译历史模型`() {
        val history = TranslationHistory(
            id = "history-1",
            sourceText = "Hello",
            translatedText = "你好",
            sourceLanguage = Language("en", "English"),
            targetLanguage = Language("zh", "中文"),
            confidence = 0.95f,
            translationType = TranslationType.ONLINE,
            isFavorite = true,
            createdAt = System.currentTimeMillis(),
            lastAccessedAt = System.currentTimeMillis(),
            accessCount = 5
        )
        
        // 验证历史记录
        assertEquals("history-1", history.id)
        assertEquals("Hello", history.sourceText)
        assertEquals("你好", history.translatedText)
        assertEquals(0.95f, history.confidence, 0.01f)
        assertEquals(TranslationType.ONLINE, history.translationType)
        assertTrue(history.isFavorite)
        assertEquals(5, history.accessCount)
    }
}
