package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.Word
import com.cadence.domain.model.learning.LearningProgress
import com.cadence.domain.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 单词详情ViewModel
 * 管理单词详情界面的状态和业务逻辑
 */
@HiltViewModel
class WordDetailViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(WordDetailUiState())
    val uiState: StateFlow<WordDetailUiState> = _uiState.asStateFlow()
    
    private val _word = MutableStateFlow<Word?>(null)
    val word: StateFlow<Word?> = _word.asStateFlow()
    
    private val _progress = MutableStateFlow<LearningProgress?>(null)
    val progress: StateFlow<LearningProgress?> = _progress.asStateFlow()
    
    private var currentWordId: String? = null
    
    /**
     * 加载单词详情
     */
    fun loadWordDetail(wordId: String) {
        currentWordId = wordId
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 加载单词信息
                val wordData = learningRepository.getWordById(wordId)
                _word.value = wordData
                
                // 加载学习进度
                val progressData = learningRepository.getLearningProgress("current_user", wordId)
                _progress.value = progressData
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载单词详情失败"
                )
            }
        }
    }
    
    /**
     * 切换收藏状态
     */
    fun toggleBookmark() {
        currentWordId?.let { wordId ->
            viewModelScope.launch {
                try {
                    learningRepository.toggleWordBookmark("current_user", wordId)
                    // 重新加载进度数据
                    val progressData = learningRepository.getLearningProgress("current_user", wordId)
                    _progress.value = progressData
                } catch (e: Exception) {
                    _uiState.value = _uiState.value.copy(
                        error = e.message ?: "切换收藏状态失败"
                    )
                }
            }
        }
    }
    
    /**
     * 开始学习单词
     */
    fun startStudy(studyMode: StudyMode) {
        currentWordId?.let { wordId ->
            viewModelScope.launch {
                try {
                    learningRepository.startStudySession(
                        userId = "current_user",
                        wordIds = listOf(wordId),
                        sessionType = studyMode.toSessionType()
                    )
                    // 可以在这里触发导航到学习界面
                } catch (e: Exception) {
                    _uiState.value = _uiState.value.copy(
                        error = e.message ?: "开始学习失败"
                    )
                }
            }
        }
    }
}

/**
 * 单词详情界面UI状态
 */
data class WordDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 学习模式
 */
enum class StudyMode(val displayName: String) {
    FLASHCARD("闪卡模式"),
    MULTIPLE_CHOICE("选择题"),
    TYPING("拼写练习"),
    LISTENING("听力练习")
}

/**
 * 将StudyMode转换为SessionType
 */
private fun StudyMode.toSessionType(): com.cadence.domain.model.learning.SessionType {
    return when (this) {
        StudyMode.FLASHCARD -> com.cadence.domain.model.learning.SessionType.REVIEW
        StudyMode.MULTIPLE_CHOICE -> com.cadence.domain.model.learning.SessionType.QUIZ
        StudyMode.TYPING -> com.cadence.domain.model.learning.SessionType.PRACTICE
        StudyMode.LISTENING -> com.cadence.domain.model.learning.SessionType.PRACTICE
    }
}