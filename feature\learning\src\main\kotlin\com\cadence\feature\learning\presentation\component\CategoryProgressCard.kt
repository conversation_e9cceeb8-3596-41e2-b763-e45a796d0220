package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.WordCategory

/**
 * 分类进度卡片组件
 * 显示特定分类的学习进度
 */
@Composable
fun CategoryProgressCard(
    category: WordCategory,
    progress: CategoryProgress,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分类图标
            Icon(
                imageVector = getCategoryIcon(category),
                contentDescription = category.displayName,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 分类信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = category.displayName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "${progress.masteredWords} / ${progress.totalWords} 已掌握",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 进度条
                LinearProgressIndicator(
                    progress = progress.progressPercentage,
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colorScheme.primary,
                    trackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 进度百分比
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "${(progress.progressPercentage * 100).toInt()}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = "完成度",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 分类进度数据类
 */
data class CategoryProgress(
    val totalWords: Int,
    val masteredWords: Int,
    val averageAccuracy: Float,
    val totalStudyTime: Long
) {
    val progressPercentage: Float
        get() = if (totalWords > 0) masteredWords.toFloat() / totalWords else 0f
}

/**
 * 获取分类对应的图标
 */
private fun getCategoryIcon(category: WordCategory): ImageVector {
    return when (category) {
        WordCategory.GENERAL -> Icons.Default.Language
        WordCategory.BUSINESS -> Icons.Default.Business
        WordCategory.ACADEMIC -> Icons.Default.School
        WordCategory.DAILY -> Icons.Default.Home
        WordCategory.TECHNICAL -> Icons.Default.Computer
        WordCategory.MEDICAL -> Icons.Default.LocalHospital
        WordCategory.LEGAL -> Icons.Default.Gavel
        WordCategory.TRAVEL -> Icons.Default.Flight
        WordCategory.FOOD -> Icons.Default.Restaurant
        WordCategory.CULTURE -> Icons.Default.Museum
    }
}