package com.cadence.feature.offline

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.core.logging.StructuredLogger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 语言包管理界面ViewModel
 * 负责语言包下载、管理和用户界面状态管理
 */
@HiltViewModel
class LanguagePackViewModel @Inject constructor(
    private val modelManager: TranslationModelManager,
    private val downloader: LanguagePackDownloader,
    private val storageManager: LanguagePackStorageManager,
    private val structuredLogger: StructuredLogger
) : ViewModel() {
    
    // UI状态管理
    private val _uiState = MutableStateFlow(LanguagePackUiState())
    val uiState: StateFlow<LanguagePackUiState> = _uiState.asStateFlow()
    
    // 可用语言包列表
    private val _availableLanguagePacks = MutableStateFlow<List<LanguagePackItem>>(emptyList())
    val availableLanguagePacks: StateFlow<List<LanguagePackItem>> = _availableLanguagePacks.asStateFlow()
    
    // 已安装语言包列表
    private val _installedLanguagePacks = MutableStateFlow<List<LanguagePackItem>>(emptyList())
    val installedLanguagePacks: StateFlow<List<LanguagePackItem>> = _installedLanguagePacks.asStateFlow()
    
    // 下载进度
    val downloadProgress: StateFlow<Map<String, DownloadProgress>> = downloader.downloadState
        .map { it.activeDownloads }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyMap())
    
    // 存储状态
    val storageState: StateFlow<StorageState> = storageManager.storageState
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), StorageState())
    
    init {
        initializeViewModel()
    }
    
    /**
     * 初始化ViewModel
     */
    private fun initializeViewModel() {
        viewModelScope.launch {
            try {
                updateUiState { it.copy(isLoading = true) }
                
                // 加载可用语言包
                loadAvailableLanguagePacks()
                
                // 加载已安装语言包
                loadInstalledLanguagePacks()
                
                // 监听模型管理器状态变化
                observeModelManagerState()
                
                updateUiState { it.copy(isLoading = false) }
                
                structuredLogger.logInfo(
                    message = "语言包管理界面初始化完成",
                    context = mapOf(
                        "available_packs" to _availableLanguagePacks.value.size.toString(),
                        "installed_packs" to _installedLanguagePacks.value.size.toString()
                    )
                )
                
            } catch (e: Exception) {
                Timber.e(e, "语言包管理界面初始化失败")
                updateUiState { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "初始化失败"
                    )
                }
            }
        }
    }
    
    /**
     * 下载语言包
     */
    fun downloadLanguagePack(languagePackItem: LanguagePackItem) {
        viewModelScope.launch {
            try {
                // 检查存储空间
                val storageCheck = storageManager.checkStorageSpace(languagePackItem.sizeMB)
                if (!storageCheck.hasEnoughSpace) {
                    updateUiState { 
                        it.copy(error = storageCheck.message)
                    }
                    return@launch
                }
                
                // 开始下载
                val result = downloader.downloadLanguagePack(languagePackItem.modelInfo)
                
                if (result.success) {
                    structuredLogger.logInfo(
                        message = "语言包下载成功",
                        context = mapOf(
                            "model_key" to languagePackItem.modelInfo.key,
                            "model_name" to languagePackItem.name
                        )
                    )
                    
                    // 刷新已安装列表
                    loadInstalledLanguagePacks()
                    
                    updateUiState { 
                        it.copy(
                            successMessage = "语言包 ${languagePackItem.name} 下载成功"
                        )
                    }
                } else {
                    updateUiState { 
                        it.copy(error = result.error ?: "下载失败")
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "下载语言包失败: ${languagePackItem.name}")
                updateUiState { 
                    it.copy(error = e.message ?: "下载失败")
                }
            }
        }
    }
    
    /**
     * 取消下载
     */
    fun cancelDownload(modelKey: String) {
        val success = downloader.cancelDownload(modelKey)
        if (success) {
            structuredLogger.logInfo(
                message = "下载已取消",
                context = mapOf("model_key" to modelKey)
            )
            updateUiState { 
                it.copy(successMessage = "下载已取消")
            }
        }
    }
    
    /**
     * 暂停下载
     */
    fun pauseDownload(modelKey: String) {
        val success = downloader.pauseDownload(modelKey)
        if (success) {
            updateUiState { 
                it.copy(successMessage = "下载已暂停")
            }
        }
    }
    
    /**
     * 恢复下载
     */
    fun resumeDownload(modelKey: String) {
        viewModelScope.launch {
            val success = downloader.resumeDownload(modelKey)
            if (success) {
                updateUiState { 
                    it.copy(successMessage = "下载已恢复")
                }
            } else {
                updateUiState { 
                    it.copy(error = "恢复下载失败")
                }
            }
        }
    }
    
    /**
     * 卸载语言包
     */
    fun uninstallLanguagePack(languagePackItem: LanguagePackItem) {
        viewModelScope.launch {
            try {
                val success = modelManager.uninstallModel(languagePackItem.modelInfo.key)
                
                if (success) {
                    structuredLogger.logInfo(
                        message = "语言包卸载成功",
                        context = mapOf(
                            "model_key" to languagePackItem.modelInfo.key,
                            "model_name" to languagePackItem.name
                        )
                    )
                    
                    // 刷新已安装列表
                    loadInstalledLanguagePacks()
                    
                    updateUiState { 
                        it.copy(
                            successMessage = "语言包 ${languagePackItem.name} 已卸载"
                        )
                    }
                } else {
                    updateUiState { 
                        it.copy(error = "卸载失败")
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "卸载语言包失败: ${languagePackItem.name}")
                updateUiState { 
                    it.copy(error = e.message ?: "卸载失败")
                }
            }
        }
    }
    
    /**
     * 执行存储清理
     */
    fun performStorageCleanup(strategy: CleanupStrategy = CleanupStrategy.SMART) {
        viewModelScope.launch {
            try {
                updateUiState { it.copy(isPerformingCleanup = true) }
                
                val result = storageManager.performCleanup(strategy)
                
                if (result.success) {
                    updateUiState { 
                        it.copy(
                            isPerformingCleanup = false,
                            successMessage = "清理完成，释放了 ${result.cleanedSizeMB}MB 空间"
                        )
                    }
                } else {
                    updateUiState { 
                        it.copy(
                            isPerformingCleanup = false,
                            error = result.error ?: "清理失败"
                        )
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "存储清理失败")
                updateUiState { 
                    it.copy(
                        isPerformingCleanup = false,
                        error = e.message ?: "清理失败"
                    )
                }
            }
        }
    }
    
    /**
     * 获取清理建议
     */
    fun getCleanupSuggestions(): List<CleanupSuggestion> {
        return storageManager.getCleanupSuggestions()
    }
    
    /**
     * 获取存储详情
     */
    fun getStorageDetails(): StorageDetails {
        return storageManager.getStorageDetails()
    }
    
    /**
     * 刷新数据
     */
    fun refresh() {
        viewModelScope.launch {
            try {
                updateUiState { it.copy(isRefreshing = true) }
                
                loadAvailableLanguagePacks()
                loadInstalledLanguagePacks()
                
                updateUiState { 
                    it.copy(
                        isRefreshing = false,
                        successMessage = "数据已刷新"
                    )
                }
                
            } catch (e: Exception) {
                Timber.e(e, "刷新数据失败")
                updateUiState { 
                    it.copy(
                        isRefreshing = false,
                        error = e.message ?: "刷新失败"
                    )
                }
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        updateUiState { it.copy(error = null) }
    }
    
    /**
     * 清除成功消息
     */
    fun clearSuccessMessage() {
        updateUiState { it.copy(successMessage = null) }
    }

    /**
     * 加载可用语言包
     */
    private suspend fun loadAvailableLanguagePacks() {
        try {
            // TODO: 从服务器或本地配置加载可用语言包列表
            // 这里使用模拟数据
            val availablePacks = listOf(
                createLanguagePackItem("zh_en", "中文-英文", "Chinese to English", 45L),
                createLanguagePackItem("en_zh", "英文-中文", "English to Chinese", 42L),
                createLanguagePackItem("zh_ja", "中文-日文", "Chinese to Japanese", 38L),
                createLanguagePackItem("ja_zh", "日文-中文", "Japanese to Chinese", 40L),
                createLanguagePackItem("zh_ko", "中文-韩文", "Chinese to Korean", 35L),
                createLanguagePackItem("ko_zh", "韩文-中文", "Korean to Chinese", 37L),
                createLanguagePackItem("en_ja", "英文-日文", "English to Japanese", 41L),
                createLanguagePackItem("ja_en", "日文-英文", "Japanese to English", 43L),
                createLanguagePackItem("en_ko", "英文-韩文", "English to Korean", 39L),
                createLanguagePackItem("ko_en", "韩文-英文", "Korean to English", 41L)
            ).filter { pack ->
                // 过滤掉已安装的语言包
                !modelManager.isModelInstalled(pack.modelInfo.key)
            }

            _availableLanguagePacks.value = availablePacks

        } catch (e: Exception) {
            Timber.e(e, "加载可用语言包失败")
            throw e
        }
    }

    /**
     * 加载已安装语言包
     */
    private suspend fun loadInstalledLanguagePacks() {
        try {
            val installedModels = modelManager.modelState.value.installedModels
            val installedPacks = installedModels.map { modelInfo ->
                LanguagePackItem(
                    id = modelInfo.key,
                    name = modelInfo.name,
                    description = modelInfo.description,
                    sourceLanguage = modelInfo.sourceLanguage,
                    targetLanguage = modelInfo.targetLanguage,
                    sizeMB = modelInfo.size / (1024 * 1024),
                    version = modelInfo.version,
                    accuracy = modelInfo.accuracy,
                    isInstalled = true,
                    modelInfo = modelInfo
                )
            }

            _installedLanguagePacks.value = installedPacks

        } catch (e: Exception) {
            Timber.e(e, "加载已安装语言包失败")
            throw e
        }
    }

    /**
     * 监听模型管理器状态变化
     */
    private fun observeModelManagerState() {
        viewModelScope.launch {
            modelManager.modelState.collect { state ->
                if (state.isReady) {
                    // 模型状态变化时刷新列表
                    loadAvailableLanguagePacks()
                    loadInstalledLanguagePacks()
                }
            }
        }
    }

    /**
     * 创建语言包项目
     */
    private fun createLanguagePackItem(
        key: String,
        name: String,
        description: String,
        sizeMB: Long
    ): LanguagePackItem {
        val parts = key.split("_")
        val sourceLanguage = parts[0]
        val targetLanguage = parts[1]

        val modelInfo = ModelInfo(
            key = key,
            name = name,
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage,
            version = "1.0.0",
            size = sizeMB * 1024 * 1024,
            checksum = "mock_checksum_$key",
            downloadUrl = "https://api.cadence.com/models/$key.tflite",
            description = description,
            accuracy = 0.85f + (key.hashCode() % 10) * 0.01f // 模拟准确率
        )

        return LanguagePackItem(
            id = key,
            name = name,
            description = description,
            sourceLanguage = sourceLanguage,
            targetLanguage = targetLanguage,
            sizeMB = sizeMB,
            version = "1.0.0",
            accuracy = modelInfo.accuracy,
            isInstalled = false,
            modelInfo = modelInfo
        )
    }

    /**
     * 更新UI状态
     */
    private fun updateUiState(update: (LanguagePackUiState) -> LanguagePackUiState) {
        _uiState.value = update(_uiState.value)
    }
}

// 数据类定义
data class LanguagePackUiState(
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val isPerformingCleanup: Boolean = false,
    val error: String? = null,
    val successMessage: String? = null
)

data class LanguagePackItem(
    val id: String,
    val name: String,
    val description: String,
    val sourceLanguage: String,
    val targetLanguage: String,
    val sizeMB: Long,
    val version: String,
    val accuracy: Float,
    val isInstalled: Boolean,
    val modelInfo: ModelInfo
) {
    val languagePair: String
        get() = "$sourceLanguage → $targetLanguage"

    val sizeText: String
        get() = "${sizeMB}MB"

    val accuracyText: String
        get() = "${(accuracy * 100).toInt()}%"
}
