package com.cadence.feature.settings.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.settings.presentation.component.*
import com.cadence.feature.settings.presentation.viewmodel.SettingsViewModel
import timber.log.Timber

/**
 * 通知设置界面
 * 管理应用的通知偏好设置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val userSettings by viewModel.userSettings.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    val notificationSettings = userSettings?.notificationSettings

    // 显示错误提示
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            Timber.e("通知设置错误: $error")
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "通知设置",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            // 通知设置说明
            item {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Notifications,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "通知管理",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "管理应用通知的显示方式和时机，确保您不会错过重要信息。",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))
            }

            // 基本通知设置
            item {
                SettingsGroupTitle(title = "基本设置")
            }

            item {
                SettingsSwitch(
                    title = "启用通知",
                    subtitle = "允许应用发送通知",
                    icon = Icons.Default.Notifications,
                    checked = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现通知开关逻辑
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "声音提醒",
                    subtitle = "通知时播放提示音",
                    icon = Icons.Default.VolumeUp,
                    checked = notificationSettings?.soundEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现声音开关逻辑
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "振动提醒",
                    subtitle = "通知时设备振动",
                    icon = Icons.Default.Vibration,
                    checked = notificationSettings?.vibrationEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现振动开关逻辑
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 翻译相关通知
            item {
                SettingsGroupTitle(title = "翻译通知")
            }

            item {
                SettingsSwitch(
                    title = "翻译完成通知",
                    subtitle = "翻译任务完成时显示通知",
                    icon = Icons.Default.Translate,
                    checked = notificationSettings?.translationCompleteEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现翻译完成通知开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "批量翻译进度",
                    subtitle = "批量翻译时显示进度通知",
                    icon = Icons.Default.BatchPrediction,
                    checked = notificationSettings?.batchTranslationProgressEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现批量翻译进度通知开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "翻译错误提醒",
                    subtitle = "翻译失败时显示错误通知",
                    icon = Icons.Default.Error,
                    checked = notificationSettings?.translationErrorEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现翻译错误通知开关
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 系统通知
            item {
                SettingsGroupTitle(title = "系统通知")
            }

            item {
                SettingsSwitch(
                    title = "应用更新提醒",
                    subtitle = "有新版本时显示更新通知",
                    icon = Icons.Default.SystemUpdate,
                    checked = notificationSettings?.appUpdateEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现应用更新通知开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "功能提示",
                    subtitle = "显示新功能和使用技巧",
                    icon = Icons.Default.Tips,
                    checked = notificationSettings?.featureTipsEnabled ?: true,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现功能提示通知开关
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 免打扰设置
            item {
                SettingsGroupTitle(title = "免打扰")
            }

            item {
                SettingsSwitch(
                    title = "免打扰模式",
                    subtitle = "在指定时间段内静音通知",
                    icon = Icons.Default.DoNotDisturb,
                    checked = notificationSettings?.doNotDisturbEnabled ?: false,
                    enabled = notificationSettings?.enabled ?: true,
                    onCheckedChange = { 
                        // TODO: 实现免打扰模式开关
                    }
                )
            }

            if (notificationSettings?.doNotDisturbEnabled == true) {
                item {
                    SettingsItem(
                        title = "免打扰时间",
                        subtitle = "设置免打扰的时间段",
                        icon = Icons.Default.Schedule,
                        onClick = {
                            // TODO: 打开时间选择器
                        },
                        enabled = notificationSettings.enabled
                    )
                }
            }

            item {
                SettingsDivider()
            }

            // 通知渠道管理
            item {
                SettingsGroupTitle(title = "高级设置")
            }

            item {
                SettingsItem(
                    title = "通知渠道管理",
                    subtitle = "管理不同类型的通知设置",
                    icon = Icons.Default.Tune,
                    onClick = {
                        // TODO: 打开系统通知渠道设置
                    }
                )
            }

            item {
                SettingsItem(
                    title = "通知历史",
                    subtitle = "查看最近的通知记录",
                    icon = Icons.Default.History,
                    onClick = {
                        // TODO: 打开通知历史页面
                    }
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // 加载指示器
    if (uiState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}
