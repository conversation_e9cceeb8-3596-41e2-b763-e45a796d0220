package com.cadence.feature.offline

import android.content.Context
import com.cadence.core.logging.StructuredLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.security.MessageDigest
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译模型管理器
 * 负责翻译模型的下载、存储、版本控制和验证
 */
@Singleton
class TranslationModelManager @Inject constructor(
    private val context: Context,
    private val structuredLogger: StructuredLogger
) {
    
    companion object {
        private const val MODELS_DIR = "translation_models"
        private const val MODEL_MANIFEST_FILE = "model_manifest.json"
        private const val MODEL_EXTENSION = ".tflite"
        private const val CHECKSUM_EXTENSION = ".sha256"
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 模型状态管理
    private val _modelState = MutableStateFlow(ModelManagerState())
    val modelState: StateFlow<ModelManagerState> = _modelState.asStateFlow()
    
    // 模型目录
    private val modelsDir: File by lazy {
        File(context.filesDir, MODELS_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 模型清单文件
    private val manifestFile: File by lazy {
        File(modelsDir, MODEL_MANIFEST_FILE)
    }
    
    // JSON序列化器
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    init {
        initializeManager()
    }
    
    /**
     * 初始化模型管理器
     */
    private fun initializeManager() {
        scope.launch {
            try {
                updateModelState { it.copy(isInitializing = true) }
                
                // 加载模型清单
                val manifest = loadModelManifest()
                
                // 验证已安装的模型
                val installedModels = verifyInstalledModels(manifest)
                
                structuredLogger.logInfo(
                    message = "模型管理器初始化完成",
                    context = mapOf(
                        "models_dir" to modelsDir.absolutePath,
                        "installed_models" to installedModels.size.toString(),
                        "manifest_models" to manifest.models.size.toString()
                    )
                )
                
                updateModelState { 
                    it.copy(
                        isInitializing = false,
                        isReady = true,
                        installedModels = installedModels,
                        availableModels = manifest.models,
                        error = null
                    )
                }
                
                Timber.d("模型管理器初始化完成，已安装模型: ${installedModels.size}")
                
            } catch (e: Exception) {
                Timber.e(e, "模型管理器初始化失败")
                structuredLogger.logError(
                    message = "模型管理器初始化失败",
                    error = e,
                    context = mapOf("models_dir" to modelsDir.absolutePath)
                )
                
                updateModelState { 
                    it.copy(
                        isInitializing = false,
                        isReady = false,
                        error = e.message
                    )
                }
            }
        }
    }
    
    /**
     * 获取可用的翻译模型列表
     */
    fun getAvailableModels(): List<String> {
        return _modelState.value.installedModels.map { it.key }
    }
    
    /**
     * 获取模型文件
     */
    fun getModelFile(modelKey: String): File {
        return File(modelsDir, "$modelKey$MODEL_EXTENSION")
    }
    
    /**
     * 检查模型是否已安装
     */
    fun isModelInstalled(modelKey: String): Boolean {
        return _modelState.value.installedModels.any { it.key == modelKey }
    }
    
    /**
     * 获取模型信息
     */
    fun getModelInfo(modelKey: String): ModelInfo? {
        return _modelState.value.installedModels.find { it.key == modelKey }
    }
    
    /**
     * 安装模型
     */
    suspend fun installModel(modelInfo: ModelInfo, modelData: ByteArray): ModelInstallResult = withContext(Dispatchers.IO) {
        try {
            updateModelState { 
                it.copy(
                    installingModels = it.installingModels + modelInfo.key
                )
            }
            
            structuredLogger.logInfo(
                message = "开始安装模型",
                context = mapOf(
                    "model_key" to modelInfo.key,
                    "model_size" to modelData.size.toString(),
                    "model_version" to modelInfo.version
                )
            )
            
            // 验证模型数据
            if (!verifyModelChecksum(modelData, modelInfo.checksum)) {
                return@withContext ModelInstallResult(
                    success = false,
                    modelKey = modelInfo.key,
                    error = "模型校验失败"
                )
            }
            
            // 写入模型文件
            val modelFile = getModelFile(modelInfo.key)
            modelFile.writeBytes(modelData)
            
            // 写入校验和文件
            val checksumFile = File(modelsDir, "${modelInfo.key}$CHECKSUM_EXTENSION")
            checksumFile.writeText(modelInfo.checksum)
            
            // 更新清单
            updateModelManifest(modelInfo)
            
            // 更新状态
            val currentState = _modelState.value
            updateModelState { 
                it.copy(
                    installedModels = it.installedModels + modelInfo,
                    installingModels = it.installingModels - modelInfo.key
                )
            }
            
            structuredLogger.logInfo(
                message = "模型安装成功",
                context = mapOf(
                    "model_key" to modelInfo.key,
                    "file_path" to modelFile.absolutePath,
                    "file_size" to modelFile.length().toString()
                )
            )
            
            ModelInstallResult(
                success = true,
                modelKey = modelInfo.key,
                installedPath = modelFile.absolutePath
            )
            
        } catch (e: Exception) {
            Timber.e(e, "模型安装失败: ${modelInfo.key}")
            structuredLogger.logError(
                message = "模型安装失败",
                error = e,
                context = mapOf("model_key" to modelInfo.key)
            )
            
            // 清理失败的安装
            cleanupFailedInstallation(modelInfo.key)
            
            updateModelState { 
                it.copy(
                    installingModels = it.installingModels - modelInfo.key
                )
            }
            
            ModelInstallResult(
                success = false,
                modelKey = modelInfo.key,
                error = e.message ?: "安装失败"
            )
        }
    }
    
    /**
     * 卸载模型
     */
    suspend fun uninstallModel(modelKey: String): Boolean = withContext(Dispatchers.IO) {
        try {
            val modelFile = getModelFile(modelKey)
            val checksumFile = File(modelsDir, "$modelKey$CHECKSUM_EXTENSION")
            
            // 删除文件
            var success = true
            if (modelFile.exists()) {
                success = success && modelFile.delete()
            }
            if (checksumFile.exists()) {
                success = success && checksumFile.delete()
            }
            
            if (success) {
                // 更新状态
                updateModelState { 
                    it.copy(
                        installedModels = it.installedModels.filter { model -> model.key != modelKey }
                    )
                }
                
                // 更新清单
                removeFromModelManifest(modelKey)
                
                structuredLogger.logInfo(
                    message = "模型卸载成功",
                    context = mapOf("model_key" to modelKey)
                )
                
                Timber.d("模型卸载成功: $modelKey")
            }
            
            success
            
        } catch (e: Exception) {
            Timber.e(e, "模型卸载失败: $modelKey")
            structuredLogger.logError(
                message = "模型卸载失败",
                error = e,
                context = mapOf("model_key" to modelKey)
            )
            false
        }
    }
    
    /**
     * 获取模型存储使用情况
     */
    fun getStorageUsage(): ModelStorageInfo {
        val totalSize = modelsDir.walkTopDown()
            .filter { it.isFile }
            .sumOf { it.length() }
        
        val modelCount = _modelState.value.installedModels.size
        val availableSpace = modelsDir.freeSpace
        
        return ModelStorageInfo(
            totalSize = totalSize,
            modelCount = modelCount,
            availableSpace = availableSpace,
            modelsDirectory = modelsDir.absolutePath
        )
    }
    
    /**
     * 清理临时文件和损坏的模型
     */
    suspend fun cleanupStorage(): Int = withContext(Dispatchers.IO) {
        var cleanedCount = 0
        
        try {
            val installedModelKeys = _modelState.value.installedModels.map { it.key }.toSet()
            
            modelsDir.listFiles()?.forEach { file ->
                when {
                    file.name.endsWith(MODEL_EXTENSION) -> {
                        val modelKey = file.nameWithoutExtension
                        if (modelKey !in installedModelKeys) {
                            if (file.delete()) {
                                cleanedCount++
                                Timber.d("清理孤立模型文件: ${file.name}")
                            }
                        }
                    }
                    file.name.endsWith(CHECKSUM_EXTENSION) -> {
                        val modelKey = file.nameWithoutExtension
                        if (modelKey !in installedModelKeys) {
                            if (file.delete()) {
                                cleanedCount++
                                Timber.d("清理孤立校验文件: ${file.name}")
                            }
                        }
                    }
                    file.name.endsWith(".tmp") -> {
                        if (file.delete()) {
                            cleanedCount++
                            Timber.d("清理临时文件: ${file.name}")
                        }
                    }
                }
            }
            
            structuredLogger.logInfo(
                message = "存储清理完成",
                context = mapOf("cleaned_files" to cleanedCount.toString())
            )
            
        } catch (e: Exception) {
            Timber.e(e, "存储清理失败")
            structuredLogger.logError(
                message = "存储清理失败",
                error = e
            )
        }
        
        cleanedCount
    }

    /**
     * 加载模型清单
     */
    private fun loadModelManifest(): ModelManifest {
        return try {
            if (manifestFile.exists()) {
                val manifestJson = manifestFile.readText()
                json.decodeFromString<ModelManifest>(manifestJson)
            } else {
                ModelManifest()
            }
        } catch (e: Exception) {
            Timber.w(e, "加载模型清单失败，使用默认清单")
            ModelManifest()
        }
    }

    /**
     * 保存模型清单
     */
    private fun saveModelManifest(manifest: ModelManifest) {
        try {
            val manifestJson = json.encodeToString(ModelManifest.serializer(), manifest)
            manifestFile.writeText(manifestJson)
        } catch (e: Exception) {
            Timber.e(e, "保存模型清单失败")
        }
    }

    /**
     * 更新模型清单
     */
    private fun updateModelManifest(modelInfo: ModelInfo) {
        val manifest = loadModelManifest()
        val updatedModels = manifest.models.filter { it.key != modelInfo.key } + modelInfo
        saveModelManifest(manifest.copy(models = updatedModels))
    }

    /**
     * 从清单中移除模型
     */
    private fun removeFromModelManifest(modelKey: String) {
        val manifest = loadModelManifest()
        val updatedModels = manifest.models.filter { it.key != modelKey }
        saveModelManifest(manifest.copy(models = updatedModels))
    }

    /**
     * 验证已安装的模型
     */
    private suspend fun verifyInstalledModels(manifest: ModelManifest): List<ModelInfo> = withContext(Dispatchers.IO) {
        val verifiedModels = mutableListOf<ModelInfo>()

        manifest.models.forEach { modelInfo ->
            val modelFile = getModelFile(modelInfo.key)
            val checksumFile = File(modelsDir, "${modelInfo.key}$CHECKSUM_EXTENSION")

            if (modelFile.exists() && checksumFile.exists()) {
                try {
                    val storedChecksum = checksumFile.readText().trim()
                    val actualChecksum = calculateFileChecksum(modelFile)

                    if (storedChecksum == actualChecksum && storedChecksum == modelInfo.checksum) {
                        verifiedModels.add(modelInfo)
                        Timber.d("模型验证成功: ${modelInfo.key}")
                    } else {
                        Timber.w("模型校验失败: ${modelInfo.key}")
                        // 删除损坏的模型
                        modelFile.delete()
                        checksumFile.delete()
                    }
                } catch (e: Exception) {
                    Timber.e(e, "模型验证异常: ${modelInfo.key}")
                }
            }
        }

        verifiedModels
    }

    /**
     * 验证模型校验和
     */
    private fun verifyModelChecksum(modelData: ByteArray, expectedChecksum: String): Boolean {
        return try {
            val actualChecksum = calculateDataChecksum(modelData)
            actualChecksum == expectedChecksum
        } catch (e: Exception) {
            Timber.e(e, "校验和验证失败")
            false
        }
    }

    /**
     * 计算文件校验和
     */
    private fun calculateFileChecksum(file: File): String {
        return calculateDataChecksum(file.readBytes())
    }

    /**
     * 计算数据校验和
     */
    private fun calculateDataChecksum(data: ByteArray): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(data)
        return hashBytes.joinToString("") { "%02x".format(it) }
    }

    /**
     * 清理失败的安装
     */
    private fun cleanupFailedInstallation(modelKey: String) {
        try {
            val modelFile = getModelFile(modelKey)
            val checksumFile = File(modelsDir, "$modelKey$CHECKSUM_EXTENSION")

            if (modelFile.exists()) modelFile.delete()
            if (checksumFile.exists()) checksumFile.delete()

            Timber.d("清理失败安装: $modelKey")
        } catch (e: Exception) {
            Timber.e(e, "清理失败安装异常: $modelKey")
        }
    }

    /**
     * 更新模型状态
     */
    private fun updateModelState(update: (ModelManagerState) -> ModelManagerState) {
        _modelState.value = update(_modelState.value)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        Timber.d("模型管理器已清理")
    }
}

// 数据类定义
@Serializable
data class ModelManifest(
    val version: String = "1.0",
    val models: List<ModelInfo> = emptyList(),
    val lastUpdated: Long = System.currentTimeMillis()
)

@Serializable
data class ModelInfo(
    val key: String,
    val name: String,
    val sourceLanguage: String,
    val targetLanguage: String,
    val version: String,
    val size: Long,
    val checksum: String,
    val downloadUrl: String = "",
    val description: String = "",
    val accuracy: Float = 0f,
    val createdAt: Long = System.currentTimeMillis()
)

data class ModelManagerState(
    val isInitializing: Boolean = false,
    val isReady: Boolean = false,
    val installedModels: List<ModelInfo> = emptyList(),
    val availableModels: List<ModelInfo> = emptyList(),
    val installingModels: Set<String> = emptySet(),
    val error: String? = null
)

data class ModelInstallResult(
    val success: Boolean,
    val modelKey: String,
    val installedPath: String = "",
    val error: String? = null
)

data class ModelStorageInfo(
    val totalSize: Long,
    val modelCount: Int,
    val availableSpace: Long,
    val modelsDirectory: String
)
