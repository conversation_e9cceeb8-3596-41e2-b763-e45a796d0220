package com.cadence.feature.culture.presentation.state

import com.cadence.domain.model.culture.*

/**
 * 文化解释界面状态
 */
data class CulturalExplanationUiState(
    val isLoading: Boolean = false,
    val culturalContexts: List<CulturalContext> = emptyList(),
    val error: String? = null,
    val searchQuery: String = "",
    val isSearching: Boolean = false,
    val selectedFilters: CulturalFilters = CulturalFilters(),
    val bookmarkedContexts: Set<String> = emptySet(),
    val learnedContexts: Set<String> = emptySet()
)

/**
 * 文化详情界面状态
 */
data class CulturalDetailUiState(
    val isLoading: Boolean = false,
    val culturalContext: CulturalContext? = null,
    val error: String? = null,
    val isBookmarked: Boolean = false,
    val isLearned: Boolean = false,
    val learningNotes: String = "",
    val isNotesEditing: Boolean = false,
    val relatedContexts: List<CulturalContext> = emptyList(),
    val isLoadingRelated: Boolean = false
)

/**
 * 文化推荐界面状态
 */
data class CulturalRecommendationUiState(
    val isLoading: Boolean = false,
    val popularRecommendations: List<CulturalRecommendation> = emptyList(),
    val personalizedRecommendations: List<CulturalRecommendation> = emptyList(),
    val categoryRecommendations: Map<CulturalKnowledgeType, List<CulturalRecommendation>> = emptyMap(),
    val error: String? = null,
    val selectedFilters: RecommendationFilters = RecommendationFilters(),
    val filteredRecommendations: List<CulturalRecommendation> = emptyList(),
    val isFiltering: Boolean = false,
    val likedRecommendations: Set<String> = emptySet(),
    val bookmarkedRecommendations: Set<String> = emptySet()
)

/**
 * 文化筛选条件
 */
data class CulturalFilters(
    val difficulty: CulturalDifficulty? = null,
    val region: String? = null,
    val knowledgeType: CulturalKnowledgeType? = null,
    val appropriateness: AppropriatenessLevel? = null,
    val tags: List<String> = emptyList()
)

/**
 * 推荐筛选条件
 */
data class RecommendationFilters(
    val type: CulturalKnowledgeType? = null,
    val difficulty: CulturalDifficulty? = null,
    val region: String? = null,
    val minPopularity: Int = 0,
    val tags: List<String> = emptyList()
)

/**
 * 搜索结果状态
 */
data class SearchResultState(
    val query: String = "",
    val results: List<CulturalContext> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val totalCount: Int = 0,
    val hasMore: Boolean = false
)

/**
 * 学习进度状态
 */
data class LearningProgressState(
    val totalContexts: Int = 0,
    val learnedContexts: Int = 0,
    val bookmarkedContexts: Int = 0,
    val studyStreak: Int = 0,
    val weeklyGoal: Int = 0,
    val weeklyProgress: Int = 0,
    val difficultyProgress: Map<CulturalDifficulty, Int> = emptyMap(),
    val regionProgress: Map<String, Int> = emptyMap()
)

/**
 * 用户交互状态
 */
data class UserInteractionState(
    val isBookmarking: Boolean = false,
    val isMarkingAsLearned: Boolean = false,
    val isLiking: Boolean = false,
    val isSaving: Boolean = false,
    val isSharing: Boolean = false,
    val lastAction: UserAction? = null
)

/**
 * 用户操作类型
 */
sealed class UserAction {
    object Bookmark : UserAction()
    object MarkAsLearned : UserAction()
    object Like : UserAction()
    object Share : UserAction()
    object SaveNotes : UserAction()
    data class Filter(val filters: CulturalFilters) : UserAction()
    data class Search(val query: String) : UserAction()
}