package com.cadence.domain.repository

import com.cadence.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 同步数据仓库接口
 * 定义数据同步相关的操作契约
 */
interface SyncRepository {
    
    /**
     * 获取同步配置
     * @return 同步配置流
     */
    fun getSyncConfig(): Flow<SyncConfig>
    
    /**
     * 更新同步配置
     * @param config 新的同步配置
     * @return 操作结果
     */
    suspend fun updateSyncConfig(config: SyncConfig): Result<Unit>
    
    /**
     * 获取当前同步状态
     * @return 同步状态流
     */
    fun getSyncStatus(): Flow<SyncStatus>
    
    /**
     * 执行完整同步
     * @param syncType 同步类型
     * @param forceSync 是否强制同步
     * @return 同步结果
     */
    suspend fun performSync(
        syncType: SyncType = SyncType.ALL,
        forceSync: Boolean = false
    ): Result<SyncResult>
    
    /**
     * 上传翻译历史到云端
     * @param translations 翻译记录列表
     * @return 上传结果
     */
    suspend fun uploadTranslationHistory(
        translations: List<Translation>
    ): Result<SyncResult>
    
    /**
     * 从云端下载翻译历史
     * @param lastSyncTime 上次同步时间
     * @return 下载结果
     */
    suspend fun downloadTranslationHistory(
        lastSyncTime: Long = 0L
    ): Result<TranslationSyncData>
    
    /**
     * 上传标签数据到云端
     * @param tags 标签列表
     * @return 上传结果
     */
    suspend fun uploadTags(tags: List<Tag>): Result<SyncResult>
    
    /**
     * 从云端下载标签数据
     * @param lastSyncTime 上次同步时间
     * @return 下载结果
     */
    suspend fun downloadTags(lastSyncTime: Long = 0L): Result<List<Tag>>
    
    /**
     * 上传用户偏好到云端
     * @param preferences 用户偏好
     * @return 上传结果
     */
    suspend fun uploadUserPreferences(
        preferences: UserPreference
    ): Result<SyncResult>
    
    /**
     * 从云端下载用户偏好
     * @return 下载结果
     */
    suspend fun downloadUserPreferences(): Result<UserPreference>
    
    /**
     * 解决同步冲突
     * @param conflicts 冲突列表
     * @param resolution 解决策略
     * @return 解决结果
     */
    suspend fun resolveConflicts(
        conflicts: List<SyncConflict<*>>,
        resolution: ConflictResolution
    ): Result<List<SyncItemResult>>
    
    /**
     * 获取同步历史记录
     * @param limit 限制数量
     * @return 同步历史流
     */
    fun getSyncHistory(limit: Int = 50): Flow<List<SyncResult>>
    
    /**
     * 获取同步统计信息
     * @return 统计信息
     */
    suspend fun getSyncStatistics(): Result<SyncStatistics>
    
    /**
     * 清理同步数据
     * @param olderThan 清理指定时间之前的数据
     * @return 操作结果
     */
    suspend fun cleanupSyncData(olderThan: Long): Result<Unit>
    
    /**
     * 检查网络连接状态
     * @return 是否可以同步
     */
    suspend fun canSync(): Boolean
    
    /**
     * 获取设备信息
     * @return 设备信息列表
     */
    suspend fun getDeviceList(): Result<List<DeviceInfo>>
    
    /**
     * 注册当前设备
     * @param deviceInfo 设备信息
     * @return 操作结果
     */
    suspend fun registerDevice(deviceInfo: DeviceInfo): Result<Unit>
    
    /**
     * 注销设备
     * @param deviceId 设备ID
     * @return 操作结果
     */
    suspend fun unregisterDevice(deviceId: String): Result<Unit>
    
    /**
     * 获取云端数据大小
     * @return 数据大小（字节）
     */
    suspend fun getCloudDataSize(): Result<Long>
    
    /**
     * 备份所有数据到云端
     * @return 备份结果
     */
    suspend fun backupAllData(): Result<SyncResult>
    
    /**
     * 从云端恢复所有数据
     * @param replaceLocal 是否替换本地数据
     * @return 恢复结果
     */
    suspend fun restoreAllData(replaceLocal: Boolean = false): Result<SyncResult>
    
    /**
     * 删除云端所有数据
     * @return 操作结果
     */
    suspend fun deleteCloudData(): Result<Unit>
}
