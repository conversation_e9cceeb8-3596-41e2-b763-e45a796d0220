package com.cadence.core.performance

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import kotlinx.coroutines.*
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import timber.log.Timber
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络性能优化器
 * 负责优化网络请求性能，包括缓存、压缩、批量处理等
 */
@Singleton
class NetworkPerformanceOptimizer @Inject constructor(
    private val context: Context
) {
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    // 网络请求统计
    private val requestMetrics = ConcurrentHashMap<String, NetworkRequestMetrics>()
    private val batchRequestQueue = mutableListOf<BatchRequest>()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 缓存配置
    private val cacheSize = 50 * 1024 * 1024 // 50MB
    private val cache = Cache(context.cacheDir, cacheSize.toLong())
    
    /**
     * 创建优化的OkHttpClient
     */
    fun createOptimizedOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .cache(cache)
            .addInterceptor(createCompressionInterceptor())
            .addInterceptor(createCacheInterceptor())
            .addInterceptor(createRetryInterceptor())
            .addInterceptor(createMetricsInterceptor())
            .addNetworkInterceptor(createNetworkCacheInterceptor())
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .connectionPool(ConnectionPool(10, 5, TimeUnit.MINUTES))
            .apply {
                if (com.cadence.cadence.BuildConfig.DEBUG) {
                    addInterceptor(HttpLoggingInterceptor().apply {
                        level = HttpLoggingInterceptor.Level.BODY
                    })
                }
            }
            .build()
    }
    
    /**
     * 创建压缩拦截器
     */
    private fun createCompressionInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            val compressedRequest = originalRequest.newBuilder()
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Accept", "application/json")
                .build()
            
            chain.proceed(compressedRequest)
        }
    }
    
    /**
     * 创建缓存拦截器
     */
    private fun createCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            
            // 根据网络状态调整缓存策略
            val cacheRequest = if (isNetworkAvailable()) {
                // 网络可用时，优先使用网络，缓存作为备份
                request.newBuilder()
                    .header("Cache-Control", "public, max-age=300") // 5分钟缓存
                    .build()
            } else {
                // 网络不可用时，强制使用缓存
                request.newBuilder()
                    .header("Cache-Control", "public, only-if-cached, max-stale=86400") // 1天
                    .build()
            }
            
            chain.proceed(cacheRequest)
        }
    }
    
    /**
     * 创建网络缓存拦截器
     */
    private fun createNetworkCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            val response = chain.proceed(chain.request())
            
            // 为响应添加缓存头
            response.newBuilder()
                .header("Cache-Control", "public, max-age=300")
                .removeHeader("Pragma")
                .build()
        }
    }
    
    /**
     * 创建重试拦截器
     */
    private fun createRetryInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            var response: Response? = null
            var exception: IOException? = null
            
            // 最多重试3次
            repeat(3) { attempt ->
                try {
                    response?.close() // 关闭之前的响应
                    response = chain.proceed(request)
                    
                    if (response!!.isSuccessful) {
                        return@Interceptor response!!
                    }
                } catch (e: IOException) {
                    exception = e
                    Timber.w("网络请求失败，第${attempt + 1}次重试: ${e.message}")
                    
                    // 指数退避延迟
                    if (attempt < 2) {
                        Thread.sleep((1000 * (attempt + 1)).toLong())
                    }
                }
            }
            
            // 所有重试都失败，抛出最后的异常
            throw exception ?: IOException("网络请求失败")
        }
    }
    
    /**
     * 创建性能指标拦截器
     */
    private fun createMetricsInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            val startTime = System.currentTimeMillis()
            
            try {
                val response = chain.proceed(request)
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                // 记录请求指标
                recordRequestMetrics(
                    url = request.url.toString(),
                    method = request.method,
                    responseCode = response.code,
                    duration = duration,
                    requestSize = request.body?.contentLength() ?: 0,
                    responseSize = response.body?.contentLength() ?: 0,
                    fromCache = response.cacheResponse != null
                )
                
                response
            } catch (e: Exception) {
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                // 记录失败的请求
                recordRequestMetrics(
                    url = request.url.toString(),
                    method = request.method,
                    responseCode = -1,
                    duration = duration,
                    requestSize = request.body?.contentLength() ?: 0,
                    responseSize = 0,
                    fromCache = false,
                    error = e.message
                )
                
                throw e
            }
        }
    }
    
    /**
     * 添加批量请求
     */
    fun addToBatch(request: BatchRequest) {
        synchronized(batchRequestQueue) {
            batchRequestQueue.add(request)
            
            // 当批量请求达到阈值时，执行批量处理
            if (batchRequestQueue.size >= 5) {
                scope.launch {
                    processBatchRequests()
                }
            }
        }
    }
    
    /**
     * 处理批量请求
     */
    private suspend fun processBatchRequests() {
        val requestsToProcess = synchronized(batchRequestQueue) {
            val requests = batchRequestQueue.toList()
            batchRequestQueue.clear()
            requests
        }
        
        if (requestsToProcess.isEmpty()) return
        
        Timber.d("开始处理批量请求，数量: ${requestsToProcess.size}")
        
        // 并行处理批量请求
        val jobs = requestsToProcess.map { batchRequest ->
            async {
                try {
                    // 这里可以实现具体的批量请求逻辑
                    // 例如：合并多个API调用为单个批量调用
                    batchRequest.callback.onSuccess("批量处理成功")
                } catch (e: Exception) {
                    batchRequest.callback.onError(e)
                }
            }
        }
        
        jobs.awaitAll()
        Timber.d("批量请求处理完成")
    }
    
    /**
     * 检查网络可用性
     */
    fun isNetworkAvailable(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
    }
    
    /**
     * 获取网络类型
     */
    fun getNetworkType(): NetworkType {
        val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE
        
        return when {
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
            capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
            else -> NetworkType.OTHER
        }
    }
    
    /**
     * 记录请求指标
     */
    private fun recordRequestMetrics(
        url: String,
        method: String,
        responseCode: Int,
        duration: Long,
        requestSize: Long,
        responseSize: Long,
        fromCache: Boolean,
        error: String? = null
    ) {
        val metrics = NetworkRequestMetrics(
            url = url,
            method = method,
            responseCode = responseCode,
            duration = duration,
            requestSize = requestSize,
            responseSize = responseSize,
            fromCache = fromCache,
            timestamp = System.currentTimeMillis(),
            networkType = getNetworkType(),
            error = error
        )
        
        val key = "${method}_${url.hashCode()}"
        requestMetrics[key] = metrics
        
        // 性能分析
        if (duration > 5000) {
            Timber.w("慢请求检测: $method $url 耗时 ${duration}ms")
        }
        
        if (fromCache) {
            Timber.d("缓存命中: $method $url")
        }
    }
    
    /**
     * 获取网络性能报告
     */
    fun getNetworkPerformanceReport(): NetworkPerformanceReport {
        val metrics = requestMetrics.values.toList()
        val totalRequests = metrics.size
        val successfulRequests = metrics.count { it.responseCode in 200..299 }
        val cachedRequests = metrics.count { it.fromCache }
        val averageResponseTime = if (metrics.isNotEmpty()) {
            metrics.map { it.duration }.average()
        } else 0.0
        
        val slowRequests = metrics.filter { it.duration > 3000 }
        val errorRequests = metrics.filter { it.responseCode < 0 || it.responseCode >= 400 }
        
        return NetworkPerformanceReport(
            totalRequests = totalRequests,
            successfulRequests = successfulRequests,
            cachedRequests = cachedRequests,
            cacheHitRate = if (totalRequests > 0) cachedRequests.toFloat() / totalRequests else 0f,
            averageResponseTime = averageResponseTime,
            slowRequests = slowRequests.size,
            errorRequests = errorRequests.size,
            networkType = getNetworkType(),
            isNetworkAvailable = isNetworkAvailable()
        )
    }
    
    /**
     * 清理过期的请求指标
     */
    fun cleanupOldMetrics() {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = requestMetrics.entries
            .filter { (_, metrics) -> currentTime - metrics.timestamp > 3600000 } // 1小时
            .map { it.key }
        
        expiredKeys.forEach { key ->
            requestMetrics.remove(key)
        }
        
        Timber.d("清理过期网络指标，数量: ${expiredKeys.size}")
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        requestMetrics.clear()
        batchRequestQueue.clear()
        cache.close()
        Timber.d("网络性能优化器已清理")
    }
}

/**
 * 网络请求指标
 */
data class NetworkRequestMetrics(
    val url: String,
    val method: String,
    val responseCode: Int,
    val duration: Long,
    val requestSize: Long,
    val responseSize: Long,
    val fromCache: Boolean,
    val timestamp: Long,
    val networkType: NetworkType,
    val error: String? = null
)

/**
 * 网络性能报告
 */
data class NetworkPerformanceReport(
    val totalRequests: Int,
    val successfulRequests: Int,
    val cachedRequests: Int,
    val cacheHitRate: Float,
    val averageResponseTime: Double,
    val slowRequests: Int,
    val errorRequests: Int,
    val networkType: NetworkType,
    val isNetworkAvailable: Boolean
)

/**
 * 批量请求
 */
data class BatchRequest(
    val id: String,
    val data: Any,
    val callback: BatchRequestCallback
)

/**
 * 批量请求回调
 */
interface BatchRequestCallback {
    fun onSuccess(result: String)
    fun onError(error: Exception)
}

/**
 * 网络类型枚举
 */
enum class NetworkType {
    NONE, WIFI, CELLULAR, ETHERNET, OTHER
}
