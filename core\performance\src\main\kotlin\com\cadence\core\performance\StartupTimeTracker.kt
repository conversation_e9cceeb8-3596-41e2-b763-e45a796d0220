package com.cadence.core.performance

import android.app.Application
import android.os.SystemClock
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用启动时间跟踪器
 * 负责监控和分析应用启动性能
 */
@Singleton
class StartupTimeTracker @Inject constructor() {
    
    private var applicationStartTime: Long = 0
    private var applicationCreatedTime: Long = 0
    private var mainActivityCreatedTime: Long = 0
    private var firstFrameRenderedTime: Long = 0
    private var splashScreenDismissedTime: Long = 0
    
    private val startupEvents = mutableListOf<StartupEvent>()
    
    /**
     * 记录应用启动开始时间
     */
    fun recordApplicationStart() {
        applicationStartTime = SystemClock.elapsedRealtime()
        addEvent("application_start", "应用进程启动")
        Timber.d("应用启动时间跟踪开始")
    }
    
    /**
     * 记录Application.onCreate完成时间
     */
    fun recordApplicationCreated() {
        applicationCreatedTime = SystemClock.elapsedRealtime()
        addEvent("application_created", "Application.onCreate完成")
        
        val initTime = applicationCreatedTime - applicationStartTime
        Timber.d("Application初始化耗时: ${initTime}ms")
    }
    
    /**
     * 记录MainActivity.onCreate完成时间
     */
    fun recordMainActivityCreated() {
        mainActivityCreatedTime = SystemClock.elapsedRealtime()
        addEvent("main_activity_created", "MainActivity.onCreate完成")
        
        val activityCreateTime = mainActivityCreatedTime - applicationCreatedTime
        Timber.d("MainActivity创建耗时: ${activityCreateTime}ms")
    }
    
    /**
     * 记录首帧渲染完成时间
     */
    fun recordFirstFrameRendered() {
        firstFrameRenderedTime = SystemClock.elapsedRealtime()
        addEvent("first_frame_rendered", "首帧渲染完成")
        
        val renderTime = firstFrameRenderedTime - mainActivityCreatedTime
        val totalStartupTime = firstFrameRenderedTime - applicationStartTime
        
        Timber.d("首帧渲染耗时: ${renderTime}ms")
        Timber.d("总启动时间: ${totalStartupTime}ms")
        
        // 分析启动性能
        analyzeStartupPerformance()
    }
    
    /**
     * 记录启动画面消失时间
     */
    fun recordSplashScreenDismissed() {
        splashScreenDismissedTime = SystemClock.elapsedRealtime()
        addEvent("splash_screen_dismissed", "启动画面消失")
        
        val splashDuration = splashScreenDismissedTime - applicationStartTime
        Timber.d("启动画面显示时长: ${splashDuration}ms")
    }
    
    /**
     * 记录自定义启动事件
     */
    fun recordCustomEvent(eventName: String, description: String) {
        val currentTime = SystemClock.elapsedRealtime()
        addEvent(eventName, description)
        
        val elapsedTime = currentTime - applicationStartTime
        Timber.d("启动事件 [$eventName]: ${elapsedTime}ms - $description")
    }
    
    /**
     * 获取启动性能报告
     */
    fun getStartupReport(): StartupReport {
        val totalTime = if (firstFrameRenderedTime > 0) {
            firstFrameRenderedTime - applicationStartTime
        } else {
            SystemClock.elapsedRealtime() - applicationStartTime
        }
        
        return StartupReport(
            totalStartupTime = totalTime,
            applicationInitTime = applicationCreatedTime - applicationStartTime,
            activityCreateTime = mainActivityCreatedTime - applicationCreatedTime,
            firstFrameRenderTime = firstFrameRenderedTime - mainActivityCreatedTime,
            splashScreenDuration = splashScreenDismissedTime - applicationStartTime,
            events = startupEvents.toList(),
            performanceGrade = calculatePerformanceGrade(totalTime)
        )
    }
    
    /**
     * 分析启动性能
     */
    private fun analyzeStartupPerformance() {
        val report = getStartupReport()
        
        Timber.i("=== 启动性能分析报告 ===")
        Timber.i("总启动时间: ${report.totalStartupTime}ms")
        Timber.i("Application初始化: ${report.applicationInitTime}ms")
        Timber.i("Activity创建: ${report.activityCreateTime}ms")
        Timber.i("首帧渲染: ${report.firstFrameRenderTime}ms")
        Timber.i("性能等级: ${report.performanceGrade}")
        
        // 性能建议
        providePerfomanceRecommendations(report)
    }
    
    /**
     * 提供性能优化建议
     */
    private fun providePerfomanceRecommendations(report: StartupReport) {
        val recommendations = mutableListOf<String>()
        
        if (report.applicationInitTime > 500) {
            recommendations.add("Application初始化时间过长，建议使用延迟初始化")
        }
        
        if (report.activityCreateTime > 300) {
            recommendations.add("Activity创建时间过长，检查onCreate中的同步操作")
        }
        
        if (report.firstFrameRenderTime > 200) {
            recommendations.add("首帧渲染时间过长，优化初始UI复杂度")
        }
        
        if (report.totalStartupTime > 2000) {
            recommendations.add("总启动时间过长，考虑实现冷启动优化")
        }
        
        if (recommendations.isNotEmpty()) {
            Timber.w("性能优化建议:")
            recommendations.forEach { recommendation ->
                Timber.w("- $recommendation")
            }
        }
    }
    
    /**
     * 计算性能等级
     */
    private fun calculatePerformanceGrade(totalTime: Long): PerformanceGrade {
        return when {
            totalTime <= 1000 -> PerformanceGrade.EXCELLENT
            totalTime <= 1500 -> PerformanceGrade.GOOD
            totalTime <= 2000 -> PerformanceGrade.FAIR
            totalTime <= 3000 -> PerformanceGrade.POOR
            else -> PerformanceGrade.VERY_POOR
        }
    }
    
    /**
     * 添加启动事件
     */
    private fun addEvent(eventName: String, description: String) {
        val currentTime = SystemClock.elapsedRealtime()
        val elapsedTime = currentTime - applicationStartTime
        
        startupEvents.add(
            StartupEvent(
                name = eventName,
                description = description,
                timestamp = currentTime,
                elapsedTime = elapsedTime
            )
        )
    }
    
    /**
     * 重置跟踪器
     */
    fun reset() {
        applicationStartTime = 0
        applicationCreatedTime = 0
        mainActivityCreatedTime = 0
        firstFrameRenderedTime = 0
        splashScreenDismissedTime = 0
        startupEvents.clear()
        Timber.d("启动时间跟踪器已重置")
    }
}

/**
 * 启动事件数据类
 */
data class StartupEvent(
    val name: String,
    val description: String,
    val timestamp: Long,
    val elapsedTime: Long
)

/**
 * 启动性能报告
 */
data class StartupReport(
    val totalStartupTime: Long,
    val applicationInitTime: Long,
    val activityCreateTime: Long,
    val firstFrameRenderTime: Long,
    val splashScreenDuration: Long,
    val events: List<StartupEvent>,
    val performanceGrade: PerformanceGrade
)

/**
 * 性能等级枚举
 */
enum class PerformanceGrade(val description: String) {
    EXCELLENT("优秀 (<1s)"),
    GOOD("良好 (1-1.5s)"),
    FAIR("一般 (1.5-2s)"),
    POOR("较差 (2-3s)"),
    VERY_POOR("很差 (>3s)")
}
