package com.cadence.core.database.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 数据库迁移：版本2到版本3
 * 添加标签功能相关表
 */
val MIGRATION_2_3 = object : Migration(2, 3) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建标签表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `tags` (
                `id` TEXT NOT NULL,
                `name` TEXT NOT NULL,
                `color` TEXT,
                `description` TEXT,
                `usage_count` INTEGER NOT NULL DEFAULT 0,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """)
        
        // 创建标签表索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_tags_name` ON `tags` (`name`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_tags_created_at` ON `tags` (`created_at`)")
        
        // 创建翻译标签关联表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `translation_tags` (
                `translation_id` TEXT NOT NULL,
                `tag_id` TEXT NOT NULL,
                `created_at` INTEGER NOT NULL,
                PRIMARY KEY(`translation_id`, `tag_id`)
            )
        """)
        
        // 创建翻译标签关联表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_translation_tags_translation_id` ON `translation_tags` (`translation_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_translation_tags_tag_id` ON `translation_tags` (`tag_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_translation_tags_created_at` ON `translation_tags` (`created_at`)")
        
        // 插入一些默认标签
        val currentTime = System.currentTimeMillis()
        val defaultTags = listOf(
            "工作" to "#2196F3",
            "学习" to "#4CAF50", 
            "生活" to "#FF9800",
            "重要" to "#F44336",
            "常用" to "#9C27B0"
        )
        
        defaultTags.forEachIndexed { index, (name, color) ->
            val id = "default_tag_${index + 1}"
            database.execSQL("""
                INSERT OR IGNORE INTO `tags` 
                (`id`, `name`, `color`, `description`, `usage_count`, `created_at`, `updated_at`) 
                VALUES ('$id', '$name', '$color', '系统预设标签', 0, $currentTime, $currentTime)
            """)
        }
    }
}
