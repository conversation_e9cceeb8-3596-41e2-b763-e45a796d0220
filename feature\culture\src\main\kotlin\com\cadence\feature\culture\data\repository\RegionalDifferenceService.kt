package com.cadence.feature.culture.data.repository

import com.cadence.core.database.dao.culture.CulturalContextDao
import com.cadence.domain.model.culture.*
import com.cadence.feature.culture.data.mapper.CulturalContextMapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 地域文化差异服务
 * 负责处理地域文化差异的分析、比较和说明功能
 */
@Singleton
class RegionalDifferenceService @Inject constructor(
    private val culturalContextDao: CulturalContextDao
) {

    /**
     * 获取指定单词在不同地区的文化差异
     */
    suspend fun getRegionalDifferences(word: String): Result<List<RegionalDifferenceComparison>> {
        return withContext(Dispatchers.IO) {
            try {
                // 获取该单词在所有地区的文化背景
                val culturalContexts = culturalContextDao.getCulturalContextsByWord(word)
                
                if (culturalContexts.isEmpty()) {
                    return@withContext Result.success(emptyList())
                }
                
                // 按地区分组
                val contextsByRegion = culturalContexts.groupBy { it.region }
                
                // 生成地域差异比较
                val comparisons = mutableListOf<RegionalDifferenceComparison>()
                
                contextsByRegion.forEach { (region, contexts) ->
                    contexts.forEach { context ->
                        // 获取该文化背景的地域差异
                        val regionalDifferences = culturalContextDao.getRegionalDifferencesByContextId(context.id)
                        
                        if (regionalDifferences.isNotEmpty()) {
                            val comparison = RegionalDifferenceComparison(
                                word = word,
                                baseRegion = region,
                                baseCulturalMeaning = context.culturalMeaning,
                                differences = regionalDifferences.map { diff ->
                                    RegionalDifferenceDetail(
                                        targetRegion = diff.region,
                                        difference = diff.difference,
                                        explanation = diff.explanation,
                                        severity = calculateDifferenceSeverity(diff.difference),
                                        examples = generateDifferenceExamples(context.id, diff.region)
                                    )
                                },
                                culturalImpact = calculateCulturalImpact(regionalDifferences),
                                usageRecommendations = generateUsageRecommendations(context, regionalDifferences)
                            )
                            comparisons.add(comparison)
                        }
                    }
                }
                
                Result.success(comparisons)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 获取地区间的文化差异统计
     */
    suspend fun getRegionalStatistics(): Result<RegionalStatistics> {
        return withContext(Dispatchers.IO) {
            try {
                val statistics = culturalContextDao.getRegionalStatistics()
                
                val regionalStats = RegionalStatistics(
                    totalRegions = statistics.totalRegions,
                    totalDifferences = statistics.totalDifferences,
                    regionDistribution = statistics.regionDistribution,
                    differenceTypeDistribution = statistics.differenceTypeDistribution,
                    averageDifferencesPerWord = statistics.averageDifferencesPerWord,
                    mostDiverseWords = statistics.mostDiverseWords,
                    regionalComplexityIndex = calculateRegionalComplexityIndex(statistics)
                )
                
                Result.success(regionalStats)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 搜索特定地区的文化特色
     */
    suspend fun searchRegionalFeatures(
        region: String,
        knowledgeType: CulturalKnowledgeType? = null
    ): Result<List<RegionalFeature>> {
        return withContext(Dispatchers.IO) {
            try {
                val contexts = if (knowledgeType != null) {
                    culturalContextDao.getCulturalContextsByRegionAndType(region, knowledgeType.name)
                } else {
                    culturalContextDao.getCulturalContextsByRegion(region)
                }
                
                val features = contexts.map { context ->
                    val usageContexts = culturalContextDao.getUsageContextsByContextId(context.id)
                    val examples = culturalContextDao.getCulturalExamplesByContextId(context.id)
                    
                    RegionalFeature(
                        word = context.word,
                        region = context.region,
                        culturalMeaning = context.culturalMeaning,
                        historicalBackground = context.historicalBackground,
                        uniqueCharacteristics = extractUniqueCharacteristics(context, usageContexts),
                        commonUsages = usageContexts.map { it.context },
                        culturalExamples = examples.map { 
                            CulturalExample(
                                id = it.id,
                                scenario = it.scenario,
                                originalText = it.originalText,
                                translatedText = it.translatedText,
                                culturalNote = it.culturalNote
                            )
                        },
                        knowledgeType = CulturalKnowledgeType.valueOf(context.knowledgeType),
                        difficulty = CulturalDifficulty.valueOf(context.difficulty)
                    )
                }
                
                Result.success(features)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * 生成地域文化学习建议
     */
    suspend fun generateLearningRecommendations(
        userRegion: String,
        targetRegion: String,
        difficulty: CulturalDifficulty
    ): Result<List<RegionalLearningRecommendation>> {
        return withContext(Dispatchers.IO) {
            try {
                // 获取两个地区的文化差异
                val differences = culturalContextDao.getRegionalDifferencesBetweenRegions(userRegion, targetRegion)
                
                val recommendations = differences
                    .filter { 
                        val contextDifficulty = culturalContextDao.getCulturalContextById(it.contextId)?.difficulty
                        contextDifficulty?.let { CulturalDifficulty.valueOf(it) } == difficulty
                    }
                    .map { difference ->
                        val context = culturalContextDao.getCulturalContextById(difference.contextId)!!
                        val usageContexts = culturalContextDao.getUsageContextsByContextId(context.id)
                        
                        RegionalLearningRecommendation(
                            word = context.word,
                            fromRegion = userRegion,
                            toRegion = targetRegion,
                            keyDifference = difference.difference,
                            learningTips = generateLearningTips(difference, usageContexts),
                            practiceExamples = generatePracticeExamples(context, difference),
                            commonMistakes = generateCommonMistakes(difference),
                            culturalContext = difference.explanation,
                            priority = calculateLearningPriority(difference, context)
                        )
                    }
                    .sortedByDescending { it.priority }
                
                Result.success(recommendations)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    // 私有辅助方法

    /**
     * 计算文化差异的严重程度
     */
    private fun calculateDifferenceSeverity(difference: String): DifferenceSeverity {
        val keywords = difference.lowercase()
        return when {
            keywords.contains("完全不同") || keywords.contains("相反") || keywords.contains("禁忌") -> DifferenceSeverity.HIGH
            keywords.contains("显著") || keywords.contains("重要") || keywords.contains("明显") -> DifferenceSeverity.MEDIUM
            else -> DifferenceSeverity.LOW
        }
    }

    /**
     * 生成差异示例
     */
    private suspend fun generateDifferenceExamples(contextId: String, region: String): List<String> {
        val examples = culturalContextDao.getCulturalExamplesByContextId(contextId)
        return examples
            .filter { it.culturalNote.contains(region, ignoreCase = true) }
            .map { "${it.scenario}: ${it.culturalNote}" }
            .take(3)
    }

    /**
     * 计算文化影响程度
     */
    private fun calculateCulturalImpact(differences: List<com.cadence.core.database.entity.culture.RegionalDifferenceEntity>): CulturalImpact {
        val highSeverityCount = differences.count { calculateDifferenceSeverity(it.difference) == DifferenceSeverity.HIGH }
        val totalCount = differences.size
        
        return when {
            highSeverityCount > totalCount * 0.5 -> CulturalImpact.CRITICAL
            highSeverityCount > totalCount * 0.3 -> CulturalImpact.SIGNIFICANT
            highSeverityCount > 0 -> CulturalImpact.MODERATE
            else -> CulturalImpact.MINIMAL
        }
    }

    /**
     * 生成使用建议
     */
    private suspend fun generateUsageRecommendations(
        context: com.cadence.core.database.entity.culture.CulturalContextEntity,
        differences: List<com.cadence.core.database.entity.culture.RegionalDifferenceEntity>
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于地域差异生成建议
        differences.forEach { diff ->
            when (calculateDifferenceSeverity(diff.difference)) {
                DifferenceSeverity.HIGH -> {
                    recommendations.add("在${diff.region}使用时需要特别注意：${diff.explanation}")
                }
                DifferenceSeverity.MEDIUM -> {
                    recommendations.add("在${diff.region}建议：${diff.explanation}")
                }
                DifferenceSeverity.LOW -> {
                    recommendations.add("在${diff.region}可以考虑：${diff.explanation}")
                }
            }
        }
        
        return recommendations.take(5)
    }

    /**
     * 计算地域复杂度指数
     */
    private fun calculateRegionalComplexityIndex(statistics: com.cadence.core.database.entity.culture.RegionalStatisticsEntity): Double {
        return (statistics.totalDifferences.toDouble() / statistics.totalRegions) * 
               (statistics.averageDifferencesPerWord / 10.0)
    }

    /**
     * 提取独特特征
     */
    private fun extractUniqueCharacteristics(
        context: com.cadence.core.database.entity.culture.CulturalContextEntity,
        usageContexts: List<com.cadence.core.database.entity.culture.UsageContextEntity>
    ): List<String> {
        val characteristics = mutableListOf<String>()
        
        // 从文化含义中提取特征
        if (context.culturalMeaning.contains("独特") || context.culturalMeaning.contains("特有")) {
            characteristics.add("地域特有表达")
        }
        
        // 从使用场景中提取特征
        usageContexts.forEach { usage ->
            if (usage.appropriateness == "HIGH") {
                characteristics.add("高度适用：${usage.context}")
            }
        }
        
        return characteristics.take(3)
    }

    /**
     * 生成学习技巧
     */
    private fun generateLearningTips(
        difference: com.cadence.core.database.entity.culture.RegionalDifferenceEntity,
        usageContexts: List<com.cadence.core.database.entity.culture.UsageContextEntity>
    ): List<String> {
        val tips = mutableListOf<String>()
        
        tips.add("重点理解：${difference.explanation}")
        
        usageContexts.forEach { usage ->
            tips.add("在${usage.context}场景下要注意文化差异")
        }
        
        return tips.take(3)
    }

    /**
     * 生成练习示例
     */
    private suspend fun generatePracticeExamples(
        context: com.cadence.core.database.entity.culture.CulturalContextEntity,
        difference: com.cadence.core.database.entity.culture.RegionalDifferenceEntity
    ): List<String> {
        val examples = culturalContextDao.getCulturalExamplesByContextId(context.id)
        return examples
            .map { "练习：${it.scenario} - ${it.culturalNote}" }
            .take(2)
    }

    /**
     * 生成常见错误
     */
    private fun generateCommonMistakes(
        difference: com.cadence.core.database.entity.culture.RegionalDifferenceEntity
    ): List<String> {
        return listOf(
            "忽视地域差异直接使用",
            "混淆不同地区的文化含义",
            "在不合适的场合使用"
        )
    }

    /**
     * 计算学习优先级
     */
    private fun calculateLearningPriority(
        difference: com.cadence.core.database.entity.culture.RegionalDifferenceEntity,
        context: com.cadence.core.database.entity.culture.CulturalContextEntity
    ): Int {
        var priority = 0
        
        // 基于差异严重程度
        priority += when (calculateDifferenceSeverity(difference.difference)) {
            DifferenceSeverity.HIGH -> 10
            DifferenceSeverity.MEDIUM -> 5
            DifferenceSeverity.LOW -> 2
        }
        
        // 基于难度
        priority += when (CulturalDifficulty.valueOf(context.difficulty)) {
            CulturalDifficulty.BEGINNER -> 1
            CulturalDifficulty.INTERMEDIATE -> 3
            CulturalDifficulty.ADVANCED -> 5
            CulturalDifficulty.EXPERT -> 7
        }
        
        return priority
    }
}

// 数据类定义

/**
 * 地域差异比较
 */
data class RegionalDifferenceComparison(
    val word: String,
    val baseRegion: String,
    val baseCulturalMeaning: String,
    val differences: List<RegionalDifferenceDetail>,
    val culturalImpact: CulturalImpact,
    val usageRecommendations: List<String>
)

/**
 * 地域差异详情
 */
data class RegionalDifferenceDetail(
    val targetRegion: String,
    val difference: String,
    val explanation: String,
    val severity: DifferenceSeverity,
    val examples: List<String>
)

/**
 * 地域统计信息
 */
data class RegionalStatistics(
    val totalRegions: Int,
    val totalDifferences: Int,
    val regionDistribution: Map<String, Int>,
    val differenceTypeDistribution: Map<String, Int>,
    val averageDifferencesPerWord: Double,
    val mostDiverseWords: List<String>,
    val regionalComplexityIndex: Double
)

/**
 * 地域特色
 */
data class RegionalFeature(
    val word: String,
    val region: String,
    val culturalMeaning: String,
    val historicalBackground: String,
    val uniqueCharacteristics: List<String>,
    val commonUsages: List<String>,
    val culturalExamples: List<CulturalExample>,
    val knowledgeType: CulturalKnowledgeType,
    val difficulty: CulturalDifficulty
)

/**
 * 地域学习建议
 */
data class RegionalLearningRecommendation(
    val word: String,
    val fromRegion: String,
    val toRegion: String,
    val keyDifference: String,
    val learningTips: List<String>,
    val practiceExamples: List<String>,
    val commonMistakes: List<String>,
    val culturalContext: String,
    val priority: Int
)

/**
 * 差异严重程度
 */
enum class DifferenceSeverity {
    LOW, MEDIUM, HIGH
}

/**
 * 文化影响程度
 */
enum class CulturalImpact {
    MINIMAL, MODERATE, SIGNIFICANT, CRITICAL
}