package com.cadence.core.database.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 数据库迁移：版本5到版本6
 * 添加学习功能相关表：words, learning_progress, study_sessions
 */
val MIGRATION_5_6 = object : Migration(5, 6) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建单词表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `words` (
                `id` TEXT NOT NULL,
                `text` TEXT NOT NULL,
                `language` TEXT NOT NULL,
                `pronunciation` TEXT,
                `definition` TEXT NOT NULL,
                `example` TEXT,
                `translation` TEXT NOT NULL,
                `translation_language` TEXT NOT NULL,
                `difficulty` TEXT NOT NULL,
                `category` TEXT NOT NULL,
                `tags` TEXT NOT NULL,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """)
        
        // 创建学习进度表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `learning_progress` (
                `id` TEXT NOT NULL,
                `user_id` TEXT NOT NULL,
                `word_id` TEXT NOT NULL,
                `mastery_level` TEXT NOT NULL,
                `correct_answers` INTEGER NOT NULL DEFAULT 0,
                `total_attempts` INTEGER NOT NULL DEFAULT 0,
                `last_studied_at` INTEGER,
                `next_review_at` INTEGER,
                `study_streak` INTEGER NOT NULL DEFAULT 0,
                `is_bookmarked` INTEGER NOT NULL DEFAULT 0,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`),
                FOREIGN KEY(`word_id`) REFERENCES `words`(`id`) ON DELETE CASCADE
            )
        """)
        
        // 创建学习会话表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `study_sessions` (
                `id` TEXT NOT NULL,
                `user_id` TEXT NOT NULL,
                `session_type` TEXT NOT NULL,
                `start_time` INTEGER NOT NULL,
                `end_time` INTEGER,
                `words_studied` TEXT NOT NULL,
                `correct_answers` INTEGER NOT NULL DEFAULT 0,
                `total_questions` INTEGER NOT NULL DEFAULT 0,
                `time_spent` INTEGER NOT NULL DEFAULT 0,
                `created_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """)
        
        // 创建学习进度表的索引
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_learning_progress_user_id` ON `learning_progress` (`user_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_learning_progress_word_id` ON `learning_progress` (`word_id`)")
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_learning_progress_user_id_word_id` ON `learning_progress` (`user_id`, `word_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_learning_progress_next_review_at` ON `learning_progress` (`next_review_at`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_learning_progress_mastery_level` ON `learning_progress` (`mastery_level`)")
        
        // 创建学习会话表的索引
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_study_sessions_user_id` ON `study_sessions` (`user_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_study_sessions_session_type` ON `study_sessions` (`session_type`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_study_sessions_start_time` ON `study_sessions` (`start_time`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_study_sessions_created_at` ON `study_sessions` (`created_at`)")
    }
}