package com.cadence.data.repository

import com.cadence.core.database.dao.TagDao
import com.cadence.core.database.entity.TagEntity
import com.cadence.core.database.entity.TranslationTagEntity
import com.cadence.data.mapper.toDomain
import com.cadence.data.mapper.toDomainList
import com.cadence.domain.model.*
import com.cadence.domain.repository.TagRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 标签仓库实现
 * 处理标签相关的数据操作
 */
@Singleton
class TagRepositoryImpl @Inject constructor(
    private val tagDao: TagDao
) : TagRepository {
    
    override fun getAllTags(): Flow<List<Tag>> {
        return tagDao.getAllTags().map { it.toDomainList() }
    }
    
    override suspend fun getTagById(tagId: String): Result<Tag?> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = tagDao.getTagById(tagId)
                Result.success(entity?.toDomain())
            } catch (e: Exception) {
                Timber.e(e, "获取标签失败: $tagId")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun getTagByName(name: String): Result<Tag?> {
        return withContext(Dispatchers.IO) {
            try {
                val entity = tagDao.getTagByName(name)
                Result.success(entity?.toDomain())
            } catch (e: Exception) {
                Timber.e(e, "根据名称获取标签失败: $name")
                Result.failure(e)
            }
        }
    }
    
    override fun searchTags(query: String): Flow<List<Tag>> {
        return tagDao.searchTags(query).map { it.toDomainList() }
    }
    
    override fun getPopularTags(limit: Int): Flow<List<Tag>> {
        return tagDao.getPopularTags(limit).map { it.toDomainList() }
    }
    
    override suspend fun createTag(request: CreateTagRequest): Result<Tag> {
        return withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                val entity = TagEntity(
                    id = UUID.randomUUID().toString(),
                    name = request.name,
                    color = request.color,
                    description = request.description,
                    usageCount = 0,
                    createdAt = currentTime,
                    updatedAt = currentTime
                )
                
                tagDao.insertTag(entity)
                Timber.d("创建标签成功: ${request.name}")
                Result.success(entity.toDomain())
            } catch (e: Exception) {
                Timber.e(e, "创建标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun updateTag(tagId: String, request: UpdateTagRequest): Result<Tag> {
        return withContext(Dispatchers.IO) {
            try {
                val existingEntity = tagDao.getTagById(tagId)
                    ?: return@withContext Result.failure(IllegalArgumentException("标签不存在"))
                
                val updatedEntity = existingEntity.copy(
                    name = request.name ?: existingEntity.name,
                    color = request.color ?: existingEntity.color,
                    description = request.description ?: existingEntity.description,
                    updatedAt = System.currentTimeMillis()
                )
                
                tagDao.updateTag(updatedEntity)
                Timber.d("更新标签成功: $tagId")
                Result.success(updatedEntity.toDomain())
            } catch (e: Exception) {
                Timber.e(e, "更新标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun deleteTag(tagId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // 先删除所有关联
                tagDao.removeAllTranslationsFromTag(tagId)
                // 再删除标签
                tagDao.deleteTagById(tagId)
                Timber.d("删除标签成功: $tagId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "删除标签失败")
                Result.failure(e)
            }
        }
    }
    
    override fun getTagsForTranslation(translationId: String): Flow<List<Tag>> {
        return tagDao.getTagsForTranslation(translationId).map { it.toDomainList() }
    }
    
    override fun getTranslationIdsForTag(tagId: String): Flow<List<String>> {
        return tagDao.getTranslationIdsForTag(tagId)
    }
    
    override suspend fun addTagToTranslation(translationId: String, tagId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // 检查是否已存在关联
                if (tagDao.hasTag(translationId, tagId)) {
                    return@withContext Result.success(Unit) // 已存在，直接返回成功
                }
                
                val translationTag = TranslationTagEntity(
                    translationId = translationId,
                    tagId = tagId,
                    createdAt = System.currentTimeMillis()
                )
                
                tagDao.addTagToTranslation(translationTag)
                tagDao.incrementUsageCount(tagId, System.currentTimeMillis())
                
                Timber.d("为翻译添加标签成功: $translationId -> $tagId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "为翻译添加标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun addTagsToTranslation(translationId: String, tagIds: List<String>): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                val translationTags = tagIds.map { tagId ->
                    TranslationTagEntity(
                        translationId = translationId,
                        tagId = tagId,
                        createdAt = currentTime
                    )
                }
                
                tagDao.addTagsToTranslation(translationTags)
                
                // 更新使用次数
                tagIds.forEach { tagId ->
                    tagDao.incrementUsageCount(tagId, currentTime)
                }
                
                Timber.d("批量为翻译添加标签成功: $translationId -> ${tagIds.size}个标签")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "批量为翻译添加标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun removeTagFromTranslation(translationId: String, tagId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                tagDao.removeTagFromTranslation(translationId, tagId)
                tagDao.decrementUsageCount(tagId, System.currentTimeMillis())
                
                Timber.d("从翻译中移除标签成功: $translationId -> $tagId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "从翻译中移除标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun removeAllTagsFromTranslation(translationId: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                // 先获取所有标签ID，用于减少使用次数
                val tagIds = tagDao.getTranslationIdsForTag(translationId)
                
                tagDao.removeAllTagsFromTranslation(translationId)
                
                // 减少使用次数
                val currentTime = System.currentTimeMillis()
                tagIds.collect { ids ->
                    ids.forEach { tagId ->
                        tagDao.decrementUsageCount(tagId, currentTime)
                    }
                }
                
                Timber.d("移除翻译的所有标签成功: $translationId")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "移除翻译的所有标签失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun hasTag(translationId: String, tagId: String): Result<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                val hasTag = tagDao.hasTag(translationId, tagId)
                Result.success(hasTag)
            } catch (e: Exception) {
                Timber.e(e, "检查标签关联失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun getTagStatistics(): Result<TagStatistics> {
        return withContext(Dispatchers.IO) {
            try {
                val totalTags = tagDao.getTagCount()
                val allTags = tagDao.getAllTags()
                
                allTags.collect { tags ->
                    val totalUsage = tags.sumOf { it.usageCount }
                    val mostUsedTag = tags.maxByOrNull { it.usageCount }?.toDomain()
                    val recentTags = tags.sortedByDescending { it.createdAt }
                        .take(5)
                        .map { it.toDomain() }
                    
                    val statistics = TagStatistics(
                        totalTags = totalTags,
                        totalUsage = totalUsage,
                        mostUsedTag = mostUsedTag,
                        recentTags = recentTags
                    )
                    
                    return@withContext Result.success(statistics)
                }
                
                Result.failure(IllegalStateException("无法获取标签统计信息"))
            } catch (e: Exception) {
                Timber.e(e, "获取标签统计信息失败")
                Result.failure(e)
            }
        }
    }
    
    override suspend fun cleanupUnusedTags(): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                tagDao.cleanupUnusedTags()
                Timber.d("清理未使用标签成功")
                Result.success(Unit)
            } catch (e: Exception) {
                Timber.e(e, "清理未使用标签失败")
                Result.failure(e)
            }
        }
    }
}
