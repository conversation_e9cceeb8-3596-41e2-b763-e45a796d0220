package com.cadence.core.speech

import android.content.Context
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.audiofx.AcousticEchoCanceler
import android.media.audiofx.AutomaticGainControl
import android.media.audiofx.NoiseSuppressor
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs
import kotlin.math.sqrt

/**
 * 音频处理器
 * 提供音频降噪、增益控制和质量优化功能
 */
@Singleton
class AudioProcessor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var audioRecord: AudioRecord? = null
    private var noiseSuppressor: NoiseSuppressor? = null
    private var acousticEchoCanceler: AcousticEchoCanceler? = null
    private var automaticGainControl: AutomaticGainControl? = null
    
    companion object {
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE_FACTOR = 2
        
        // 音频质量阈值
        private const val MIN_VOLUME_THRESHOLD = 0.01f
        private const val MAX_VOLUME_THRESHOLD = 0.8f
        private const val NOISE_FLOOR_THRESHOLD = 0.05f
    }
    
    /**
     * 音频质量评估结果
     */
    data class AudioQuality(
        val volume: Float,
        val noiseLevel: Float,
        val clarity: Float,
        val isGoodQuality: Boolean
    ) {
        val qualityScore: Float
            get() = (volume * 0.3f + (1 - noiseLevel) * 0.4f + clarity * 0.3f).coerceIn(0f, 1f)
        
        val qualityLevel: QualityLevel
            get() = when {
                qualityScore >= 0.8f -> QualityLevel.EXCELLENT
                qualityScore >= 0.6f -> QualityLevel.GOOD
                qualityScore >= 0.4f -> QualityLevel.FAIR
                else -> QualityLevel.POOR
            }
    }
    
    enum class QualityLevel {
        EXCELLENT, GOOD, FAIR, POOR
    }
    
    /**
     * 初始化音频处理器
     */
    fun initialize(): Boolean {
        return try {
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            ) * BUFFER_SIZE_FACTOR
            
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT,
                bufferSize
            )
            
            if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
                Timber.e("AudioRecord初始化失败")
                return false
            }
            
            setupAudioEffects()
            true
        } catch (e: Exception) {
            Timber.e(e, "音频处理器初始化失败")
            false
        }
    }
    
    /**
     * 设置音频效果
     */
    private fun setupAudioEffects() {
        audioRecord?.audioSessionId?.let { sessionId ->
            try {
                // 噪声抑制
                if (NoiseSuppressor.isAvailable()) {
                    noiseSuppressor = NoiseSuppressor.create(sessionId)?.apply {
                        enabled = true
                    }
                    Timber.d("噪声抑制器已启用")
                }
                
                // 回声消除
                if (AcousticEchoCanceler.isAvailable()) {
                    acousticEchoCanceler = AcousticEchoCanceler.create(sessionId)?.apply {
                        enabled = true
                    }
                    Timber.d("回声消除器已启用")
                }
                
                // 自动增益控制
                if (AutomaticGainControl.isAvailable()) {
                    automaticGainControl = AutomaticGainControl.create(sessionId)?.apply {
                        enabled = true
                    }
                    Timber.d("自动增益控制已启用")
                }
            } catch (e: Exception) {
                Timber.w(e, "音频效果设置失败")
            }
        }
    }
    
    /**
     * 检查音频质量
     */
    fun checkAudioQuality(): Flow<AudioQuality> = flow {
        if (audioRecord?.state != AudioRecord.STATE_INITIALIZED) {
            emit(AudioQuality(0f, 1f, 0f, false))
            return@flow
        }
        
        try {
            audioRecord?.startRecording()
            
            val bufferSize = AudioRecord.getMinBufferSize(
                SAMPLE_RATE,
                CHANNEL_CONFIG,
                AUDIO_FORMAT
            )
            val buffer = ShortArray(bufferSize)
            
            // 采集音频样本进行质量分析
            repeat(10) { // 采集10次样本
                val readBytes = audioRecord?.read(buffer, 0, bufferSize) ?: 0
                if (readBytes > 0) {
                    val quality = analyzeAudioQuality(buffer, readBytes)
                    emit(quality)
                }
                
                Thread.sleep(100) // 100ms间隔
            }
        } catch (e: Exception) {
            Timber.e(e, "音频质量检查失败")
            emit(AudioQuality(0f, 1f, 0f, false))
        } finally {
            audioRecord?.stop()
        }
    }
    
    /**
     * 分析音频质量
     */
    private fun analyzeAudioQuality(buffer: ShortArray, length: Int): AudioQuality {
        if (length <= 0) {
            return AudioQuality(0f, 1f, 0f, false)
        }
        
        // 计算音量（RMS）
        var sum = 0.0
        for (i in 0 until length) {
            sum += buffer[i] * buffer[i]
        }
        val rms = sqrt(sum / length)
        val volume = (rms / Short.MAX_VALUE).toFloat().coerceIn(0f, 1f)
        
        // 计算噪声水平（零交叉率）
        var zeroCrossings = 0
        for (i in 1 until length) {
            if ((buffer[i] >= 0) != (buffer[i - 1] >= 0)) {
                zeroCrossings++
            }
        }
        val zeroCrossingRate = zeroCrossings.toFloat() / length
        val noiseLevel = (zeroCrossingRate * 2).coerceIn(0f, 1f)
        
        // 计算清晰度（信噪比估算）
        val signalPower = volume
        val noisePower = noiseLevel.coerceAtLeast(NOISE_FLOOR_THRESHOLD)
        val snr = signalPower / noisePower
        val clarity = (snr / 10f).coerceIn(0f, 1f)
        
        // 判断是否为良好质量
        val isGoodQuality = volume >= MIN_VOLUME_THRESHOLD &&
                volume <= MAX_VOLUME_THRESHOLD &&
                noiseLevel <= 0.3f &&
                clarity >= 0.5f
        
        return AudioQuality(volume, noiseLevel, clarity, isGoodQuality)
    }
    
    /**
     * 获取音频设备信息
     */
    fun getAudioDeviceInfo(): AudioDeviceInfo {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        
        return AudioDeviceInfo(
            hasBuiltInMic = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS).any { 
                it.type == android.media.AudioDeviceInfo.TYPE_BUILTIN_MIC 
            },
            hasBluetoothMic = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS).any { 
                it.type == android.media.AudioDeviceInfo.TYPE_BLUETOOTH_SCO 
            },
            hasWiredHeadset = audioManager.getDevices(AudioManager.GET_DEVICES_INPUTS).any { 
                it.type == android.media.AudioDeviceInfo.TYPE_WIRED_HEADSET 
            },
            currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC),
            maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC),
            isMicrophoneMute = audioManager.isMicrophoneMute
        )
    }
    
    /**
     * 优化录音设置
     */
    fun optimizeRecordingSettings(): RecordingOptimization {
        val deviceInfo = getAudioDeviceInfo()
        
        return RecordingOptimization(
            recommendedSampleRate = when {
                deviceInfo.hasBluetoothMic -> 8000 // 蓝牙设备通常支持较低采样率
                deviceInfo.hasWiredHeadset -> 44100 // 有线耳机支持高质量
                else -> 16000 // 内置麦克风默认
            },
            recommendedChannels = if (deviceInfo.hasWiredHeadset) 2 else 1,
            enableNoiseReduction = !deviceInfo.hasWiredHeadset, // 有线耳机通常质量较好
            enableEchoCancellation = !deviceInfo.hasWiredHeadset,
            recommendedGain = when {
                deviceInfo.isMicrophoneMute -> 0f
                deviceInfo.currentVolume < deviceInfo.maxVolume * 0.3f -> 1.5f
                deviceInfo.currentVolume > deviceInfo.maxVolume * 0.8f -> 0.8f
                else -> 1.0f
            }
        )
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            noiseSuppressor?.release()
            acousticEchoCanceler?.release()
            automaticGainControl?.release()
            audioRecord?.release()
        } catch (e: Exception) {
            Timber.e(e, "释放音频处理器资源失败")
        } finally {
            noiseSuppressor = null
            acousticEchoCanceler = null
            automaticGainControl = null
            audioRecord = null
        }
    }
}

/**
 * 音频设备信息
 */
data class AudioDeviceInfo(
    val hasBuiltInMic: Boolean,
    val hasBluetoothMic: Boolean,
    val hasWiredHeadset: Boolean,
    val currentVolume: Int,
    val maxVolume: Int,
    val isMicrophoneMute: Boolean
)

/**
 * 录音优化建议
 */
data class RecordingOptimization(
    val recommendedSampleRate: Int,
    val recommendedChannels: Int,
    val enableNoiseReduction: Boolean,
    val enableEchoCancellation: Boolean,
    val recommendedGain: Float
)
