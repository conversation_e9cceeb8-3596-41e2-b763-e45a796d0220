package com.cadence.core.translation

import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 方言处理器
 * 处理不同地区的方言特色和语言变体
 */
@Singleton
class DialectProcessor @Inject constructor() {
    
    /**
     * 获取方言映射规则
     */
    fun getDialectMappings(
        sourceLanguage: Language,
        targetLanguage: Language,
        sourceRegion: Region?,
        targetRegion: Region?
    ): List<DialectMapping> {
        val mappings = mutableListOf<DialectMapping>()
        
        // 中文方言处理
        if (targetLanguage.code == "zh") {
            mappings.addAll(getChineseDialectMappings(targetRegion?.code))
        }
        
        // 英语方言处理
        if (targetLanguage.code == "en") {
            mappings.addAll(getEnglishDialectMappings(targetRegion?.code))
        }
        
        // 西班牙语方言处理
        if (targetLanguage.code == "es") {
            mappings.addAll(getSpanishDialectMappings(targetRegion?.code))
        }
        
        // 阿拉伯语方言处理
        if (targetLanguage.code == "ar") {
            mappings.addAll(getArabicDialectMappings(targetRegion?.code))
        }
        
        // 法语方言处理
        if (targetLanguage.code == "fr") {
            mappings.addAll(getFrenchDialectMappings(targetRegion?.code))
        }
        
        // 葡萄牙语方言处理
        if (targetLanguage.code == "pt") {
            mappings.addAll(getPortugueseDialectMappings(targetRegion?.code))
        }
        
        Timber.d("为${sourceLanguage.code}->${targetLanguage.code}(${sourceRegion?.code}->${targetRegion?.code})生成了${mappings.size}个方言映射")
        
        return mappings
    }
    
    /**
     * 中文方言映射
     */
    private fun getChineseDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "TW" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "使用繁体中文字符",
                    examples = listOf("计算机→電腦", "软件→軟體", "网络→網路")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "台湾地区特有表达方式",
                    examples = listOf("很棒→超讚", "厉害→厲害", "没问题→沒問題")
                ),
                DialectMapping(
                    type = DialectType.POLITENESS,
                    description = "台湾地区的礼貌用语习惯",
                    examples = listOf("谢谢→謝謝", "不好意思→不好意思", "麻烦您→麻煩您")
                )
            )
            
            "HK" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "香港粤语影响的用词",
                    examples = listOf("出租车→的士", "公交车→巴士", "电梯→升降機")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "香港地区特色表达",
                    examples = listOf("很好→好正", "没关系→冇問題", "再见→拜拜")
                )
            )
            
            "SG" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "新加坡华语特色用词",
                    examples = listOf("组屋→組屋", "巴刹→巴刹", "咖啡店→咖啡店")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "新加坡式华语表达",
                    examples = listOf("可以→可以", "没有→沒有", "这样→這樣")
                )
            )
            
            else -> emptyList()
        }
    }
    
    /**
     * 英语方言映射
     */
    private fun getEnglishDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "UK" -> listOf(
                DialectMapping(
                    type = DialectType.SPELLING,
                    description = "英式拼写",
                    examples = listOf("color→colour", "center→centre", "realize→realise")
                ),
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "英式英语词汇",
                    examples = listOf("elevator→lift", "apartment→flat", "truck→lorry")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "英式表达习惯",
                    examples = listOf("I guess→I reckon", "awesome→brilliant", "guys→chaps")
                )
            )
            
            "US" -> listOf(
                DialectMapping(
                    type = DialectType.SPELLING,
                    description = "美式拼写",
                    examples = listOf("colour→color", "centre→center", "realise→realize")
                ),
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "美式英语词汇",
                    examples = listOf("lift→elevator", "flat→apartment", "lorry→truck")
                )
            )
            
            "AU" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "澳式英语词汇",
                    examples = listOf("afternoon→arvo", "breakfast→brekkie", "definitely→defo")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "澳式表达习惯",
                    examples = listOf("How are you?→How ya going?", "thank you→cheers", "sure→too right")
                )
            )
            
            else -> emptyList()
        }
    }
    
    /**
     * 西班牙语方言映射
     */
    private fun getSpanishDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "MX" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "墨西哥西班牙语词汇",
                    examples = listOf("coche→carro", "ordenador→computadora", "móvil→celular")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "墨西哥式表达",
                    examples = listOf("¿Qué tal?→¿Qué onda?", "vale→órale", "genial→padrísimo")
                )
            )
            
            "AR" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "阿根廷西班牙语词汇",
                    examples = listOf("coche→auto", "patata→papa", "zumo→jugo")
                ),
                DialectMapping(
                    type = DialectType.PRONUNCIATION,
                    description = "阿根廷口音特色",
                    examples = listOf("ll/y音变化", "vos代替tú", "che的使用")
                )
            )
            
            else -> emptyList()
        }
    }
    
    /**
     * 阿拉伯语方言映射
     */
    private fun getArabicDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "EG" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "埃及阿拉伯语词汇",
                    examples = listOf("كيف حالك→إزيك", "ماذا→إيه", "جيد→كويس")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "埃及方言表达",
                    examples = listOf("مرحبا→أهلا وسهلا", "شكرا→متشكر", "معذرة→آسف")
                )
            )
            
            "SA" -> listOf(
                DialectMapping(
                    type = DialectType.FORMALITY,
                    description = "沙特阿拉伯正式用语",
                    examples = listOf("使用标准阿拉伯语", "正式称谓", "宗教用语")
                )
            )
            
            else -> emptyList()
        }
    }
    
    /**
     * 法语方言映射
     */
    private fun getFrenchDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "CA" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "加拿大法语词汇",
                    examples = listOf("email→courriel", "weekend→fin de semaine", "parking→stationnement")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "魁北克法语表达",
                    examples = listOf("au revoir→à la prochaine", "merci→merci bien", "bonjour→salut")
                )
            )
            
            "BE" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "比利时法语特色",
                    examples = listOf("septante→soixante-dix", "nonante→quatre-vingt-dix", "déjeuner→dîner")
                )
            )
            
            else -> emptyList()
        }
    }
    
    /**
     * 葡萄牙语方言映射
     */
    private fun getPortugueseDialectMappings(regionCode: String?): List<DialectMapping> {
        return when (regionCode) {
            "BR" -> listOf(
                DialectMapping(
                    type = DialectType.VOCABULARY,
                    description = "巴西葡萄牙语词汇",
                    examples = listOf("comboio→trem", "telemóvel→celular", "casa de banho→banheiro")
                ),
                DialectMapping(
                    type = DialectType.PRONUNCIATION,
                    description = "巴西葡萄牙语发音特色",
                    examples = listOf("鼻音化", "开放元音", "辅音软化")
                ),
                DialectMapping(
                    type = DialectType.EXPRESSION,
                    description = "巴西式表达",
                    examples = listOf("obrigado→valeu", "como está?→como vai?", "tchau→até logo")
                )
            )
            
            else -> emptyList()
        }
    }
}

/**
 * 方言映射规则
 */
data class DialectMapping(
    val type: DialectType,
    val description: String,
    val examples: List<String> = emptyList()
)

/**
 * 方言类型
 */
enum class DialectType {
    VOCABULARY,     // 词汇差异
    SPELLING,       // 拼写差异
    PRONUNCIATION,  // 发音差异
    EXPRESSION,     // 表达习惯
    FORMALITY,      // 正式程度
    POLITENESS      // 礼貌用语
}
