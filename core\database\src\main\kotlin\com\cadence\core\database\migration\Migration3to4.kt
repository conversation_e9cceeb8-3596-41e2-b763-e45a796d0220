package com.cadence.core.database.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import java.util.UUID

/**
 * 数据库版本3到4的迁移
 * 添加同步功能相关的表
 */
val MIGRATION_3_4 = object : Migration(3, 4) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建同步配置表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS sync_config (
                id TEXT NOT NULL PRIMARY KEY,
                isEnabled INTEGER NOT NULL DEFAULT 0,
                autoSyncEnabled INTEGER NOT NULL DEFAULT 1,
                syncInterval INTEGER NOT NULL DEFAULT 3600000,
                wifiOnlySync INTEGER NOT NULL DEFAULT 1,
                lastSyncTime INTEGER NOT NULL DEFAULT 0,
                userId TEXT,
                deviceId TEXT NOT NULL,
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建同步历史表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS sync_history (
                id TEXT NOT NULL PRIMARY KEY,
                syncType TEXT NOT NULL,
                status TEXT NOT NULL,
                startTime INTEGER NOT NULL,
                endTime INTEGER NOT NULL,
                uploadedCount INTEGER NOT NULL DEFAULT 0,
                downloadedCount INTEGER NOT NULL DEFAULT 0,
                conflictCount INTEGER NOT NULL DEFAULT 0,
                errorMessage TEXT,
                details TEXT,
                createdAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建同步历史表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_history_syncType ON sync_history(syncType)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_history_status ON sync_history(status)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_history_startTime ON sync_history(startTime)")
        
        // 创建同步冲突表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS sync_conflicts (
                id TEXT NOT NULL PRIMARY KEY,
                itemId TEXT NOT NULL,
                itemType TEXT NOT NULL,
                conflictType TEXT NOT NULL,
                localData TEXT NOT NULL,
                remoteData TEXT NOT NULL,
                resolved INTEGER NOT NULL DEFAULT 0,
                resolution TEXT,
                createdAt INTEGER NOT NULL,
                resolvedAt INTEGER
            )
        """.trimIndent())
        
        // 创建同步冲突表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_conflicts_itemId ON sync_conflicts(itemId)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_conflicts_conflictType ON sync_conflicts(conflictType)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_conflicts_resolved ON sync_conflicts(resolved)")
        
        // 创建设备信息表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS device_info (
                id TEXT NOT NULL PRIMARY KEY,
                deviceId TEXT NOT NULL,
                deviceName TEXT NOT NULL,
                platform TEXT NOT NULL DEFAULT 'Android',
                appVersion TEXT NOT NULL,
                lastActiveTime INTEGER NOT NULL,
                isCurrentDevice INTEGER NOT NULL DEFAULT 0,
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建设备信息表索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS index_device_info_deviceId ON device_info(deviceId)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_device_info_isCurrentDevice ON device_info(isCurrentDevice)")
        
        // 创建同步队列表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS sync_queue (
                id TEXT NOT NULL PRIMARY KEY,
                itemId TEXT NOT NULL,
                itemType TEXT NOT NULL,
                action TEXT NOT NULL,
                priority INTEGER NOT NULL DEFAULT 0,
                data TEXT NOT NULL,
                retryCount INTEGER NOT NULL DEFAULT 0,
                maxRetries INTEGER NOT NULL DEFAULT 3,
                lastError TEXT,
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建同步队列表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_queue_itemType ON sync_queue(itemType)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_queue_action ON sync_queue(action)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_queue_priority ON sync_queue(priority)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_sync_queue_createdAt ON sync_queue(createdAt)")
        
        // 创建同步统计表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS sync_statistics (
                id TEXT NOT NULL PRIMARY KEY,
                totalSyncs INTEGER NOT NULL DEFAULT 0,
                successfulSyncs INTEGER NOT NULL DEFAULT 0,
                failedSyncs INTEGER NOT NULL DEFAULT 0,
                lastSyncTime INTEGER NOT NULL DEFAULT 0,
                totalDataSynced INTEGER NOT NULL DEFAULT 0,
                averageSyncDuration INTEGER NOT NULL DEFAULT 0,
                syncsByType TEXT NOT NULL DEFAULT '{}',
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建数据版本表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS data_versions (
                id TEXT NOT NULL PRIMARY KEY,
                dataType TEXT NOT NULL,
                itemId TEXT NOT NULL,
                version INTEGER NOT NULL DEFAULT 1,
                lastModified INTEGER NOT NULL,
                deviceId TEXT NOT NULL,
                checksum TEXT,
                createdAt INTEGER NOT NULL,
                updatedAt INTEGER NOT NULL
            )
        """.trimIndent())
        
        // 创建数据版本表索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS index_data_versions_dataType_itemId ON data_versions(dataType, itemId)")
        
        // 插入默认同步配置
        val deviceId = UUID.randomUUID().toString()
        val currentTime = System.currentTimeMillis()
        
        database.execSQL("""
            INSERT INTO sync_config (
                id, isEnabled, autoSyncEnabled, syncInterval, wifiOnlySync, 
                lastSyncTime, userId, deviceId, createdAt, updatedAt
            ) VALUES (
                'default', 0, 1, 3600000, 1, 
                0, NULL, '$deviceId', $currentTime, $currentTime
            )
        """.trimIndent())
        
        // 插入默认同步统计
        database.execSQL("""
            INSERT INTO sync_statistics (
                id, totalSyncs, successfulSyncs, failedSyncs, lastSyncTime,
                totalDataSynced, averageSyncDuration, syncsByType, createdAt, updatedAt
            ) VALUES (
                'default', 0, 0, 0, 0,
                0, 0, '{}', $currentTime, $currentTime
            )
        """.trimIndent())
        
        // 注册当前设备
        database.execSQL("""
            INSERT INTO device_info (
                id, deviceId, deviceName, platform, appVersion,
                lastActiveTime, isCurrentDevice, createdAt, updatedAt
            ) VALUES (
                '$deviceId', '$deviceId', 'Android Device', 'Android', '1.0.0',
                $currentTime, 1, $currentTime, $currentTime
            )
        """.trimIndent())
    }
}
