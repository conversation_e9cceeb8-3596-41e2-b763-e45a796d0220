package com.cadence.feature.culture.data.repository

import com.cadence.core.database.dao.culture.CulturalContextDao
import com.cadence.domain.model.culture.*
import com.cadence.domain.repository.culture.CulturalContextRepository
import com.cadence.feature.culture.data.mapper.CulturalContextMapper
import com.cadence.feature.culture.presentation.state.CulturalFilters
import com.cadence.feature.culture.presentation.state.RecommendationFilters
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 文化背景Repository实现类
 * 负责连接Domain层和Data层，处理数据的获取、存储和转换
 */
@Singleton
class CulturalContextRepositoryImpl @Inject constructor(
    private val culturalContextDao: CulturalContextDao
) : CulturalContextRepository {

    /**
     * 根据单词获取文化背景信息
     */
    override suspend fun getCulturalContextsByWord(word: String): Result<List<CulturalContext>> {
        return try {
            val contextEntities = culturalContextDao.getCulturalContextsByWord(word)
            val contextIds = contextEntities.map { it.id }
            
            // 批量获取相关数据
            val usageContextsMap = culturalContextDao.getUsageContextsByContextIds(contextIds)
                .groupBy { it.contextId }
            val regionalDifferencesMap = culturalContextDao.getRegionalDifferencesByContextIds(contextIds)
                .groupBy { it.contextId }
            val examplesMap = culturalContextDao.getCulturalExamplesByContextIds(contextIds)
                .groupBy { it.contextId }
            val recommendationsMap = culturalContextDao.getCulturalRecommendationsByContextIds(contextIds)
                .groupBy { it.contextId }
            val learningProgressMap = culturalContextDao.getLearningProgressByContextIds(contextIds)
                .associateBy { it.contextId }
            
            val domainModels = CulturalContextMapper.mapToDomainList(
                contextEntities = contextEntities,
                usageContextsMap = usageContextsMap,
                regionalDifferencesMap = regionalDifferencesMap,
                examplesMap = examplesMap,
                recommendationsMap = recommendationsMap,
                learningProgressMap = learningProgressMap
            )
            
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 根据ID获取文化背景信息
     */
    override suspend fun getCulturalContextById(contextId: String): Result<CulturalContext> {
        return try {
            val contextEntity = culturalContextDao.getCulturalContextById(contextId)
                ?: return Result.failure(Exception("文化背景不存在"))
            
            val usageContexts = culturalContextDao.getUsageContextsByContextId(contextId)
            val regionalDifferences = culturalContextDao.getRegionalDifferencesByContextId(contextId)
            val examples = culturalContextDao.getCulturalExamplesByContextId(contextId)
            val recommendations = culturalContextDao.getCulturalRecommendationsByContextId(contextId)
            val learningProgress = culturalContextDao.getLearningProgressByContextId(contextId)
            
            val domainModel = CulturalContextMapper.mapToDomain(
                contextEntity = contextEntity,
                usageContexts = usageContexts,
                regionalDifferences = regionalDifferences,
                examples = examples,
                recommendations = recommendations,
                learningProgress = learningProgress
            )
            
            Result.success(domainModel)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 搜索文化背景信息
     */
    override suspend fun searchCulturalContexts(
        query: String,
        filters: CulturalFilters
    ): Result<List<CulturalContext>> {
        return try {
            val contextEntities = culturalContextDao.searchCulturalContexts(
                query = query,
                difficulty = filters.difficulty?.name,
                region = filters.region,
                knowledgeType = filters.knowledgeType?.name
            )
            
            val contextIds = contextEntities.map { it.id }
            
            // 批量获取相关数据
            val usageContextsMap = culturalContextDao.getUsageContextsByContextIds(contextIds)
                .groupBy { it.contextId }
            val regionalDifferencesMap = culturalContextDao.getRegionalDifferencesByContextIds(contextIds)
                .groupBy { it.contextId }
            val examplesMap = culturalContextDao.getCulturalExamplesByContextIds(contextIds)
                .groupBy { it.contextId }
            val recommendationsMap = culturalContextDao.getCulturalRecommendationsByContextIds(contextIds)
                .groupBy { it.contextId }
            val learningProgressMap = culturalContextDao.getLearningProgressByContextIds(contextIds)
                .associateBy { it.contextId }
            
            val domainModels = CulturalContextMapper.mapToDomainList(
                contextEntities = contextEntities,
                usageContextsMap = usageContextsMap,
                regionalDifferencesMap = regionalDifferencesMap,
                examplesMap = examplesMap,
                recommendationsMap = recommendationsMap,
                learningProgressMap = learningProgressMap
            )
            
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取相关文化背景
     */
    override suspend fun getRelatedCulturalContexts(
        contextId: String,
        limit: Int
    ): Result<List<CulturalContext>> {
        return try {
            val contextEntities = culturalContextDao.getRelatedCulturalContexts(contextId, limit)
            val contextIds = contextEntities.map { it.id }
            
            // 批量获取相关数据
            val usageContextsMap = culturalContextDao.getUsageContextsByContextIds(contextIds)
                .groupBy { it.contextId }
            val regionalDifferencesMap = culturalContextDao.getRegionalDifferencesByContextIds(contextIds)
                .groupBy { it.contextId }
            val examplesMap = culturalContextDao.getCulturalExamplesByContextIds(contextIds)
                .groupBy { it.contextId }
            val recommendationsMap = culturalContextDao.getCulturalRecommendationsByContextIds(contextIds)
                .groupBy { it.contextId }
            val learningProgressMap = culturalContextDao.getLearningProgressByContextIds(contextIds)
                .associateBy { it.contextId }
            
            val domainModels = CulturalContextMapper.mapToDomainList(
                contextEntities = contextEntities,
                usageContextsMap = usageContextsMap,
                regionalDifferencesMap = regionalDifferencesMap,
                examplesMap = examplesMap,
                recommendationsMap = recommendationsMap,
                learningProgressMap = learningProgressMap
            )
            
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 收藏文化背景
     */
    override suspend fun bookmarkCulturalContext(contextId: String): Result<Unit> {
        return try {
            culturalContextDao.updateBookmarkStatus(contextId, true)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 取消收藏文化背景
     */
    override suspend fun unbookmarkCulturalContext(contextId: String): Result<Unit> {
        return try {
            culturalContextDao.updateBookmarkStatus(contextId, false)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 标记文化背景为已学习
     */
    override suspend fun markCulturalContextAsLearned(contextId: String): Result<Unit> {
        return try {
            culturalContextDao.updateLearnedStatus(contextId, true)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 取消文化背景的已学习标记
     */
    override suspend fun unmarkCulturalContextAsLearned(contextId: String): Result<Unit> {
        return try {
            culturalContextDao.updateLearnedStatus(contextId, false)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 保存学习笔记
     */
    override suspend fun saveLearningNotes(contextId: String, notes: String): Result<Unit> {
        return try {
            culturalContextDao.updateLearningNotes(contextId, notes)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取学习笔记
     */
    override suspend fun getLearningNotes(contextId: String): Result<String> {
        return try {
            val notes = culturalContextDao.getLearningNotes(contextId) ?: ""
            Result.success(notes)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 检查是否已收藏
     */
    override suspend fun isContextBookmarked(contextId: String): Result<Boolean> {
        return try {
            val isBookmarked = culturalContextDao.isContextBookmarked(contextId)
            Result.success(isBookmarked)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 检查是否已学习
     */
    override suspend fun isContextLearned(contextId: String): Result<Boolean> {
        return try {
            val isLearned = culturalContextDao.isContextLearned(contextId)
            Result.success(isLearned)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取已收藏的文化背景ID列表
     */
    override suspend fun getBookmarkedContextIds(): Result<List<String>> {
        return try {
            val bookmarkedIds = culturalContextDao.getBookmarkedContextIds()
            Result.success(bookmarkedIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取已学习的文化背景ID列表
     */
    override suspend fun getLearnedContextIds(): Result<List<String>> {
        return try {
            val learnedIds = culturalContextDao.getLearnedContextIds()
            Result.success(learnedIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取热门推荐
     */
    override suspend fun getPopularRecommendations(limit: Int): Result<List<CulturalRecommendation>> {
        return try {
            val recommendationEntities = culturalContextDao.getPopularRecommendations(limit)
            val domainModels = recommendationEntities.map { 
                CulturalContextMapper.mapCulturalRecommendationToDomain(it) 
            }
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取个性化推荐
     */
    override suspend fun getPersonalizedRecommendations(limit: Int): Result<List<CulturalRecommendation>> {
        return try {
            val recommendationEntities = culturalContextDao.getPersonalizedRecommendations(limit)
            val domainModels = recommendationEntities.map { 
                CulturalContextMapper.mapCulturalRecommendationToDomain(it) 
            }
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 根据类型获取推荐
     */
    override suspend fun getRecommendationsByType(
        type: CulturalKnowledgeType,
        limit: Int
    ): Result<List<CulturalRecommendation>> {
        return try {
            val recommendationEntities = culturalContextDao.getRecommendationsByType(type.name, limit)
            val domainModels = recommendationEntities.map { 
                CulturalContextMapper.mapCulturalRecommendationToDomain(it) 
            }
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取筛选后的推荐
     */
    override suspend fun getFilteredRecommendations(filters: RecommendationFilters): Result<List<CulturalRecommendation>> {
        return try {
            val recommendationEntities = culturalContextDao.getFilteredRecommendations(
                type = filters.type?.name,
                difficulty = filters.difficulty?.name,
                region = filters.region,
                minPopularity = filters.minPopularity
            )
            val domainModels = recommendationEntities.map { 
                CulturalContextMapper.mapCulturalRecommendationToDomain(it) 
            }
            Result.success(domainModels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 点赞推荐
     */
    override suspend fun likeRecommendation(recommendationId: String): Result<Unit> {
        return try {
            culturalContextDao.updateRecommendationLike(recommendationId, true)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 取消点赞推荐
     */
    override suspend fun unlikeRecommendation(recommendationId: String): Result<Unit> {
        return try {
            culturalContextDao.updateRecommendationLike(recommendationId, false)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 收藏推荐
     */
    override suspend fun bookmarkRecommendation(recommendationId: String): Result<Unit> {
        return try {
            culturalContextDao.updateRecommendationBookmark(recommendationId, true)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 取消收藏推荐
     */
    override suspend fun unbookmarkRecommendation(recommendationId: String): Result<Unit> {
        return try {
            culturalContextDao.updateRecommendationBookmark(recommendationId, false)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取已点赞的推荐ID列表
     */
    override suspend fun getLikedRecommendationIds(): Result<List<String>> {
        return try {
            val likedIds = culturalContextDao.getLikedRecommendationIds()
            Result.success(likedIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取已收藏的推荐ID列表
     */
    override suspend fun getBookmarkedRecommendationIds(): Result<List<String>> {
        return try {
            val bookmarkedIds = culturalContextDao.getBookmarkedRecommendationIds()
            Result.success(bookmarkedIds)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取学习统计信息
     */
    override suspend fun getLearningStatistics(): Result<LearningStatistics> {
        return try {
            val statistics = culturalContextDao.getLearningStatistics()
            val domainStatistics = LearningStatistics(
                totalContexts = statistics.totalContexts,
                learnedContexts = statistics.learnedContexts,
                bookmarkedContexts = statistics.bookmarkedContexts,
                averageStudyTime = statistics.averageStudyTime,
                studyStreak = statistics.studyStreak,
                weeklyGoal = statistics.weeklyGoal,
                weeklyProgress = statistics.weeklyProgress
            )
            Result.success(domainStatistics)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取文化背景流（响应式）
     */
    override fun getCulturalContextsFlow(word: String): Flow<List<CulturalContext>> {
        return culturalContextDao.getCulturalContextsFlowByWord(word).map { contextEntities ->
            val contextIds = contextEntities.map { it.id }
            
            // 批量获取相关数据
            val usageContextsMap = culturalContextDao.getUsageContextsByContextIds(contextIds)
                .groupBy { it.contextId }
            val regionalDifferencesMap = culturalContextDao.getRegionalDifferencesByContextIds(contextIds)
                .groupBy { it.contextId }
            val examplesMap = culturalContextDao.getCulturalExamplesByContextIds(contextIds)
                .groupBy { it.contextId }
            val recommendationsMap = culturalContextDao.getCulturalRecommendationsByContextIds(contextIds)
                .groupBy { it.contextId }
            val learningProgressMap = culturalContextDao.getLearningProgressByContextIds(contextIds)
                .associateBy { it.contextId }
            
            CulturalContextMapper.mapToDomainList(
                contextEntities = contextEntities,
                usageContextsMap = usageContextsMap,
                regionalDifferencesMap = regionalDifferencesMap,
                examplesMap = examplesMap,
                recommendationsMap = recommendationsMap,
                learningProgressMap = learningProgressMap
            )
        }
    }

    /**
     * 获取学习进度流（响应式）
     */
    override fun getLearningProgressFlow(): Flow<LearningStatistics> {
        return culturalContextDao.getLearningStatisticsFlow().map { statistics ->
            LearningStatistics(
                totalContexts = statistics.totalContexts,
                learnedContexts = statistics.learnedContexts,
                bookmarkedContexts = statistics.bookmarkedContexts,
                averageStudyTime = statistics.averageStudyTime,
                studyStreak = statistics.studyStreak,
                weeklyGoal = statistics.weeklyGoal,
                weeklyProgress = statistics.weeklyProgress
            )
        }
    }
}