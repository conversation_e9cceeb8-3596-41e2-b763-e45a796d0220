package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.*
import com.cadence.domain.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 学习进度ViewModel
 * 管理学习进度界面的状态和数据
 */
@HiltViewModel
class ProgressViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ProgressUiState())
    val uiState: StateFlow<ProgressUiState> = _uiState.asStateFlow()
    
    private val _statistics = MutableStateFlow<LearningStatistics?>(null)
    val statistics: StateFlow<LearningStatistics?> = _statistics.asStateFlow()
    
    private val _progressByMastery = MutableStateFlow<Map<MasteryLevel, List<LearningProgress>>>(emptyMap())
    val progressByMastery: StateFlow<Map<MasteryLevel, List<LearningProgress>>> = _progressByMastery.asStateFlow()
    
    private val _categoryProgress = MutableStateFlow<Map<WordCategory, CategoryProgress>>(emptyMap())
    val categoryProgress: StateFlow<Map<WordCategory, CategoryProgress>> = _categoryProgress.asStateFlow()
    
    private val _dailyProgress = MutableStateFlow<List<DailyProgress>>(emptyList())
    val dailyProgress: StateFlow<List<DailyProgress>> = _dailyProgress.asStateFlow()
    
    /**
     * 加载进度数据
     */
    fun loadProgressData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val userId = "current_user"
                
                // 加载学习统计
                val stats = learningRepository.getLearningStatistics(userId)
                _statistics.value = stats
                
                // 加载用户学习进度并按掌握程度分组
                learningRepository.getUserLearningProgress(userId).collect { progressList ->
                    val groupedProgress = progressList.groupBy { it.masteryLevel }
                    _progressByMastery.value = groupedProgress
                }
                
                // 加载分类进度
                val categoryProgressMap = learningRepository.getCategoryProgress(userId)
                _categoryProgress.value = categoryProgressMap
                
                // 加载每日进度（最近7天）
                val dailyProgressList = learningRepository.getDailyProgress(userId, 7)
                _dailyProgress.value = dailyProgressList
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载进度数据失败"
                )
            }
        }
    }
    
    /**
     * 刷新进度数据
     */
    fun refreshProgress() {
        loadProgressData()
    }
    
    /**
     * 获取指定掌握程度的单词数量
     */
    fun getWordCountByMastery(masteryLevel: MasteryLevel): Int {
        return _progressByMastery.value[masteryLevel]?.size ?: 0
    }
    
    /**
     * 获取总体学习进度百分比
     */
    fun getOverallProgressPercentage(): Float {
        val stats = _statistics.value ?: return 0f
        return if (stats.totalWordsLearned > 0) {
            stats.masteredWords.toFloat() / stats.totalWordsLearned
        } else 0f
    }
}

/**
 * 进度界面UI状态
 */
data class ProgressUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)