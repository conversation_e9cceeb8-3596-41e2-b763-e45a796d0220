package com.cadence.domain.repository.culture

import com.cadence.domain.model.culture.*
import kotlinx.coroutines.flow.Flow

/**
 * 文化推荐Repository接口
 * 提供文化知识推荐相关功能
 */
interface CulturalRecommendationRepository {
    
    // 基础CRUD操作
    suspend fun saveRecommendation(recommendation: CulturalRecommendation): Result<Unit>
    suspend fun saveRecommendations(recommendations: List<CulturalRecommendation>): Result<Unit>
    suspend fun updateRecommendation(recommendation: CulturalRecommendation): Result<Unit>
    suspend fun deleteRecommendation(recommendationId: String): Result<Unit>
    
    // 查询操作
    suspend fun getRecommendationById(recommendationId: String): Result<CulturalRecommendation?>
    suspend fun getRecommendationsByType(type: CulturalKnowledgeType): Result<List<CulturalRecommendation>>
    suspend fun getRecommendationsByDifficulty(difficulty: CulturalDifficulty): Result<List<CulturalRecommendation>>
    suspend fun getRecommendationsByRegion(region: String): Result<List<CulturalRecommendation>>
    suspend fun getRecommendationsByLanguage(language: String): Result<List<CulturalRecommendation>>
    suspend fun searchRecommendations(query: String): Result<List<CulturalRecommendation>>
    suspend fun getRecommendationsPaged(page: Int, pageSize: Int): Result<List<CulturalRecommendation>>
    suspend fun getTopRecommendations(limit: Int): Result<List<CulturalRecommendation>>
    
    // Flow查询
    fun getAllRecommendationsFlow(): Flow<List<CulturalRecommendation>>
    fun getRecommendationsByTypeFlow(type: CulturalKnowledgeType): Flow<List<CulturalRecommendation>>
    fun getTopRecommendationsFlow(limit: Int): Flow<List<CulturalRecommendation>>
    
    // 个性化推荐
    suspend fun getPersonalizedRecommendations(
        userId: String,
        userPreferences: UserCulturalPreferences,
        limit: Int = 10
    ): Result<List<CulturalRecommendation>>
    
    suspend fun getRecommendationsBasedOnHistory(
        userId: String,
        limit: Int = 10
    ): Result<List<CulturalRecommendation>>
    
    suspend fun getRecommendationsByTypeAndDifficulty(
        type: CulturalKnowledgeType,
        difficulty: CulturalDifficulty,
        limit: Int = 10
    ): Result<List<CulturalRecommendation>>
    
    // 统计和分析
    suspend fun getRecommendationCount(): Result<Int>
    suspend fun getRecommendationCountByType(type: CulturalKnowledgeType): Result<Int>
    suspend fun getRecommendationStatsByTypes(): Result<Map<CulturalKnowledgeType, Int>>
    suspend fun getPopularRecommendations(limit: Int = 20): Result<List<CulturalRecommendation>>
    
    // 交互操作
    suspend fun incrementPopularity(recommendationId: String): Result<Unit>
    suspend fun recordUserInteraction(
        userId: String,
        recommendationId: String,
        interactionType: RecommendationInteractionType
    ): Result<Unit>
    
    // 缓存和同步
    suspend fun refreshRecommendations(): Result<Unit>
    suspend fun syncRecommendations(): Result<Unit>
}

/**
 * 用户文化偏好
 */
data class UserCulturalPreferences(
    val preferredLanguages: List<String>,
    val preferredRegions: List<String>,
    val preferredTypes: List<CulturalKnowledgeType>,
    val preferredDifficulty: CulturalDifficulty,
    val interests: List<String>
)

/**
 * 推荐交互类型
 */
enum class RecommendationInteractionType {
    VIEW,
    LIKE,
    BOOKMARK,
    SHARE,
    COMPLETE
}