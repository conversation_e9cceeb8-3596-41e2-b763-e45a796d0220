package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 结构化日志器
 * 提供结构化的日志记录功能，支持JSON格式输出和多级别日志
 */
@Singleton
class StructuredLogger @Inject constructor(
    private val context: Context,
    private val logFormatter: LogFormatter,
    private val logFileManager: LogFileManager
) {
    
    companion object {
        private const val LOG_BUFFER_SIZE = 1000
        private const val LOG_FLUSH_INTERVAL_MS = 5000L
        private const val MAX_LOG_ENTRY_SIZE = 10 * 1024 // 10KB
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val json = Json { 
        prettyPrint = false
        ignoreUnknownKeys = true
    }
    
    // 日志缓冲区
    private val logBuffer = Channel<LogEntry>(LOG_BUFFER_SIZE)
    
    // 日志统计
    private val _logStats = MutableStateFlow(LogStatistics())
    val logStats: StateFlow<LogStatistics> = _logStats.asStateFlow()
    
    // 日志上下文
    private val globalContext = mutableMapOf<String, String>()
    private val threadLocalContext = ThreadLocal<MutableMap<String, String>>()
    
    init {
        startLogProcessor()
        startPeriodicFlush()
        Timber.d("结构化日志器已初始化")
    }
    
    /**
     * 记录VERBOSE级别日志
     */
    fun verbose(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.VERBOSE, message, context, exception)
    }
    
    /**
     * 记录DEBUG级别日志
     */
    fun debug(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.DEBUG, message, context, exception)
    }
    
    /**
     * 记录INFO级别日志
     */
    fun info(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.INFO, message, context, exception)
    }
    
    /**
     * 记录WARN级别日志
     */
    fun warn(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.WARN, message, context, exception)
    }
    
    /**
     * 记录ERROR级别日志
     */
    fun error(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.ERROR, message, context, exception)
    }
    
    /**
     * 记录FATAL级别日志
     */
    fun fatal(
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        log(LogLevel.FATAL, message, context, exception)
    }
    
    /**
     * 通用日志记录方法
     */
    fun log(
        level: LogLevel,
        message: String,
        context: Map<String, String> = emptyMap(),
        exception: Throwable? = null
    ) {
        try {
            val logEntry = createLogEntry(level, message, context, exception)
            
            // 检查日志条目大小
            val entrySize = estimateLogEntrySize(logEntry)
            if (entrySize > MAX_LOG_ENTRY_SIZE) {
                val truncatedEntry = truncateLogEntry(logEntry)
                offerToBuffer(truncatedEntry)
            } else {
                offerToBuffer(logEntry)
            }
            
            // 同时输出到Timber（用于开发调试）
            outputToTimber(level, message, exception)
            
            // 更新统计
            updateLogStatistics(level)
            
        } catch (e: Exception) {
            // 避免日志记录本身出错导致的无限循环
            Timber.e(e, "记录结构化日志时发生错误")
        }
    }
    
    /**
     * 创建日志条目
     */
    private fun createLogEntry(
        level: LogLevel,
        message: String,
        context: Map<String, String>,
        exception: Throwable?
    ): LogEntry {
        val timestamp = System.currentTimeMillis()
        val threadName = Thread.currentThread().name
        val threadId = Thread.currentThread().id
        
        // 合并上下文
        val mergedContext = buildMap {
            putAll(globalContext)
            threadLocalContext.get()?.let { putAll(it) }
            putAll(context)
        }
        
        return LogEntry(
            timestamp = timestamp,
            level = level,
            message = message,
            context = mergedContext,
            threadName = threadName,
            threadId = threadId,
            exception = exception?.let { createExceptionInfo(it) },
            sessionId = getSessionId(),
            userId = getUserId(),
            buildType = getBuildType(),
            appVersion = getAppVersion()
        )
    }
    
    /**
     * 创建异常信息
     */
    private fun createExceptionInfo(exception: Throwable): ExceptionInfo {
        return ExceptionInfo(
            type = exception.javaClass.name,
            message = exception.message ?: "No message",
            stackTrace = getStackTrace(exception),
            cause = exception.cause?.let { createExceptionInfo(it) }
        )
    }
    
    /**
     * 估算日志条目大小
     */
    private fun estimateLogEntrySize(logEntry: LogEntry): Int {
        return try {
            json.encodeToString(logEntry).length
        } catch (e: Exception) {
            logEntry.message.length + 1000 // 粗略估算
        }
    }
    
    /**
     * 截断日志条目
     */
    private fun truncateLogEntry(logEntry: LogEntry): LogEntry {
        val maxMessageLength = 1000
        val maxContextEntries = 10
        val maxStackTraceLines = 50
        
        return logEntry.copy(
            message = if (logEntry.message.length > maxMessageLength) {
                logEntry.message.take(maxMessageLength) + "... (truncated)"
            } else {
                logEntry.message
            },
            context = logEntry.context.entries.take(maxContextEntries).associate { it.key to it.value },
            exception = logEntry.exception?.let { exception ->
                exception.copy(
                    stackTrace = truncateStackTrace(exception.stackTrace, maxStackTraceLines)
                )
            }
        )
    }
    
    /**
     * 截断堆栈跟踪
     */
    private fun truncateStackTrace(stackTrace: String, maxLines: Int): String {
        val lines = stackTrace.split("\n")
        return if (lines.size > maxLines) {
            lines.take(maxLines).joinToString("\n") + "\n... (truncated ${lines.size - maxLines} lines)"
        } else {
            stackTrace
        }
    }
    
    /**
     * 添加到缓冲区
     */
    private fun offerToBuffer(logEntry: LogEntry) {
        if (!logBuffer.trySend(logEntry).isSuccess) {
            // 缓冲区满了，丢弃最旧的日志
            Timber.w("日志缓冲区已满，丢弃日志条目")
        }
    }
    
    /**
     * 输出到Timber
     */
    private fun outputToTimber(level: LogLevel, message: String, exception: Throwable?) {
        when (level) {
            LogLevel.VERBOSE -> Timber.v(exception, message)
            LogLevel.DEBUG -> Timber.d(exception, message)
            LogLevel.INFO -> Timber.i(exception, message)
            LogLevel.WARN -> Timber.w(exception, message)
            LogLevel.ERROR -> Timber.e(exception, message)
            LogLevel.FATAL -> Timber.e(exception, "[FATAL] $message")
        }
    }
    
    /**
     * 启动日志处理器
     */
    private fun startLogProcessor() {
        scope.launch {
            for (logEntry in logBuffer) {
                try {
                    processLogEntry(logEntry)
                } catch (e: Exception) {
                    Timber.e(e, "处理日志条目时发生错误")
                }
            }
        }
    }
    
    /**
     * 处理日志条目
     */
    private suspend fun processLogEntry(logEntry: LogEntry) = withContext(Dispatchers.IO) {
        try {
            // 格式化日志
            val formattedLog = logFormatter.format(logEntry)
            
            // 写入文件
            logFileManager.writeLog(formattedLog, logEntry.level)
            
        } catch (e: Exception) {
            Timber.e(e, "处理日志条目失败")
        }
    }
    
    /**
     * 启动定期刷新
     */
    private fun startPeriodicFlush() {
        scope.launch {
            while (isActive) {
                delay(LOG_FLUSH_INTERVAL_MS)
                try {
                    logFileManager.flush()
                } catch (e: Exception) {
                    Timber.e(e, "定期刷新日志文件失败")
                }
            }
        }
    }
    
    /**
     * 更新日志统计
     */
    private fun updateLogStatistics(level: LogLevel) {
        val currentStats = _logStats.value
        _logStats.value = currentStats.copy(
            totalLogs = currentStats.totalLogs + 1,
            logsByLevel = currentStats.logsByLevel.toMutableMap().apply {
                this[level] = (this[level] ?: 0) + 1
            },
            lastLogTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 设置全局上下文
     */
    fun setGlobalContext(key: String, value: String) {
        globalContext[key] = value
    }
    
    /**
     * 移除全局上下文
     */
    fun removeGlobalContext(key: String) {
        globalContext.remove(key)
    }
    
    /**
     * 设置线程本地上下文
     */
    fun setThreadContext(key: String, value: String) {
        val context = threadLocalContext.get() ?: mutableMapOf<String, String>().also {
            threadLocalContext.set(it)
        }
        context[key] = value
    }
    
    /**
     * 清除线程本地上下文
     */
    fun clearThreadContext() {
        threadLocalContext.remove()
    }
    
    /**
     * 获取会话ID
     */
    private fun getSessionId(): String {
        // 这里应该从会话管理器获取
        return "session_${System.currentTimeMillis()}"
    }
    
    /**
     * 获取用户ID
     */
    private fun getUserId(): String? {
        // 这里应该从用户管理器获取
        return null
    }
    
    /**
     * 获取构建类型
     */
    private fun getBuildType(): String {
        return if (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE != 0) {
            "debug"
        } else {
            "release"
        }
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "unknown"
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * 获取堆栈跟踪
     */
    private fun getStackTrace(exception: Throwable): String {
        return try {
            val stringWriter = java.io.StringWriter()
            val printWriter = java.io.PrintWriter(stringWriter)
            exception.printStackTrace(printWriter)
            stringWriter.toString()
        } catch (e: Exception) {
            "Unable to get stack trace"
        }
    }
    
    /**
     * 强制刷新日志
     */
    suspend fun flush() {
        logFileManager.flush()
    }
    
    /**
     * 获取日志统计信息
     */
    fun getLogStatistics(): LogStatistics {
        return _logStats.value
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        logBuffer.close()
        threadLocalContext.remove()
        Timber.d("结构化日志器已清理")
    }
}

/**
 * 日志级别枚举
 */
enum class LogLevel(val priority: Int) {
    VERBOSE(2),
    DEBUG(3),
    INFO(4),
    WARN(5),
    ERROR(6),
    FATAL(7)
}

/**
 * 日志条目数据类
 */
data class LogEntry(
    val timestamp: Long,
    val level: LogLevel,
    val message: String,
    val context: Map<String, String>,
    val threadName: String,
    val threadId: Long,
    val exception: ExceptionInfo? = null,
    val sessionId: String,
    val userId: String? = null,
    val buildType: String,
    val appVersion: String
)

/**
 * 日志统计数据类
 */
data class LogStatistics(
    val totalLogs: Long = 0,
    val logsByLevel: Map<LogLevel, Long> = emptyMap(),
    val lastLogTime: Long = 0
)
