package com.cadence.core.speech

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.Voice
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.io.File
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线语音引擎
 * 提供离线TTS和语音识别备用方案
 */
@Singleton
class OfflineSpeechEngine @Inject constructor(
    @ApplicationContext private val context: Context,
    private val speechCacheManager: SpeechCacheManager
) {
    
    private var offlineTts: TextToSpeech? = null
    private var isOfflineTtsInitialized = false
    private val offlineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // 支持的离线语言
    private val supportedOfflineLanguages = setOf(
        "zh-CN", "zh-TW", "en-US", "en-GB", 
        "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"
    )
    
    // 离线语音数据目录
    private val offlineDataDir by lazy {
        File(context.filesDir, "offline_speech").apply {
            if (!exists()) mkdirs()
        }
    }
    
    /**
     * 离线语音引擎状态
     */
    data class OfflineEngineStatus(
        val isTtsAvailable: Boolean,
        val availableLanguages: List<String>,
        val installedVoices: List<OfflineVoice>,
        val totalDataSize: Long,
        val isInitialized: Boolean
    )
    
    /**
     * 离线语音
     */
    data class OfflineVoice(
        val name: String,
        val language: String,
        val quality: VoiceQuality,
        val isInstalled: Boolean,
        val dataSize: Long
    )
    
    enum class VoiceQuality {
        LOW, NORMAL, HIGH, VERY_HIGH
    }
    
    /**
     * 初始化离线语音引擎
     */
    suspend fun initialize(): Boolean = withContext(Dispatchers.Main) {
        if (isOfflineTtsInitialized) return@withContext true
        
        return@withContext suspendCancellableCoroutine { continuation ->
            try {
                offlineTts = TextToSpeech(context) { status ->
                    isOfflineTtsInitialized = status == TextToSpeech.SUCCESS
                    if (isOfflineTtsInitialized) {
                        setupOfflineVoices()
                    }
                    continuation.resume(isOfflineTtsInitialized)
                }
                
                continuation.invokeOnCancellation {
                    if (!isOfflineTtsInitialized) {
                        offlineTts?.shutdown()
                        offlineTts = null
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "离线TTS初始化失败")
                continuation.resume(false)
            }
        }
    }
    
    /**
     * 设置离线语音
     */
    private fun setupOfflineVoices() {
        try {
            offlineTts?.voices?.forEach { voice ->
                if (voice.isNetworkConnectionRequired.not()) {
                    Timber.d("发现离线语音: ${voice.name}, 语言: ${voice.locale}")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "设置离线语音失败")
        }
    }
    
    /**
     * 检查语言是否支持离线TTS
     */
    fun isLanguageSupportedOffline(language: String): Boolean {
        return supportedOfflineLanguages.contains(language) && 
               isVoiceAvailableOffline(language)
    }
    
    /**
     * 检查语音是否可离线使用
     */
    private fun isVoiceAvailableOffline(language: String): Boolean {
        return try {
            offlineTts?.voices?.any { voice ->
                voice.locale.toString().startsWith(language.substring(0, 2)) &&
                !voice.isNetworkConnectionRequired
            } ?: false
        } catch (e: Exception) {
            Timber.e(e, "检查离线语音可用性失败")
            false
        }
    }
    
    /**
     * 离线TTS合成
     */
    suspend fun synthesizeOffline(
        text: String,
        language: String,
        config: TtsConfig = TtsConfig()
    ): Flow<OfflineTtsResult> = callbackFlow {
        if (!isOfflineTtsInitialized) {
            trySend(OfflineTtsResult.Error("离线TTS未初始化"))
            close()
            return@callbackFlow
        }
        
        if (!isLanguageSupportedOffline(language)) {
            trySend(OfflineTtsResult.Error("不支持离线语言: $language"))
            close()
            return@callbackFlow
        }
        
        // 检查缓存
        val cachedAudio = speechCacheManager.getCachedTtsAudio(text, language, config)
        if (cachedAudio != null) {
            trySend(OfflineTtsResult.Success(cachedAudio, true))
            close()
            return@callbackFlow
        }
        
        try {
            // 设置语言
            val locale = Locale.forLanguageTag(language)
            val result = offlineTts?.setLanguage(locale)
            
            if (result == TextToSpeech.LANG_MISSING_DATA || 
                result == TextToSpeech.LANG_NOT_SUPPORTED) {
                trySend(OfflineTtsResult.Error("语言不支持: $language"))
                close()
                return@callbackFlow
            }
            
            // 设置语音参数
            offlineTts?.setSpeechRate(config.speechRate)
            offlineTts?.setPitch(config.pitch)
            
            // 合成到文件
            val outputFile = File(offlineDataDir, "temp_${System.currentTimeMillis()}.wav")
            val utteranceId = "offline_${System.currentTimeMillis()}"
            
            val synthesisResult = offlineTts?.synthesizeToFile(
                text,
                null,
                outputFile,
                utteranceId
            )
            
            if (synthesisResult == TextToSpeech.SUCCESS) {
                // 等待合成完成
                delay(1000) // 简单等待，实际应该监听完成事件
                
                if (outputFile.exists()) {
                    val audioData = outputFile.readBytes()
                    outputFile.delete()
                    
                    // 缓存结果
                    speechCacheManager.cacheTtsAudio(text, language, audioData, config)
                    
                    trySend(OfflineTtsResult.Success(audioData, false))
                } else {
                    trySend(OfflineTtsResult.Error("音频文件生成失败"))
                }
            } else {
                trySend(OfflineTtsResult.Error("TTS合成失败"))
            }
            
        } catch (e: Exception) {
            Timber.e(e, "离线TTS合成失败")
            trySend(OfflineTtsResult.Error(e.message ?: "未知错误"))
        }
        
        close()
    }
    
    /**
     * 获取离线引擎状态
     */
    fun getOfflineEngineStatus(): OfflineEngineStatus {
        val availableLanguages = mutableListOf<String>()
        val installedVoices = mutableListOf<OfflineVoice>()
        
        try {
            offlineTts?.voices?.forEach { voice ->
                if (!voice.isNetworkConnectionRequired) {
                    val language = voice.locale.toString()
                    availableLanguages.add(language)
                    
                    installedVoices.add(
                        OfflineVoice(
                            name = voice.name,
                            language = language,
                            quality = mapVoiceQuality(voice.quality),
                            isInstalled = true,
                            dataSize = estimateVoiceDataSize(voice)
                        )
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "获取离线引擎状态失败")
        }
        
        return OfflineEngineStatus(
            isTtsAvailable = isOfflineTtsInitialized,
            availableLanguages = availableLanguages.distinct(),
            installedVoices = installedVoices,
            totalDataSize = calculateTotalDataSize(),
            isInitialized = isOfflineTtsInitialized
        )
    }
    
    /**
     * 映射语音质量
     */
    private fun mapVoiceQuality(quality: Int): VoiceQuality {
        return when (quality) {
            Voice.QUALITY_VERY_HIGH -> VoiceQuality.VERY_HIGH
            Voice.QUALITY_HIGH -> VoiceQuality.HIGH
            Voice.QUALITY_NORMAL -> VoiceQuality.NORMAL
            else -> VoiceQuality.LOW
        }
    }
    
    /**
     * 估算语音数据大小
     */
    private fun estimateVoiceDataSize(voice: Voice): Long {
        // 根据语音质量估算数据大小（MB）
        return when (voice.quality) {
            Voice.QUALITY_VERY_HIGH -> 50L * 1024 * 1024
            Voice.QUALITY_HIGH -> 30L * 1024 * 1024
            Voice.QUALITY_NORMAL -> 20L * 1024 * 1024
            else -> 10L * 1024 * 1024
        }
    }
    
    /**
     * 计算总数据大小
     */
    private fun calculateTotalDataSize(): Long {
        return try {
            offlineDataDir.walkTopDown()
                .filter { it.isFile }
                .sumOf { it.length() }
        } catch (e: Exception) {
            Timber.e(e, "计算数据大小失败")
            0L
        }
    }
    
    /**
     * 下载离线语音包
     */
    suspend fun downloadOfflineVoicePack(language: String): Flow<DownloadProgress> = flow {
        emit(DownloadProgress.Started)
        
        try {
            // 检查是否已安装
            if (isLanguageSupportedOffline(language)) {
                emit(DownloadProgress.Completed)
                return@flow
            }
            
            // 模拟下载过程（实际应该从服务器下载）
            emit(DownloadProgress.Progress(0.1f, "准备下载..."))
            delay(500)
            
            emit(DownloadProgress.Progress(0.3f, "下载语音数据..."))
            delay(1000)
            
            emit(DownloadProgress.Progress(0.6f, "安装语音包..."))
            delay(800)
            
            emit(DownloadProgress.Progress(0.9f, "配置语音引擎..."))
            delay(500)
            
            emit(DownloadProgress.Completed)
            
        } catch (e: Exception) {
            Timber.e(e, "下载离线语音包失败")
            emit(DownloadProgress.Error(e.message ?: "下载失败"))
        }
    }
    
    /**
     * 删除离线语音包
     */
    suspend fun deleteOfflineVoicePack(language: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // 删除相关的缓存文件
                val languageDir = File(offlineDataDir, language)
                if (languageDir.exists()) {
                    languageDir.deleteRecursively()
                }
                
                Timber.d("离线语音包已删除: $language")
                true
            } catch (e: Exception) {
                Timber.e(e, "删除离线语音包失败: $language")
                false
            }
        }
    }
    
    /**
     * 清理离线数据
     */
    suspend fun cleanupOfflineData() {
        withContext(Dispatchers.IO) {
            try {
                // 清理临时文件
                offlineDataDir.listFiles()?.forEach { file ->
                    if (file.name.startsWith("temp_")) {
                        file.delete()
                    }
                }
                
                Timber.d("离线数据清理完成")
            } catch (e: Exception) {
                Timber.e(e, "清理离线数据失败")
            }
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            offlineTts?.shutdown()
            offlineScope.cancel()
        } catch (e: Exception) {
            Timber.e(e, "释放离线语音引擎失败")
        } finally {
            offlineTts = null
            isOfflineTtsInitialized = false
        }
    }
}

/**
 * 离线TTS结果
 */
sealed class OfflineTtsResult {
    data class Success(val audioData: ByteArray, val fromCache: Boolean) : OfflineTtsResult()
    data class Error(val message: String) : OfflineTtsResult()
}

/**
 * 下载进度
 */
sealed class DownloadProgress {
    object Started : DownloadProgress()
    data class Progress(val progress: Float, val message: String) : DownloadProgress()
    object Completed : DownloadProgress()
    data class Error(val message: String) : DownloadProgress()
}
