package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.DailyProgress
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.max

/**
 * 学习趋势图表组件
 * 显示学习进度的时间趋势
 */
@Composable
fun LearningTrendsChart(
    title: String,
    data: List<DailyProgress>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (data.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无数据",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                // 图表
                TrendsChart(
                    data = data,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 图例
                ChartLegend()
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 统计摘要
                ChartSummary(data = data)
            }
        }
    }
}

/**
 * 趋势图表
 */
@Composable
private fun TrendsChart(
    data: List<DailyProgress>,
    modifier: Modifier = Modifier
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val secondaryColor = MaterialTheme.colorScheme.secondary
    val surfaceColor = MaterialTheme.colorScheme.surface
    val onSurfaceColor = MaterialTheme.colorScheme.onSurface
    
    Canvas(modifier = modifier) {
        if (data.isEmpty()) return@Canvas
        
        val width = size.width
        val height = size.height
        val padding = 40f
        
        val chartWidth = width - padding * 2
        val chartHeight = height - padding * 2
        
        // 计算数据范围
        val maxWords = data.maxOfOrNull { it.wordsStudied } ?: 1
        val maxAccuracy = 1f // 正确率最大值为1
        
        // 绘制背景网格
        drawGrid(
            width = chartWidth,
            height = chartHeight,
            offsetX = padding,
            offsetY = padding,
            color = onSurfaceColor.copy(alpha = 0.1f)
        )
        
        // 绘制学习单词数曲线
        drawWordsLine(
            data = data,
            maxValue = maxWords,
            width = chartWidth,
            height = chartHeight,
            offsetX = padding,
            offsetY = padding,
            color = primaryColor
        )
        
        // 绘制正确率曲线
        drawAccuracyLine(
            data = data,
            width = chartWidth,
            height = chartHeight,
            offsetX = padding,
            offsetY = padding,
            color = secondaryColor
        )
        
        // 绘制坐标轴
        drawAxes(
            width = chartWidth,
            height = chartHeight,
            offsetX = padding,
            offsetY = padding,
            color = onSurfaceColor.copy(alpha = 0.6f)
        )
    }
}

/**
 * 绘制网格
 */
private fun DrawScope.drawGrid(
    width: Float,
    height: Float,
    offsetX: Float,
    offsetY: Float,
    color: Color
) {
    val gridLines = 5
    
    // 水平网格线
    for (i in 0..gridLines) {
        val y = offsetY + (height * i / gridLines)
        drawLine(
            color = color,
            start = Offset(offsetX, y),
            end = Offset(offsetX + width, y),
            strokeWidth = 1.dp.toPx()
        )
    }
    
    // 垂直网格线
    for (i in 0..gridLines) {
        val x = offsetX + (width * i / gridLines)
        drawLine(
            color = color,
            start = Offset(x, offsetY),
            end = Offset(x, offsetY + height),
            strokeWidth = 1.dp.toPx()
        )
    }
}

/**
 * 绘制学习单词数曲线
 */
private fun DrawScope.drawWordsLine(
    data: List<DailyProgress>,
    maxValue: Int,
    width: Float,
    height: Float,
    offsetX: Float,
    offsetY: Float,
    color: Color
) {
    if (data.size < 2) return
    
    val path = Path()
    val points = mutableListOf<Offset>()
    
    data.forEachIndexed { index, progress ->
        val x = offsetX + (width * index / (data.size - 1))
        val y = offsetY + height - (height * progress.wordsStudied / maxValue)
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // 绘制曲线
    drawPath(
        path = path,
        color = color,
        style = Stroke(
            width = 3.dp.toPx(),
            cap = StrokeCap.Round,
            join = StrokeJoin.Round
        )
    )
    
    // 绘制数据点
    points.forEach { point ->
        drawCircle(
            color = color,
            radius = 4.dp.toPx(),
            center = point
        )
        drawCircle(
            color = Color.White,
            radius = 2.dp.toPx(),
            center = point
        )
    }
}

/**
 * 绘制正确率曲线
 */
private fun DrawScope.drawAccuracyLine(
    data: List<DailyProgress>,
    width: Float,
    height: Float,
    offsetX: Float,
    offsetY: Float,
    color: Color
) {
    if (data.size < 2) return
    
    val path = Path()
    val points = mutableListOf<Offset>()
    
    data.forEachIndexed { index, progress ->
        val accuracy = if (progress.totalAnswers > 0) {
            progress.correctAnswers.toFloat() / progress.totalAnswers
        } else 0f
        
        val x = offsetX + (width * index / (data.size - 1))
        val y = offsetY + height - (height * accuracy)
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // 绘制曲线
    drawPath(
        path = path,
        color = color,
        style = Stroke(
            width = 2.dp.toPx(),
            cap = StrokeCap.Round,
            join = StrokeJoin.Round,
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f))
        )
    )
    
    // 绘制数据点
    points.forEach { point ->
        drawCircle(
            color = color,
            radius = 3.dp.toPx(),
            center = point
        )
    }
}

/**
 * 绘制坐标轴
 */
private fun DrawScope.drawAxes(
    width: Float,
    height: Float,
    offsetX: Float,
    offsetY: Float,
    color: Color
) {
    // X轴
    drawLine(
        color = color,
        start = Offset(offsetX, offsetY + height),
        end = Offset(offsetX + width, offsetY + height),
        strokeWidth = 2.dp.toPx()
    )
    
    // Y轴
    drawLine(
        color = color,
        start = Offset(offsetX, offsetY),
        end = Offset(offsetX, offsetY + height),
        strokeWidth = 2.dp.toPx()
    )
}

/**
 * 图例
 */
@Composable
private fun ChartLegend(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        LegendItem(
            color = MaterialTheme.colorScheme.primary,
            label = "学习单词数",
            isLine = true
        )
        
        Spacer(modifier = Modifier.width(24.dp))
        
        LegendItem(
            color = MaterialTheme.colorScheme.secondary,
            label = "正确率",
            isLine = false
        )
    }
}

/**
 * 图例项
 */
@Composable
private fun LegendItem(
    color: Color,
    label: String,
    isLine: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Canvas(
            modifier = Modifier.size(16.dp)
        ) {
            if (isLine) {
                drawLine(
                    color = color,
                    start = Offset(0f, size.height / 2),
                    end = Offset(size.width, size.height / 2),
                    strokeWidth = 3.dp.toPx(),
                    cap = StrokeCap.Round
                )
            } else {
                drawLine(
                    color = color,
                    start = Offset(0f, size.height / 2),
                    end = Offset(size.width, size.height / 2),
                    strokeWidth = 2.dp.toPx(),
                    cap = StrokeCap.Round,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(6f, 3f))
                )
            }
        }
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 图表摘要
 */
@Composable
private fun ChartSummary(
    data: List<DailyProgress>,
    modifier: Modifier = Modifier
) {
    val totalWords = data.sumOf { it.wordsStudied }
    val averageAccuracy = if (data.isNotEmpty()) {
        data.map { progress ->
            if (progress.totalAnswers > 0) {
                progress.correctAnswers.toFloat() / progress.totalAnswers
            } else 0f
        }.average().toFloat()
    } else 0f
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        SummaryItem(
            label = "总学习",
            value = "$totalWords 词",
            modifier = Modifier.weight(1f)
        )
        
        SummaryItem(
            label = "平均正确率",
            value = "${(averageAccuracy * 100).toInt()}%",
            modifier = Modifier.weight(1f)
        )
        
        SummaryItem(
            label = "学习天数",
            value = "${data.count { it.wordsStudied > 0 }} 天",
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 摘要项
 */
@Composable
private fun SummaryItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}