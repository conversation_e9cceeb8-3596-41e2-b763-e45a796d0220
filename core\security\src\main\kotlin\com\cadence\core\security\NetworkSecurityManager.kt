package com.cadence.core.security

import android.content.Context
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import timber.log.Timber
import java.io.IOException
import java.security.MessageDigest
import java.security.cert.CertificateException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import javax.net.ssl.*

/**
 * 网络安全管理器
 * 负责网络通信的安全性，包括证书固定、请求签名等
 */
@Singleton
class NetworkSecurityManager @Inject constructor(
    private val context: Context,
    private val cryptoManager: CryptoManager
) {
    
    companion object {
        // Google API的证书指纹（示例，实际使用时需要获取真实的证书指纹）
        private val GOOGLE_API_PINS = arrayOf(
            "sha256/FEzVOUp4dF3gI0ZVPRJhFbSD608T5Wx+6xhk5sRvdUw=",
            "sha256/Y9mvm0exBk1JoQ57f9Vm28jKo5lFm/woKcVxrYxu80o="
        )
        
        private const val REQUEST_TIMEOUT = 30L
        private const val CONNECT_TIMEOUT = 15L
        private const val WRITE_TIMEOUT = 30L
    }
    
    /**
     * 创建安全的OkHttpClient
     */
    fun createSecureOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(REQUEST_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(createRequestSigningInterceptor())
            .addInterceptor(createSecurityHeadersInterceptor())
            .addNetworkInterceptor(createNetworkSecurityInterceptor())
            .certificatePinner(createCertificatePinner())
            .sslSocketFactory(createSSLSocketFactory(), createTrustManager())
            .hostnameVerifier(createHostnameVerifier())
            .apply {
                if (com.cadence.cadence.BuildConfig.DEBUG) {
                    addInterceptor(createSecureLoggingInterceptor())
                }
            }
            .build()
    }
    
    /**
     * 创建证书固定配置
     */
    private fun createCertificatePinner(): CertificatePinner {
        return CertificatePinner.Builder()
            .add("generativelanguage.googleapis.com", *GOOGLE_API_PINS)
            .add("*.googleapis.com", *GOOGLE_API_PINS)
            .build()
    }
    
    /**
     * 创建请求签名拦截器
     */
    private fun createRequestSigningInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            
            // 生成请求签名
            val timestamp = System.currentTimeMillis().toString()
            val nonce = generateNonce()
            val signature = generateRequestSignature(originalRequest, timestamp, nonce)
            
            val signedRequest = originalRequest.newBuilder()
                .addHeader("X-Timestamp", timestamp)
                .addHeader("X-Nonce", nonce)
                .addHeader("X-Signature", signature)
                .addHeader("X-Client-Version", getClientVersion())
                .build()
            
            chain.proceed(signedRequest)
        }
    }
    
    /**
     * 创建安全头拦截器
     */
    private fun createSecurityHeadersInterceptor(): Interceptor {
        return Interceptor { chain ->
            val originalRequest = chain.request()
            
            val secureRequest = originalRequest.newBuilder()
                .addHeader("X-Requested-With", "XMLHttpRequest")
                .addHeader("Cache-Control", "no-cache, no-store, must-revalidate")
                .addHeader("Pragma", "no-cache")
                .addHeader("Expires", "0")
                .addHeader("X-Content-Type-Options", "nosniff")
                .addHeader("X-Frame-Options", "DENY")
                .addHeader("X-XSS-Protection", "1; mode=block")
                .build()
            
            chain.proceed(secureRequest)
        }
    }
    
    /**
     * 创建网络安全拦截器
     */
    private fun createNetworkSecurityInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            
            // 验证请求URL的安全性
            if (!isSecureUrl(request.url.toString())) {
                throw SecurityException("不安全的请求URL: ${request.url}")
            }
            
            val response = chain.proceed(request)
            
            // 验证响应的安全性
            validateResponse(response)
            
            response
        }
    }
    
    /**
     * 创建安全的日志拦截器
     */
    private fun createSecureLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor { message ->
            // 过滤敏感信息
            val sanitizedMessage = sanitizeLogMessage(message)
            Timber.d("Network: $sanitizedMessage")
        }.apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
    }
    
    /**
     * 创建SSL Socket Factory
     */
    private fun createSSLSocketFactory(): SSLSocketFactory {
        return try {
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, arrayOf(createTrustManager()), java.security.SecureRandom())
            sslContext.socketFactory
        } catch (e: Exception) {
            Timber.e(e, "SSL Socket Factory创建失败")
            throw SecurityException("SSL配置失败", e)
        }
    }
    
    /**
     * 创建信任管理器
     */
    private fun createTrustManager(): X509TrustManager {
        return object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
                // 客户端证书验证
            }
            
            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
                // 服务器证书验证
                try {
                    // 验证证书链
                    for (cert in chain) {
                        cert.checkValidity()
                    }
                    
                    // 额外的证书验证逻辑
                    validateCertificateChain(chain)
                } catch (e: CertificateException) {
                    Timber.e(e, "服务器证书验证失败")
                    throw e
                }
            }
            
            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return arrayOf()
            }
        }
    }
    
    /**
     * 创建主机名验证器
     */
    private fun createHostnameVerifier(): HostnameVerifier {
        return HostnameVerifier { hostname, session ->
            // 验证主机名
            val allowedHosts = listOf(
                "generativelanguage.googleapis.com",
                "googleapis.com"
            )
            
            allowedHosts.any { allowedHost ->
                hostname == allowedHost || hostname.endsWith(".$allowedHost")
            }
        }
    }
    
    /**
     * 生成随机数
     */
    private fun generateNonce(): String {
        val bytes = ByteArray(16)
        java.security.SecureRandom().nextBytes(bytes)
        return bytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 生成请求签名
     */
    private fun generateRequestSignature(request: Request, timestamp: String, nonce: String): String {
        return try {
            val method = request.method
            val url = request.url.toString()
            val body = request.body?.let { requestBody ->
                val buffer = okio.Buffer()
                requestBody.writeTo(buffer)
                buffer.readUtf8()
            } ?: ""
            
            val signatureData = "$method|$url|$body|$timestamp|$nonce"
            val hash = MessageDigest.getInstance("SHA-256")
                .digest(signatureData.toByteArray())
            
            hash.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Timber.e(e, "请求签名生成失败")
            ""
        }
    }
    
    /**
     * 获取客户端版本
     */
    private fun getClientVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName}-${packageInfo.versionCode}"
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * 验证URL安全性
     */
    private fun isSecureUrl(url: String): Boolean {
        return url.startsWith("https://") && 
               (url.contains("googleapis.com") || url.contains("google.com"))
    }
    
    /**
     * 验证响应安全性
     */
    private fun validateResponse(response: Response) {
        // 检查响应状态码
        if (response.code !in 200..299) {
            Timber.w("收到非成功响应: ${response.code}")
        }
        
        // 检查响应头
        val contentType = response.header("Content-Type")
        if (contentType != null && !contentType.startsWith("application/json")) {
            Timber.w("意外的响应内容类型: $contentType")
        }
        
        // 检查响应大小
        val contentLength = response.header("Content-Length")?.toLongOrNull()
        if (contentLength != null && contentLength > 10 * 1024 * 1024) { // 10MB
            Timber.w("响应内容过大: $contentLength bytes")
        }
    }
    
    /**
     * 验证证书链
     */
    private fun validateCertificateChain(chain: Array<X509Certificate>) {
        // 额外的证书验证逻辑
        for (cert in chain) {
            // 检查证书有效期
            cert.checkValidity()
            
            // 检查证书主题
            val subject = cert.subjectDN.name
            Timber.d("证书主题: $subject")
            
            // 可以添加更多的证书验证逻辑
        }
    }
    
    /**
     * 清理日志消息中的敏感信息
     */
    private fun sanitizeLogMessage(message: String): String {
        var sanitized = message
        
        // 移除API密钥
        sanitized = sanitized.replace(Regex("key=[^&\\s]+"), "key=***")
        
        // 移除认证令牌
        sanitized = sanitized.replace(Regex("Authorization: Bearer [^\\s]+"), "Authorization: Bearer ***")
        
        // 移除其他敏感信息
        sanitized = sanitized.replace(Regex("token=[^&\\s]+"), "token=***")
        sanitized = sanitized.replace(Regex("password=[^&\\s]+"), "password=***")
        
        return sanitized
    }
    
    /**
     * 验证API响应完整性
     */
    suspend fun verifyResponseIntegrity(responseBody: String, expectedHash: String?): Boolean {
        return if (expectedHash != null) {
            cryptoManager.verifyIntegrity(responseBody, expectedHash)
        } else {
            true // 如果没有提供哈希值，跳过验证
        }
    }
    
    /**
     * 加密API请求体
     */
    suspend fun encryptRequestBody(requestBody: String): String {
        return try {
            val encryptedData = cryptoManager.encrypt(requestBody)
            // 将加密数据编码为Base64
            android.util.Base64.encodeToString(
                "${encryptedData.encryptedData.joinToString(",")};${encryptedData.iv.joinToString(",")}".toByteArray(),
                android.util.Base64.DEFAULT
            )
        } catch (e: Exception) {
            Timber.e(e, "请求体加密失败")
            requestBody // 加密失败时返回原始数据
        }
    }
    
    /**
     * 解密API响应体
     */
    suspend fun decryptResponseBody(encryptedResponse: String): String {
        return try {
            val decodedData = String(android.util.Base64.decode(encryptedResponse, android.util.Base64.DEFAULT))
            val parts = decodedData.split(";")
            
            if (parts.size == 2) {
                val encryptedData = parts[0].split(",").map { it.toByte() }.toByteArray()
                val iv = parts[1].split(",").map { it.toByte() }.toByteArray()
                
                val encryptedDataObj = EncryptedData(encryptedData, iv)
                cryptoManager.decrypt(encryptedDataObj)
            } else {
                encryptedResponse // 如果格式不正确，返回原始数据
            }
        } catch (e: Exception) {
            Timber.e(e, "响应体解密失败")
            encryptedResponse // 解密失败时返回原始数据
        }
    }
}
