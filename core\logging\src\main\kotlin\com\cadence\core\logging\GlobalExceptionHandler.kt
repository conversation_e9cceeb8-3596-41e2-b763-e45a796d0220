package com.cadence.core.logging

import android.content.Context
import android.os.Build
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 全局异常处理器
 * 负责捕获和处理应用中的所有未处理异常
 */
@Singleton
class GlobalExceptionHandler @Inject constructor(
    private val context: Context,
    private val errorClassifier: ErrorClassifier,
    private val errorRecoveryManager: ErrorRecoveryManager,
    private val crashReportManager: CrashReportManager,
    private val structuredLogger: StructuredLogger
) : Thread.UncaughtExceptionHandler {
    
    companion object {
        private const val CRASH_LOG_FILE = "crash_logs.txt"
        private const val MAX_CRASH_REPORTS = 50
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    
    // 异常状态管理
    private val _exceptionState = MutableStateFlow(ExceptionState())
    val exceptionState: StateFlow<ExceptionState> = _exceptionState.asStateFlow()
    
    // 异常统计
    private val exceptionStats = mutableMapOf<String, Int>()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    init {
        // 设置为默认的未捕获异常处理器
        Thread.setDefaultUncaughtExceptionHandler(this)
        Timber.d("全局异常处理器已初始化")
    }
    
    /**
     * 处理未捕获的异常
     */
    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            handleException(thread, exception, isFatal = true)
        } catch (e: Exception) {
            Timber.e(e, "处理未捕获异常时发生错误")
        } finally {
            // 调用默认处理器确保应用正常退出
            defaultHandler?.uncaughtException(thread, exception)
        }
    }
    
    /**
     * 处理可恢复的异常
     */
    fun handleRecoverableException(
        exception: Throwable,
        context: String = "unknown",
        additionalData: Map<String, Any> = emptyMap()
    ) {
        scope.launch {
            try {
                handleException(
                    thread = Thread.currentThread(),
                    exception = exception,
                    isFatal = false,
                    context = context,
                    additionalData = additionalData
                )
            } catch (e: Exception) {
                Timber.e(e, "处理可恢复异常时发生错误")
            }
        }
    }
    
    /**
     * 核心异常处理逻辑
     */
    private suspend fun handleException(
        thread: Thread,
        exception: Throwable,
        isFatal: Boolean,
        context: String = "uncaught",
        additionalData: Map<String, Any> = emptyMap()
    ) = withContext(Dispatchers.IO) {
        val timestamp = System.currentTimeMillis()
        val exceptionId = generateExceptionId(exception, timestamp)
        
        // 分类异常
        val classification = errorClassifier.classifyError(exception)
        
        // 创建异常报告
        val exceptionReport = ExceptionReport(
            id = exceptionId,
            timestamp = timestamp,
            threadName = thread.name,
            exception = exception,
            classification = classification,
            isFatal = isFatal,
            context = context,
            additionalData = additionalData,
            deviceInfo = collectDeviceInfo(),
            stackTrace = getStackTrace(exception)
        )
        
        // 记录结构化日志
        logException(exceptionReport)
        
        // 更新异常统计
        updateExceptionStats(classification.category)
        
        // 更新异常状态
        updateExceptionState(exceptionReport)
        
        // 尝试错误恢复
        if (!isFatal) {
            val recoveryResult = errorRecoveryManager.attemptRecovery(exceptionReport)
            if (recoveryResult.isSuccess) {
                Timber.i("异常恢复成功: ${exceptionReport.id}")
                structuredLogger.info(
                    message = "Exception recovery successful",
                    context = mapOf(
                        "exception_id" to exceptionReport.id,
                        "recovery_strategy" to recoveryResult.strategy,
                        "recovery_time_ms" to recoveryResult.recoveryTimeMs.toString()
                    )
                )
            }
        }
        
        // 生成崩溃报告
        if (isFatal || classification.severity == ErrorSeverity.CRITICAL) {
            crashReportManager.generateCrashReport(exceptionReport)
        }
        
        // 保存到本地文件
        saveCrashToFile(exceptionReport)
    }
    
    /**
     * 记录异常日志
     */
    private suspend fun logException(report: ExceptionReport) {
        val logLevel = when (report.classification.severity) {
            ErrorSeverity.LOW -> LogLevel.INFO
            ErrorSeverity.MEDIUM -> LogLevel.WARN
            ErrorSeverity.HIGH -> LogLevel.ERROR
            ErrorSeverity.CRITICAL -> LogLevel.FATAL
        }
        
        structuredLogger.log(
            level = logLevel,
            message = "Exception occurred: ${report.exception.javaClass.simpleName}",
            context = mapOf(
                "exception_id" to report.id,
                "thread" to report.threadName,
                "category" to report.classification.category,
                "severity" to report.classification.severity.name,
                "is_fatal" to report.isFatal.toString(),
                "context" to report.context,
                "message" to (report.exception.message ?: "No message"),
                "stack_trace" to report.stackTrace
            ),
            exception = report.exception
        )
    }
    
    /**
     * 生成异常ID
     */
    private fun generateExceptionId(exception: Throwable, timestamp: Long): String {
        val exceptionHash = exception.javaClass.simpleName.hashCode()
        val messageHash = exception.message?.hashCode() ?: 0
        return "EXC_${timestamp}_${Math.abs(exceptionHash + messageHash)}"
    }
    
    /**
     * 获取异常堆栈跟踪
     */
    private fun getStackTrace(exception: Throwable): String {
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)
        exception.printStackTrace(printWriter)
        return stringWriter.toString()
    }
    
    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE,
            hardware = Build.HARDWARE,
            totalMemory = getTotalMemory(),
            availableMemory = getAvailableMemory(),
            storageSpace = getAvailableStorage()
        )
    }
    
    /**
     * 获取总内存
     */
    private fun getTotalMemory(): Long {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val memInfo = android.app.ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memInfo)
            memInfo.totalMem
        } catch (e: Exception) {
            -1L
        }
    }
    
    /**
     * 获取可用内存
     */
    private fun getAvailableMemory(): Long {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val memInfo = android.app.ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memInfo)
            memInfo.availMem
        } catch (e: Exception) {
            -1L
        }
    }
    
    /**
     * 获取可用存储空间
     */
    private fun getAvailableStorage(): Long {
        return try {
            val stat = android.os.StatFs(context.filesDir.path)
            stat.availableBytes
        } catch (e: Exception) {
            -1L
        }
    }
    
    /**
     * 更新异常统计
     */
    private fun updateExceptionStats(category: String) {
        exceptionStats[category] = exceptionStats.getOrDefault(category, 0) + 1
    }
    
    /**
     * 更新异常状态
     */
    private fun updateExceptionState(report: ExceptionReport) {
        val currentState = _exceptionState.value
        _exceptionState.value = currentState.copy(
            totalExceptions = currentState.totalExceptions + 1,
            lastExceptionTime = report.timestamp,
            lastExceptionId = report.id,
            fatalExceptions = if (report.isFatal) currentState.fatalExceptions + 1 else currentState.fatalExceptions,
            exceptionsByCategory = exceptionStats.toMap()
        )
    }
    
    /**
     * 保存崩溃信息到文件
     */
    private suspend fun saveCrashToFile(report: ExceptionReport) {
        try {
            val crashFile = java.io.File(context.filesDir, CRASH_LOG_FILE)
            val crashInfo = buildString {
                appendLine("=== CRASH REPORT ===")
                appendLine("ID: ${report.id}")
                appendLine("Timestamp: ${dateFormat.format(Date(report.timestamp))}")
                appendLine("Thread: ${report.threadName}")
                appendLine("Fatal: ${report.isFatal}")
                appendLine("Category: ${report.classification.category}")
                appendLine("Severity: ${report.classification.severity}")
                appendLine("Context: ${report.context}")
                appendLine("Exception: ${report.exception.javaClass.simpleName}")
                appendLine("Message: ${report.exception.message}")
                appendLine("Device: ${report.deviceInfo.manufacturer} ${report.deviceInfo.model}")
                appendLine("Android: ${report.deviceInfo.androidVersion} (API ${report.deviceInfo.apiLevel})")
                appendLine("Stack Trace:")
                appendLine(report.stackTrace)
                appendLine("=== END REPORT ===")
                appendLine()
            }
            
            crashFile.appendText(crashInfo)
            
            // 限制文件大小
            if (crashFile.length() > 5 * 1024 * 1024) { // 5MB
                rotateCrashFile(crashFile)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "保存崩溃信息到文件失败")
        }
    }
    
    /**
     * 轮转崩溃日志文件
     */
    private fun rotateCrashFile(crashFile: java.io.File) {
        try {
            val backupFile = java.io.File(context.filesDir, "crash_logs_backup.txt")
            if (backupFile.exists()) {
                backupFile.delete()
            }
            crashFile.renameTo(backupFile)
        } catch (e: Exception) {
            Timber.e(e, "轮转崩溃日志文件失败")
        }
    }
    
    /**
     * 获取异常统计信息
     */
    fun getExceptionStatistics(): ExceptionStatistics {
        val currentState = _exceptionState.value
        return ExceptionStatistics(
            totalExceptions = currentState.totalExceptions,
            fatalExceptions = currentState.fatalExceptions,
            recoverableExceptions = currentState.totalExceptions - currentState.fatalExceptions,
            exceptionsByCategory = currentState.exceptionsByCategory,
            lastExceptionTime = currentState.lastExceptionTime
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        Timber.d("全局异常处理器已清理")
    }
}

/**
 * 异常报告数据类
 */
data class ExceptionReport(
    val id: String,
    val timestamp: Long,
    val threadName: String,
    val exception: Throwable,
    val classification: ErrorClassification,
    val isFatal: Boolean,
    val context: String,
    val additionalData: Map<String, Any>,
    val deviceInfo: DeviceInfo,
    val stackTrace: String
)

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val manufacturer: String,
    val model: String,
    val androidVersion: String,
    val apiLevel: Int,
    val brand: String,
    val device: String,
    val hardware: String,
    val totalMemory: Long,
    val availableMemory: Long,
    val storageSpace: Long
)

/**
 * 异常状态数据类
 */
data class ExceptionState(
    val totalExceptions: Long = 0,
    val fatalExceptions: Long = 0,
    val lastExceptionTime: Long = 0,
    val lastExceptionId: String = "",
    val exceptionsByCategory: Map<String, Int> = emptyMap()
)

/**
 * 异常统计数据类
 */
data class ExceptionStatistics(
    val totalExceptions: Long,
    val fatalExceptions: Long,
    val recoverableExceptions: Long,
    val exceptionsByCategory: Map<String, Int>,
    val lastExceptionTime: Long
)
