package com.cadence.ui.favorites

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.*
import com.cadence.domain.usecase.ManageFavoritesUseCase
import com.cadence.domain.repository.TranslationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 收藏详情视图模型
 * 管理收藏项详情的UI状态和业务逻辑
 */
@HiltViewModel
class FavoriteDetailViewModel @Inject constructor(
    private val manageFavoritesUseCase: ManageFavoritesUseCase,
    private val translationRepository: TranslationRepository
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(FavoriteDetailUiState())
    val uiState: StateFlow<FavoriteDetailUiState> = _uiState.asStateFlow()
    
    // 收藏项详情
    private val _favoriteItem = MutableStateFlow<FavoriteItem?>(null)
    val favoriteItem: StateFlow<FavoriteItem?> = _favoriteItem.asStateFlow()
    
    // 翻译详情
    private val _translation = MutableStateFlow<Translation?>(null)
    val translation: StateFlow<Translation?> = _translation.asStateFlow()
    
    // 收藏夹列表
    private val _folders = MutableStateFlow<List<FavoriteFolder>>(emptyList())
    val folders: StateFlow<List<FavoriteFolder>> = _folders.asStateFlow()
    
    init {
        loadFolders()
    }
    
    /**
     * 加载收藏项详情
     */
    fun loadFavoriteDetail(favoriteId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // 获取收藏项详情
                val result = manageFavoritesUseCase.getFavoriteItemById(favoriteId)
                result.fold(
                    onSuccess = { favoriteItem ->
                        _favoriteItem.value = favoriteItem
                        
                        // 加载对应的翻译详情
                        loadTranslationDetail(favoriteItem.translationId)
                        
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "加载收藏项详情失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "加载收藏项详情失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "加载收藏项详情异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载收藏项详情异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载翻译详情
     */
    private fun loadTranslationDetail(translationId: String) {
        viewModelScope.launch {
            try {
                translationRepository.getTranslationById(translationId)
                    .catch { error ->
                        Timber.e(error, "加载翻译详情失败")
                        _uiState.value = _uiState.value.copy(
                            error = "加载翻译详情失败: ${error.message}"
                        )
                    }
                    .collect { translation ->
                        _translation.value = translation
                    }
            } catch (e: Exception) {
                Timber.e(e, "加载翻译详情异常")
                _uiState.value = _uiState.value.copy(
                    error = "加载翻译详情异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载收藏夹列表
     */
    private fun loadFolders() {
        viewModelScope.launch {
            try {
                manageFavoritesUseCase.getAllFolders()
                    .catch { error ->
                        Timber.e(error, "加载收藏夹列表失败")
                    }
                    .collect { folders ->
                        _folders.value = folders
                    }
            } catch (e: Exception) {
                Timber.e(e, "加载收藏夹列表异常")
            }
        }
    }
    
    /**
     * 更新收藏项
     */
    fun updateFavoriteItem(
        favoriteId: String,
        note: String?,
        tags: List<String>,
        priority: FavoritePriority
    ) {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.updateFavoriteItem(
                    favoriteId = favoriteId,
                    note = note,
                    tags = tags,
                    priority = priority
                )
                result.fold(
                    onSuccess = {
                        Timber.d("更新收藏项成功: $favoriteId")
                        _uiState.value = _uiState.value.copy(message = "更新成功")
                        
                        // 重新加载收藏项详情
                        loadFavoriteDetail(favoriteId)
                    },
                    onFailure = { error ->
                        Timber.e(error, "更新收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            error = "更新收藏项失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新收藏项异常")
                _uiState.value = _uiState.value.copy(
                    error = "更新收藏项异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 移动收藏项到其他收藏夹
     */
    fun moveFavoriteItem(favoriteId: String, targetFolderId: String) {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.moveFavoriteItem(favoriteId, targetFolderId)
                result.fold(
                    onSuccess = {
                        Timber.d("移动收藏项成功: $favoriteId -> $targetFolderId")
                        _uiState.value = _uiState.value.copy(message = "移动成功")
                        
                        // 重新加载收藏项详情
                        loadFavoriteDetail(favoriteId)
                    },
                    onFailure = { error ->
                        Timber.e(error, "移动收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            error = "移动收藏项失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "移动收藏项异常")
                _uiState.value = _uiState.value.copy(
                    error = "移动收藏项异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 删除收藏项
     */
    fun deleteFavoriteItem(favoriteId: String) {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.removeFavoriteItem(favoriteId)
                result.fold(
                    onSuccess = {
                        Timber.d("删除收藏项成功: $favoriteId")
                        _uiState.value = _uiState.value.copy(message = "删除成功")
                    },
                    onFailure = { error ->
                        Timber.e(error, "删除收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            error = "删除收藏项失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "删除收藏项异常")
                _uiState.value = _uiState.value.copy(
                    error = "删除收藏项异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
}

/**
 * 收藏详情UI状态
 */
data class FavoriteDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
