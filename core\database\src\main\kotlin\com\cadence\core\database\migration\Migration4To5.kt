package com.cadence.core.database.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 数据库版本4到5的迁移
 * 添加收藏功能相关表
 */
val MIGRATION_4_5 = object : Migration(4, 5) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建收藏夹表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_folders` (
                `id` TEXT NOT NULL,
                `name` TEXT NOT NULL,
                `description` TEXT,
                `color` TEXT NOT NULL DEFAULT '#2196F3',
                `icon` TEXT,
                `is_default` INTEGER NOT NULL DEFAULT 0,
                `sort_order` INTEGER NOT NULL DEFAULT 0,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """.trimIndent())
        
        // 创建收藏夹索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_favorite_folders_name` ON `favorite_folders` (`name`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_folders_is_default` ON `favorite_folders` (`is_default`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_folders_sort_order` ON `favorite_folders` (`sort_order`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_folders_created_at` ON `favorite_folders` (`created_at`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_folders_updated_at` ON `favorite_folders` (`updated_at`)")
        
        // 创建收藏项表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_items` (
                `id` TEXT NOT NULL,
                `translation_id` TEXT NOT NULL,
                `folder_id` TEXT NOT NULL,
                `note` TEXT,
                `tags` TEXT NOT NULL DEFAULT '[]',
                `priority` TEXT NOT NULL DEFAULT 'NORMAL',
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`),
                FOREIGN KEY(`folder_id`) REFERENCES `favorite_folders`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE,
                FOREIGN KEY(`translation_id`) REFERENCES `translations`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE
            )
        """.trimIndent())
        
        // 创建收藏项索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_favorite_items_translation_id` ON `favorite_items` (`translation_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_items_folder_id` ON `favorite_items` (`folder_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_items_priority` ON `favorite_items` (`priority`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_items_created_at` ON `favorite_items` (`created_at`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_items_updated_at` ON `favorite_items` (`updated_at`)")
        
        // 创建收藏夹配置表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_config` (
                `id` TEXT NOT NULL DEFAULT 'default_config',
                `auto_sync` INTEGER NOT NULL DEFAULT 1,
                `default_folder_id` TEXT,
                `max_items_per_folder` INTEGER NOT NULL DEFAULT 1000,
                `enable_notifications` INTEGER NOT NULL DEFAULT 1,
                `show_item_count` INTEGER NOT NULL DEFAULT 1,
                `compact_view` INTEGER NOT NULL DEFAULT 0,
                `enable_tags` INTEGER NOT NULL DEFAULT 1,
                `enable_priority` INTEGER NOT NULL DEFAULT 1,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """.trimIndent())
        
        // 创建收藏夹配置索引
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_config_updated_at` ON `favorite_config` (`updated_at`)")
        
        // 创建收藏夹操作记录表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_operations` (
                `id` TEXT NOT NULL,
                `operation_type` TEXT NOT NULL,
                `target_id` TEXT NOT NULL,
                `details` TEXT,
                `timestamp` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """.trimIndent())
        
        // 创建收藏夹操作记录索引
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_operations_operation_type` ON `favorite_operations` (`operation_type`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_operations_target_id` ON `favorite_operations` (`target_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_operations_timestamp` ON `favorite_operations` (`timestamp`)")
        
        // 创建收藏夹同步信息表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_sync_info` (
                `id` TEXT NOT NULL,
                `item_id` TEXT NOT NULL,
                `item_type` TEXT NOT NULL,
                `status` TEXT NOT NULL,
                `last_sync_time` INTEGER,
                `sync_error` TEXT,
                `version` INTEGER NOT NULL DEFAULT 1,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """.trimIndent())
        
        // 创建收藏夹同步信息索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_favorite_sync_info_item_id_item_type` ON `favorite_sync_info` (`item_id`, `item_type`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_sync_info_status` ON `favorite_sync_info` (`status`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_sync_info_last_sync_time` ON `favorite_sync_info` (`last_sync_time`)")
        
        // 创建收藏夹使用统计表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_usage_stats` (
                `id` TEXT NOT NULL,
                `folder_id` TEXT NOT NULL,
                `access_count` INTEGER NOT NULL DEFAULT 0,
                `last_access_time` INTEGER NOT NULL,
                `average_session_duration` INTEGER NOT NULL DEFAULT 0,
                `most_used_tags` TEXT NOT NULL DEFAULT '[]',
                `peak_usage_hour` INTEGER NOT NULL DEFAULT 0,
                `created_at` INTEGER NOT NULL,
                `updated_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`),
                FOREIGN KEY(`folder_id`) REFERENCES `favorite_folders`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE
            )
        """.trimIndent())
        
        // 创建收藏夹使用统计索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_favorite_usage_stats_folder_id` ON `favorite_usage_stats` (`folder_id`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_usage_stats_access_count` ON `favorite_usage_stats` (`access_count`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_usage_stats_last_access_time` ON `favorite_usage_stats` (`last_access_time`)")
        
        // 创建收藏夹搜索建议表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS `favorite_search_suggestions` (
                `id` TEXT NOT NULL,
                `type` TEXT NOT NULL,
                `value` TEXT NOT NULL,
                `count` INTEGER NOT NULL DEFAULT 1,
                `last_used` INTEGER NOT NULL,
                `created_at` INTEGER NOT NULL,
                PRIMARY KEY(`id`)
            )
        """.trimIndent())
        
        // 创建收藏夹搜索建议索引
        database.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_favorite_search_suggestions_type_value` ON `favorite_search_suggestions` (`type`, `value`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_search_suggestions_count` ON `favorite_search_suggestions` (`count`)")
        database.execSQL("CREATE INDEX IF NOT EXISTS `index_favorite_search_suggestions_last_used` ON `favorite_search_suggestions` (`last_used`)")
        
        // 插入默认收藏夹配置
        val currentTime = System.currentTimeMillis()
        database.execSQL("""
            INSERT OR IGNORE INTO `favorite_config` (
                `id`, `auto_sync`, `default_folder_id`, `max_items_per_folder`,
                `enable_notifications`, `show_item_count`, `compact_view`,
                `enable_tags`, `enable_priority`, `created_at`, `updated_at`
            ) VALUES (
                'default_config', 1, NULL, 1000,
                1, 1, 0,
                1, 1, $currentTime, $currentTime
            )
        """.trimIndent())
        
        // 创建默认收藏夹
        val defaultFolderId = "default_folder_${System.currentTimeMillis()}"
        database.execSQL("""
            INSERT OR IGNORE INTO `favorite_folders` (
                `id`, `name`, `description`, `color`, `icon`,
                `is_default`, `sort_order`, `created_at`, `updated_at`
            ) VALUES (
                '$defaultFolderId', '默认收藏夹', '系统默认收藏夹', '#2196F3', 'star',
                1, 0, $currentTime, $currentTime
            )
        """.trimIndent())
        
        // 更新默认收藏夹配置
        database.execSQL("""
            UPDATE `favorite_config` 
            SET `default_folder_id` = '$defaultFolderId', `updated_at` = $currentTime 
            WHERE `id` = 'default_config'
        """.trimIndent())
    }
}
