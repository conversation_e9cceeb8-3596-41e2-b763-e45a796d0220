package com.cadence.core.performance

import android.content.Context
import android.os.Handler
import android.os.Looper
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 综合性能监控器
 * 负责监控应用的整体性能，包括启动、内存、网络、UI等各个方面
 */
@Singleton
class PerformanceMonitor @Inject constructor(
    private val context: Context,
    private val startupTimeTracker: StartupTimeTracker,
    private val memoryManager: MemoryManager,
    private val networkPerformanceOptimizer: NetworkPerformanceOptimizer
) {
    
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private val mainHandler = Handler(Looper.getMainLooper())
    
    // 性能监控状态
    private val _monitoringState = MutableStateFlow(PerformanceMonitoringState())
    val monitoringState: StateFlow<PerformanceMonitoringState> = _monitoringState.asStateFlow()
    
    // 性能指标收集
    private val performanceMetrics = ConcurrentHashMap<String, PerformanceMetric>()
    private val performanceAlerts = mutableListOf<PerformanceAlert>()
    
    // UI性能监控
    private var frameDropCount = 0
    private var totalFrames = 0
    private var isUIMonitoring = false
    
    // 监控配置
    private val monitoringConfig = PerformanceMonitoringConfig(
        enableStartupMonitoring = true,
        enableMemoryMonitoring = true,
        enableNetworkMonitoring = true,
        enableUIMonitoring = true,
        monitoringIntervalMs = 30000,
        alertThresholds = PerformanceAlertThresholds(
            startupTimeMs = 3000,
            memoryUsageRatio = 0.85f,
            frameDropRate = 0.1f,
            networkErrorRate = 0.2f
        )
    )
    
    /**
     * 开始性能监控
     */
    fun startMonitoring() {
        if (_monitoringState.value.isMonitoring) {
            Timber.d("性能监控已在运行")
            return
        }
        
        _monitoringState.value = _monitoringState.value.copy(
            isMonitoring = true,
            startTime = System.currentTimeMillis()
        )
        
        // 启动各项监控
        if (monitoringConfig.enableStartupMonitoring) {
            startStartupMonitoring()
        }
        
        if (monitoringConfig.enableMemoryMonitoring) {
            memoryManager.startMemoryMonitoring(monitoringConfig.monitoringIntervalMs)
        }
        
        if (monitoringConfig.enableUIMonitoring) {
            startUIPerformanceMonitoring()
        }
        
        // 启动定期性能检查
        startPeriodicPerformanceCheck()
        
        Timber.i("性能监控已启动")
    }
    
    /**
     * 停止性能监控
     */
    fun stopMonitoring() {
        _monitoringState.value = _monitoringState.value.copy(
            isMonitoring = false,
            endTime = System.currentTimeMillis()
        )
        
        memoryManager.stopMemoryMonitoring()
        stopUIPerformanceMonitoring()
        
        Timber.i("性能监控已停止")
    }
    
    /**
     * 记录性能指标
     */
    fun recordMetric(name: String, value: Double, unit: String = "", tags: Map<String, String> = emptyMap()) {
        val metric = PerformanceMetric(
            name = name,
            value = value,
            unit = unit,
            timestamp = System.currentTimeMillis(),
            tags = tags
        )
        
        performanceMetrics[name] = metric
        
        // 检查是否需要发出性能警报
        checkPerformanceAlert(metric)
        
        Timber.d("记录性能指标: $name = $value $unit")
    }
    
    /**
     * 记录性能事件
     */
    fun recordEvent(eventName: String, duration: Long, metadata: Map<String, Any> = emptyMap()) {
        val event = PerformanceEvent(
            name = eventName,
            duration = duration,
            timestamp = System.currentTimeMillis(),
            metadata = metadata
        )
        
        // 更新监控状态
        val currentEvents = _monitoringState.value.events.toMutableList()
        currentEvents.add(event)
        
        _monitoringState.value = _monitoringState.value.copy(
            events = currentEvents,
            lastEventTime = System.currentTimeMillis()
        )
        
        Timber.d("记录性能事件: $eventName, 耗时: ${duration}ms")
    }
    
    /**
     * 获取综合性能报告
     */
    fun getPerformanceReport(): PerformanceReport {
        val startupReport = startupTimeTracker.getStartupReport()
        val memoryReport = memoryManager.getMemoryReport()
        val networkReport = networkPerformanceOptimizer.getNetworkPerformanceReport()
        
        val uiPerformanceScore = calculateUIPerformanceScore()
        val overallScore = calculateOverallPerformanceScore(startupReport, memoryReport, networkReport, uiPerformanceScore)
        
        return PerformanceReport(
            timestamp = System.currentTimeMillis(),
            overallScore = overallScore,
            startupReport = startupReport,
            memoryReport = memoryReport,
            networkReport = networkReport,
            uiPerformanceScore = uiPerformanceScore,
            frameDropRate = if (totalFrames > 0) frameDropCount.toFloat() / totalFrames else 0f,
            metrics = performanceMetrics.values.toList(),
            alerts = performanceAlerts.toList(),
            monitoringDuration = System.currentTimeMillis() - _monitoringState.value.startTime
        )
    }
    
    /**
     * 启动启动性能监控
     */
    private fun startStartupMonitoring() {
        // 启动时间跟踪已在Application中启动
        Timber.d("启动性能监控已激活")
    }
    
    /**
     * 启动UI性能监控
     */
    private fun startUIPerformanceMonitoring() {
        if (isUIMonitoring) return
        
        isUIMonitoring = true
        
        // 监控主线程帧率
        val choreographer = android.view.Choreographer.getInstance()
        val frameCallback = object : android.view.Choreographer.FrameCallback {
            private var lastFrameTime = 0L
            
            override fun doFrame(frameTimeNanos: Long) {
                if (lastFrameTime > 0) {
                    val frameDuration = (frameTimeNanos - lastFrameTime) / 1_000_000 // 转换为毫秒
                    totalFrames++
                    
                    // 检测掉帧（超过16.67ms表示掉帧）
                    if (frameDuration > 16.67) {
                        frameDropCount++
                        
                        if (frameDuration > 50) { // 严重掉帧
                            Timber.w("检测到严重掉帧: ${frameDuration}ms")
                        }
                    }
                }
                
                lastFrameTime = frameTimeNanos
                
                if (isUIMonitoring) {
                    choreographer.postFrameCallback(this)
                }
            }
        }
        
        choreographer.postFrameCallback(frameCallback)
        Timber.d("UI性能监控已启动")
    }
    
    /**
     * 停止UI性能监控
     */
    private fun stopUIPerformanceMonitoring() {
        isUIMonitoring = false
        Timber.d("UI性能监控已停止")
    }
    
    /**
     * 启动定期性能检查
     */
    private fun startPeriodicPerformanceCheck() {
        scope.launch {
            while (_monitoringState.value.isMonitoring) {
                try {
                    performPerformanceCheck()
                    delay(monitoringConfig.monitoringIntervalMs)
                } catch (e: Exception) {
                    Timber.e(e, "定期性能检查过程中发生错误")
                }
            }
        }
    }
    
    /**
     * 执行性能检查
     */
    private suspend fun performPerformanceCheck() {
        // 检查内存使用情况
        val memoryInfo = memoryManager.getCurrentMemoryInfo()
        recordMetric("memory_usage_ratio", memoryInfo.memoryUsageRatio.toDouble(), "%")
        
        // 检查网络性能
        val networkReport = networkPerformanceOptimizer.getNetworkPerformanceReport()
        recordMetric("network_cache_hit_rate", networkReport.cacheHitRate.toDouble(), "%")
        recordMetric("network_average_response_time", networkReport.averageResponseTime, "ms")
        
        // 检查UI性能
        val frameDropRate = if (totalFrames > 0) frameDropCount.toFloat() / totalFrames else 0f
        recordMetric("ui_frame_drop_rate", frameDropRate.toDouble(), "%")
        
        // 清理过期数据
        cleanupOldData()
    }
    
    /**
     * 检查性能警报
     */
    private fun checkPerformanceAlert(metric: PerformanceMetric) {
        val alert = when (metric.name) {
            "memory_usage_ratio" -> {
                if (metric.value > monitoringConfig.alertThresholds.memoryUsageRatio) {
                    PerformanceAlert(
                        type = AlertType.MEMORY_HIGH,
                        message = "内存使用率过高: ${(metric.value * 100).toInt()}%",
                        severity = AlertSeverity.WARNING,
                        timestamp = System.currentTimeMillis(),
                        metric = metric
                    )
                } else null
            }
            "ui_frame_drop_rate" -> {
                if (metric.value > monitoringConfig.alertThresholds.frameDropRate) {
                    PerformanceAlert(
                        type = AlertType.UI_PERFORMANCE,
                        message = "UI掉帧率过高: ${(metric.value * 100).toInt()}%",
                        severity = AlertSeverity.WARNING,
                        timestamp = System.currentTimeMillis(),
                        metric = metric
                    )
                } else null
            }
            else -> null
        }
        
        alert?.let {
            performanceAlerts.add(it)
            Timber.w("性能警报: ${it.message}")
        }
    }
    
    /**
     * 计算UI性能分数
     */
    private fun calculateUIPerformanceScore(): Double {
        val frameDropRate = if (totalFrames > 0) frameDropCount.toFloat() / totalFrames else 0f
        return ((1.0 - frameDropRate) * 100).coerceIn(0.0, 100.0)
    }
    
    /**
     * 计算综合性能分数
     */
    private fun calculateOverallPerformanceScore(
        startupReport: StartupReport,
        memoryReport: MemoryReport,
        networkReport: NetworkPerformanceReport,
        uiScore: Double
    ): Double {
        // 启动性能分数
        val startupScore = when (startupReport.performanceGrade) {
            PerformanceGrade.EXCELLENT -> 100.0
            PerformanceGrade.GOOD -> 80.0
            PerformanceGrade.FAIR -> 60.0
            PerformanceGrade.POOR -> 40.0
            PerformanceGrade.VERY_POOR -> 20.0
        }
        
        // 内存性能分数
        val memoryScore = when (memoryReport.memoryPressureLevel) {
            MemoryPressureLevel.LOW -> 100.0
            MemoryPressureLevel.MEDIUM -> 75.0
            MemoryPressureLevel.HIGH -> 50.0
            MemoryPressureLevel.CRITICAL -> 25.0
        }
        
        // 网络性能分数
        val networkScore = (networkReport.cacheHitRate * 50 + 
                           (1.0 - (networkReport.errorRequests.toFloat() / networkReport.totalRequests.coerceAtLeast(1))) * 50)
            .coerceIn(0.0, 100.0)
        
        // 加权平均
        return (startupScore * 0.3 + memoryScore * 0.3 + networkScore * 0.2 + uiScore * 0.2)
    }
    
    /**
     * 清理过期数据
     */
    private fun cleanupOldData() {
        val currentTime = System.currentTimeMillis()
        val expiredTime = currentTime - 3600000 // 1小时
        
        // 清理过期指标
        val expiredMetrics = performanceMetrics.entries
            .filter { (_, metric) -> metric.timestamp < expiredTime }
            .map { it.key }
        
        expiredMetrics.forEach { key ->
            performanceMetrics.remove(key)
        }
        
        // 清理过期警报
        performanceAlerts.removeAll { it.timestamp < expiredTime }
        
        // 清理网络指标
        networkPerformanceOptimizer.cleanupOldMetrics()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        performanceMetrics.clear()
        performanceAlerts.clear()
        scope.cancel()
        Timber.d("性能监控器已清理")
    }
}

/**
 * 性能监控状态
 */
data class PerformanceMonitoringState(
    val isMonitoring: Boolean = false,
    val startTime: Long = 0,
    val endTime: Long = 0,
    val lastEventTime: Long = 0,
    val events: List<PerformanceEvent> = emptyList()
)

/**
 * 性能指标
 */
data class PerformanceMetric(
    val name: String,
    val value: Double,
    val unit: String,
    val timestamp: Long,
    val tags: Map<String, String>
)

/**
 * 性能事件
 */
data class PerformanceEvent(
    val name: String,
    val duration: Long,
    val timestamp: Long,
    val metadata: Map<String, Any>
)

/**
 * 性能报告
 */
data class PerformanceReport(
    val timestamp: Long,
    val overallScore: Double,
    val startupReport: StartupReport,
    val memoryReport: MemoryReport,
    val networkReport: NetworkPerformanceReport,
    val uiPerformanceScore: Double,
    val frameDropRate: Float,
    val metrics: List<PerformanceMetric>,
    val alerts: List<PerformanceAlert>,
    val monitoringDuration: Long
)

/**
 * 性能警报
 */
data class PerformanceAlert(
    val type: AlertType,
    val message: String,
    val severity: AlertSeverity,
    val timestamp: Long,
    val metric: PerformanceMetric
)

/**
 * 性能监控配置
 */
data class PerformanceMonitoringConfig(
    val enableStartupMonitoring: Boolean,
    val enableMemoryMonitoring: Boolean,
    val enableNetworkMonitoring: Boolean,
    val enableUIMonitoring: Boolean,
    val monitoringIntervalMs: Long,
    val alertThresholds: PerformanceAlertThresholds
)

/**
 * 性能警报阈值
 */
data class PerformanceAlertThresholds(
    val startupTimeMs: Long,
    val memoryUsageRatio: Float,
    val frameDropRate: Float,
    val networkErrorRate: Float
)

/**
 * 警报类型
 */
enum class AlertType {
    STARTUP_SLOW, MEMORY_HIGH, UI_PERFORMANCE, NETWORK_ERROR
}

/**
 * 警报严重程度
 */
enum class AlertSeverity {
    INFO, WARNING, ERROR, CRITICAL
}
