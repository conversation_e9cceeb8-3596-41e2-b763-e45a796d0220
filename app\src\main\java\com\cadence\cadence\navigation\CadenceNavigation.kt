package com.cadence.cadence.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.cadence.cadence.ui.components.CadenceBottomNavigation
import com.cadence.feature.translation.presentation.screen.TranslationScreen
import com.cadence.feature.translation.presentation.screen.TranslationHistoryScreen
import com.cadence.feature.settings.presentation.screen.SettingsScreen
import com.cadence.feature.settings.presentation.screen.ThemeSettingsScreen
import com.cadence.feature.settings.presentation.screen.LanguageSettingsScreen
import com.cadence.feature.settings.presentation.screen.NotificationSettingsScreen
import com.cadence.feature.settings.presentation.screen.PrivacySettingsScreen
import com.cadence.ui.favorites.FavoritesScreen
import com.cadence.ui.favorites.FavoriteDetailScreen
import com.cadence.ui.placeholder.LearningScreen
import com.cadence.ui.placeholder.CulturalScreen
import com.cadence.feature.settings.presentation.screen.SettingsScreen
import com.cadence.feature.settings.presentation.screen.ThemeSettingsScreen
import com.cadence.feature.settings.presentation.screen.LanguageSettingsScreen
import com.cadence.feature.settings.presentation.screen.NotificationSettingsScreen
import com.cadence.feature.settings.presentation.screen.PrivacySettingsScreen
import com.cadence.feature.translation.presentation.screen.TranslationScreen
import com.cadence.feature.translation.presentation.screen.TranslationHistoryScreen
import timber.log.Timber

/**
 * Cadence应用程序主导航系统
 * 
 * 负责：
 * - 管理应用内页面导航
 * - 配置底部导航栏
 * - 处理深度链接
 * - 管理导航状态
 */
@Composable
fun CadenceNavigation(
    navController: NavHostController = rememberNavController()
) {
    Scaffold(
        bottomBar = {
            CadenceBottomNavigation(navController = navController)
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = CadenceDestinations.TRANSLATION_ROUTE,
            modifier = Modifier.padding(paddingValues)
        ) {
            // 主翻译页面
            composable(CadenceDestinations.TRANSLATION_ROUTE) {
                Timber.d("导航到翻译页面")
                TranslationScreen(
                    onNavigateToHistory = {
                        navController.navigate(CadenceDestinations.HISTORY_ROUTE)
                    },
                    onNavigateToSettings = {
                        navController.navigate(CadenceDestinations.SETTINGS_ROUTE)
                    }
                )
            }

            // 历史记录页面
            composable(CadenceDestinations.HISTORY_ROUTE) {
                Timber.d("导航到历史记录页面")
                TranslationHistoryScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onTranslationClick = { translation ->
                        // 导航回翻译界面，可以在ViewModel中处理选中的翻译
                        navController.navigate(CadenceDestinations.TRANSLATION_ROUTE) {
                            popUpTo(CadenceDestinations.TRANSLATION_ROUTE) {
                                inclusive = true
                            }
                        }
                    }
                )
            }
            
            // 收藏页面
            composable(CadenceDestinations.FAVORITES_ROUTE) {
                Timber.d("导航到收藏页面")
                FavoritesScreen(
                    onNavigateToFavoriteDetail = { favoriteId ->
                        navController.navigate(
                            CadenceNavigationArgs.favoritesDetail(favoriteId)
                        )
                    }
                )
            }

            // 收藏详情页面
            composable(
                route = CadenceDestinations.FAVORITES_DETAIL_WITH_ID,
                arguments = listOf(
                    navArgument(CadenceDestinations.FAVORITE_ID_KEY) {
                        type = NavType.StringType
                    }
                )
            ) { backStackEntry ->
                val favoriteId = backStackEntry.arguments?.getString(CadenceDestinations.FAVORITE_ID_KEY) ?: ""
                Timber.d("导航到收藏详情页面: $favoriteId")
                FavoriteDetailScreen(
                    favoriteId = favoriteId,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            // 学习模式页面
            composable(CadenceDestinations.LEARNING_ROUTE) {
                Timber.d("导航到学习模式页面")
                LearningScreen()
            }

            // 设置页面
            composable(CadenceDestinations.SETTINGS_ROUTE) {
                Timber.d("导航到设置页面")
                SettingsScreen(
                    onNavigateToThemeSettings = {
                        navController.navigate(CadenceDestinations.THEME_SETTINGS_ROUTE)
                    },
                    onNavigateToLanguageSettings = {
                        navController.navigate(CadenceDestinations.LANGUAGE_SETTINGS_ROUTE)
                    },
                    onNavigateToNotificationSettings = {
                        navController.navigate(CadenceDestinations.NOTIFICATION_SETTINGS_ROUTE)
                    },
                    onNavigateToPrivacySettings = {
                        navController.navigate(CadenceDestinations.PRIVACY_SETTINGS_ROUTE)
                    },
                    onNavigateToAbout = {
                        // TODO: 实现关于页面导航
                        Timber.d("导航到关于页面 - 待实现")
                    }
                )
            }

            // 主题设置页面
            composable(CadenceDestinations.THEME_SETTINGS_ROUTE) {
                Timber.d("导航到主题设置页面")
                ThemeSettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }

            // 语言设置页面
            composable(CadenceDestinations.LANGUAGE_SETTINGS_ROUTE) {
                Timber.d("导航到语言设置页面")
                LanguageSettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }

            // 通知设置页面
            composable(CadenceDestinations.NOTIFICATION_SETTINGS_ROUTE) {
                Timber.d("导航到通知设置页面")
                NotificationSettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }

            // 隐私设置页面
            composable(CadenceDestinations.PRIVACY_SETTINGS_ROUTE) {
                Timber.d("导航到隐私设置页面")
                PrivacySettingsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }

            // 文化背景页面
            composable(CadenceDestinations.CULTURAL_ROUTE) {
                Timber.d("导航到文化背景页面")
                CulturalScreen()
            }
        }
    }
}



/**
 * 通用占位页面组件
 */
@Composable
private fun PlaceholderScreen(title: String) {
    androidx.compose.foundation.layout.Box(
        modifier = androidx.compose.ui.Modifier.fillMaxSize(),
        contentAlignment = androidx.compose.ui.Alignment.Center
    ) {
        androidx.compose.material3.Text(
            text = "$title 页面",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium
        )
    }
}