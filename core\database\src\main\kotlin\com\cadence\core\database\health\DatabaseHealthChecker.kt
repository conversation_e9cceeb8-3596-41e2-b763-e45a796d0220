package com.cadence.core.database.health

import com.cadence.core.database.CadenceDatabase
import com.cadence.core.database.dao.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据库健康检查器
 * 负责检查数据库完整性、性能和数据一致性
 */
@Singleton
class DatabaseHealthChecker @Inject constructor(
    private val database: CadenceDatabase,
    private val translationDao: TranslationDao,
    private val translationCacheDao: TranslationCacheDao,
    private val userPreferenceDao: UserPreferenceDao,
    private val languageRegionDao: LanguageRegionDao
) {
    
    /**
     * 执行完整的数据库健康检查
     */
    suspend fun performHealthCheck(): DatabaseHealthReport {
        return withContext(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            
            try {
                Timber.d("开始数据库健康检查")
                
                val checks = mutableListOf<HealthCheckResult>()
                
                // 1. 数据库连接检查
                checks.add(checkDatabaseConnection())
                
                // 2. 表结构完整性检查
                checks.add(checkTableIntegrity())
                
                // 3. 数据一致性检查
                checks.add(checkDataConsistency())
                
                // 4. 索引性能检查
                checks.add(checkIndexPerformance())
                
                // 5. 缓存健康检查
                checks.add(checkCacheHealth())
                
                // 6. 存储空间检查
                checks.add(checkStorageSpace())
                
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                val overallStatus = if (checks.any { it.status == HealthStatus.CRITICAL }) {
                    HealthStatus.CRITICAL
                } else if (checks.any { it.status == HealthStatus.WARNING }) {
                    HealthStatus.WARNING
                } else {
                    HealthStatus.HEALTHY
                }
                
                val report = DatabaseHealthReport(
                    overallStatus = overallStatus,
                    checkResults = checks,
                    checkDuration = duration,
                    timestamp = System.currentTimeMillis()
                )
                
                Timber.d("数据库健康检查完成: 状态=$overallStatus, 耗时=${duration}ms")
                
                return@withContext report
                
            } catch (e: Exception) {
                Timber.e(e, "数据库健康检查失败")
                return@withContext DatabaseHealthReport(
                    overallStatus = HealthStatus.CRITICAL,
                    checkResults = listOf(
                        HealthCheckResult(
                            checkName = "健康检查执行",
                            status = HealthStatus.CRITICAL,
                            message = "健康检查执行失败: ${e.message}",
                            details = e.stackTraceToString()
                        )
                    ),
                    checkDuration = System.currentTimeMillis() - startTime,
                    timestamp = System.currentTimeMillis()
                )
            }
        }
    }
    
    /**
     * 检查数据库连接
     */
    private suspend fun checkDatabaseConnection(): HealthCheckResult {
        return try {
            // 执行简单查询测试连接
            database.query("SELECT 1", null).use { cursor ->
                if (cursor.moveToFirst()) {
                    HealthCheckResult(
                        checkName = "数据库连接",
                        status = HealthStatus.HEALTHY,
                        message = "数据库连接正常"
                    )
                } else {
                    HealthCheckResult(
                        checkName = "数据库连接",
                        status = HealthStatus.CRITICAL,
                        message = "数据库查询返回空结果"
                    )
                }
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "数据库连接",
                status = HealthStatus.CRITICAL,
                message = "数据库连接失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
    
    /**
     * 检查表结构完整性
     */
    private suspend fun checkTableIntegrity(): HealthCheckResult {
        return try {
            val requiredTables = listOf(
                "translations", "translation_cache", "language_regions", 
                "user_preferences", "tags", "translation_tags",
                "words", "learning_progress", "study_sessions",
                "cultural_contexts", "usage_contexts", "regional_differences",
                "favorite_folders", "favorite_items"
            )
            
            val missingTables = mutableListOf<String>()
            
            for (tableName in requiredTables) {
                database.query(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
                    arrayOf(tableName)
                ).use { cursor ->
                    if (!cursor.moveToFirst()) {
                        missingTables.add(tableName)
                    }
                }
            }
            
            if (missingTables.isEmpty()) {
                HealthCheckResult(
                    checkName = "表结构完整性",
                    status = HealthStatus.HEALTHY,
                    message = "所有必需的表都存在"
                )
            } else {
                HealthCheckResult(
                    checkName = "表结构完整性",
                    status = HealthStatus.CRITICAL,
                    message = "缺少必需的表",
                    details = "缺少的表: ${missingTables.joinToString(", ")}"
                )
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "表结构完整性",
                status = HealthStatus.CRITICAL,
                message = "表结构检查失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
    
    /**
     * 检查数据一致性
     */
    private suspend fun checkDataConsistency(): HealthCheckResult {
        return try {
            val issues = mutableListOf<String>()
            
            // 检查语言区域数据
            val languageRegionCount = languageRegionDao.getActiveLanguageRegionCount()
            if (languageRegionCount == 0) {
                issues.add("没有可用的语言区域数据")
            }
            
            // 检查用户偏好数据
            val userPreferenceCount = userPreferenceDao.getPreferenceCount()
            if (userPreferenceCount == 0) {
                issues.add("没有用户偏好数据")
            }
            
            // 检查翻译缓存数据完整性
            val invalidCacheCount = translationCacheDao.getInvalidCacheCount()
            if (invalidCacheCount > 0) {
                issues.add("发现 $invalidCacheCount 条无效的缓存记录")
            }
            
            if (issues.isEmpty()) {
                HealthCheckResult(
                    checkName = "数据一致性",
                    status = HealthStatus.HEALTHY,
                    message = "数据一致性检查通过"
                )
            } else {
                HealthCheckResult(
                    checkName = "数据一致性",
                    status = HealthStatus.WARNING,
                    message = "发现数据一致性问题",
                    details = issues.joinToString("; ")
                )
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "数据一致性",
                status = HealthStatus.CRITICAL,
                message = "数据一致性检查失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
    
    /**
     * 检查索引性能
     */
    private suspend fun checkIndexPerformance(): HealthCheckResult {
        return try {
            // 执行一些常见查询并测量性能
            val startTime = System.currentTimeMillis()
            
            // 测试翻译查询性能
            translationDao.getRecentTranslations(10)
            
            // 测试缓存查询性能
            translationCacheDao.getCacheStatistics()
            
            val queryTime = System.currentTimeMillis() - startTime
            
            when {
                queryTime < 100 -> HealthCheckResult(
                    checkName = "索引性能",
                    status = HealthStatus.HEALTHY,
                    message = "查询性能良好 (${queryTime}ms)"
                )
                queryTime < 500 -> HealthCheckResult(
                    checkName = "索引性能",
                    status = HealthStatus.WARNING,
                    message = "查询性能一般 (${queryTime}ms)",
                    details = "建议检查索引配置"
                )
                else -> HealthCheckResult(
                    checkName = "索引性能",
                    status = HealthStatus.CRITICAL,
                    message = "查询性能较差 (${queryTime}ms)",
                    details = "需要优化索引或查询语句"
                )
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "索引性能",
                status = HealthStatus.CRITICAL,
                message = "索引性能检查失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
    
    /**
     * 检查缓存健康状态
     */
    private suspend fun checkCacheHealth(): HealthCheckResult {
        return try {
            val cacheCount = translationCacheDao.getCacheCount()
            val currentTime = System.currentTimeMillis()
            val expiredCount = translationCacheDao.getExpiredCacheCount(currentTime - 24 * 3600_000)
            
            val expiredPercentage = if (cacheCount > 0) (expiredCount.toFloat() / cacheCount) else 0f
            
            when {
                expiredPercentage < 0.1f -> HealthCheckResult(
                    checkName = "缓存健康",
                    status = HealthStatus.HEALTHY,
                    message = "缓存状态良好 (过期率: ${String.format("%.1f", expiredPercentage * 100)}%)"
                )
                expiredPercentage < 0.3f -> HealthCheckResult(
                    checkName = "缓存健康",
                    status = HealthStatus.WARNING,
                    message = "缓存过期率较高 (${String.format("%.1f", expiredPercentage * 100)}%)",
                    details = "建议清理过期缓存"
                )
                else -> HealthCheckResult(
                    checkName = "缓存健康",
                    status = HealthStatus.CRITICAL,
                    message = "缓存过期率过高 (${String.format("%.1f", expiredPercentage * 100)}%)",
                    details = "需要立即清理过期缓存"
                )
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "缓存健康",
                status = HealthStatus.CRITICAL,
                message = "缓存健康检查失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
    
    /**
     * 检查存储空间
     */
    private suspend fun checkStorageSpace(): HealthCheckResult {
        return try {
            // 获取数据库文件大小
            val dbPath = database.openHelper.readableDatabase.path
            val dbFile = java.io.File(dbPath)
            val dbSizeMB = dbFile.length() / (1024 * 1024)
            
            when {
                dbSizeMB < 100 -> HealthCheckResult(
                    checkName = "存储空间",
                    status = HealthStatus.HEALTHY,
                    message = "数据库大小正常 (${dbSizeMB}MB)"
                )
                dbSizeMB < 500 -> HealthCheckResult(
                    checkName = "存储空间",
                    status = HealthStatus.WARNING,
                    message = "数据库大小较大 (${dbSizeMB}MB)",
                    details = "建议定期清理数据"
                )
                else -> HealthCheckResult(
                    checkName = "存储空间",
                    status = HealthStatus.CRITICAL,
                    message = "数据库大小过大 (${dbSizeMB}MB)",
                    details = "需要清理历史数据或优化存储"
                )
            }
        } catch (e: Exception) {
            HealthCheckResult(
                checkName = "存储空间",
                status = HealthStatus.WARNING,
                message = "存储空间检查失败: ${e.message}",
                details = e.stackTraceToString()
            )
        }
    }
}

/**
 * 健康状态枚举
 */
enum class HealthStatus {
    HEALTHY,    // 健康
    WARNING,    // 警告
    CRITICAL    // 严重
}

/**
 * 健康检查结果
 */
data class HealthCheckResult(
    val checkName: String,
    val status: HealthStatus,
    val message: String,
    val details: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 数据库健康报告
 */
data class DatabaseHealthReport(
    val overallStatus: HealthStatus,
    val checkResults: List<HealthCheckResult>,
    val checkDuration: Long,
    val timestamp: Long
)
