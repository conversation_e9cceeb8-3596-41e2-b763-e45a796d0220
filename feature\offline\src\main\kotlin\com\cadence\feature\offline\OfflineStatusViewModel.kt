package com.cadence.feature.offline

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.core.logging.StructuredLogger
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 离线状态ViewModel
 */
@HiltViewModel
class OfflineStatusViewModel @Inject constructor(
    private val offlineModeManager: OfflineModeManager,
    private val structuredLogger: StructuredLogger
) : ViewModel() {
    
    // 网络状态
    val networkStatus: StateFlow<NetworkStatus> = offlineModeManager.networkStatus
    
    // 离线模式状态
    val offlineMode: StateFlow<OfflineModeState> = offlineModeManager.offlineMode
    
    // 功能可用性
    val featureAvailability: StateFlow<FeatureAvailability> = offlineModeManager.featureAvailability
    
    // 用户提示
    val userPrompts: StateFlow<List<OfflinePrompt>> = offlineModeManager.userPrompts
    
    // 是否显示离线指示器
    val shouldShowOfflineIndicator: StateFlow<Boolean> = networkStatus.map { status ->
        status != NetworkStatus.CONNECTED
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )
    
    // 是否有活跃的提示
    val hasActivePrompts: StateFlow<Boolean> = userPrompts.map { prompts ->
        prompts.isNotEmpty()
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = false
    )
    
    // 离线能力状态
    val offlineCapabilityStatus: StateFlow<OfflineCapabilityStatus> = 
        offlineMode.map { mode ->
            when {
                mode.isOffline && mode.hasOfflineCapability -> OfflineCapabilityStatus.FULLY_CAPABLE
                mode.isOffline && !mode.hasOfflineCapability -> OfflineCapabilityStatus.LIMITED
                !mode.isOffline -> OfflineCapabilityStatus.ONLINE
                else -> OfflineCapabilityStatus.UNKNOWN
            }
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = OfflineCapabilityStatus.UNKNOWN
        )
    
    // 功能可用性摘要
    val featureAvailabilitySummary: StateFlow<FeatureAvailabilitySummary> = 
        featureAvailability.map { availability ->
            FeatureAvailabilitySummary(
                totalFeatures = availability.totalFeatureCount,
                availableFeatures = availability.availableFeatureCount,
                availabilityPercentage = availability.availabilityPercentage,
                hasBasicTranslation = availability.hasBasicTranslationCapability,
                hasOfflineCapability = availability.hasOfflineCapability,
                limitedFeatures = availability.limitedFeatures
            )
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = FeatureAvailabilitySummary()
        )
    
    init {
        observeOfflineModeChanges()
    }
    
    /**
     * 监听离线模式变化
     */
    private fun observeOfflineModeChanges() {
        viewModelScope.launch {
            networkStatus.collect { status ->
                structuredLogger.logInfo(
                    message = "网络状态变化",
                    context = mapOf("network_status" to status.name)
                )
            }
        }
        
        viewModelScope.launch {
            offlineMode.collect { mode ->
                structuredLogger.logInfo(
                    message = "离线模式状态变化",
                    context = mapOf(
                        "is_offline" to mode.isOffline.toString(),
                        "has_offline_capability" to mode.hasOfflineCapability.toString(),
                        "language_pairs" to mode.availableLanguagePairs.toString()
                    )
                )
            }
        }
    }
    
    /**
     * 关闭提示
     */
    fun dismissPrompt(promptId: String) {
        offlineModeManager.dismissPrompt(promptId)
        
        structuredLogger.logInfo(
            message = "用户关闭提示",
            context = mapOf("prompt_id" to promptId)
        )
    }
    
    /**
     * 处理提示动作
     */
    fun handlePromptAction(promptId: String, action: PromptAction) {
        offlineModeManager.handlePromptAction(promptId, action)
        
        structuredLogger.logInfo(
            message = "用户处理提示动作",
            context = mapOf(
                "prompt_id" to promptId,
                "action" to action.name
            )
        )
    }
    
    /**
     * 检查功能是否可用
     */
    fun isFeatureAvailable(feature: OfflineFeature): Boolean {
        return offlineModeManager.isFeatureAvailable(feature)
    }
    
    /**
     * 获取功能限制说明
     */
    fun getFeatureLimitation(feature: OfflineFeature): String? {
        return offlineModeManager.getFeatureLimitation(feature)
    }
    
    /**
     * 获取离线模式建议
     */
    fun getOfflineModeSuggestions(): List<OfflineSuggestion> {
        return offlineModeManager.getOfflineModeSuggestions()
    }
    
    /**
     * 刷新网络状态
     */
    fun refreshNetworkStatus() {
        viewModelScope.launch {
            try {
                // 触发网络状态检查
                // offlineModeManager 会自动处理
                
                structuredLogger.logInfo(
                    message = "用户手动刷新网络状态"
                )
                
            } catch (e: Exception) {
                Timber.e(e, "刷新网络状态失败")
                structuredLogger.logError(
                    message = "刷新网络状态失败",
                    error = e
                )
            }
        }
    }
    
    /**
     * 获取网络状态描述
     */
    fun getNetworkStatusDescription(): String {
        return when (networkStatus.value) {
            NetworkStatus.CONNECTED -> "网络连接正常，所有功能可用"
            NetworkStatus.DISCONNECTED -> {
                if (offlineMode.value.hasOfflineCapability) {
                    "离线模式，离线翻译功能可用"
                } else {
                    "无网络连接，功能受限"
                }
            }
            NetworkStatus.LIMITED -> "网络连接不稳定，部分功能可能受影响"
            NetworkStatus.UNKNOWN -> "正在检测网络状态..."
        }
    }
    
    /**
     * 获取离线模式描述
     */
    fun getOfflineModeDescription(): String {
        val mode = offlineMode.value
        return when {
            !mode.isOffline -> "在线模式"
            mode.hasOfflineCapability -> "离线模式 - ${mode.availableLanguagePairs}个语言对可用"
            else -> "离线模式 - 功能受限"
        }
    }
    
    /**
     * 获取功能可用性描述
     */
    fun getFeatureAvailabilityDescription(): String {
        val availability = featureAvailability.value
        val percentage = availability.availabilityPercentage.toInt()
        
        return when {
            percentage >= 90 -> "所有功能正常 ($percentage%)"
            percentage >= 70 -> "大部分功能可用 ($percentage%)"
            percentage >= 50 -> "部分功能可用 ($percentage%)"
            else -> "功能严重受限 ($percentage%)"
        }
    }
    
    /**
     * 检查是否需要显示警告
     */
    fun shouldShowWarning(): Boolean {
        val mode = offlineMode.value
        val availability = featureAvailability.value
        
        return (mode.isOffline && !mode.hasOfflineCapability) ||
               !availability.hasBasicTranslationCapability ||
               availability.availabilityPercentage < 50f
    }
    
    /**
     * 获取警告消息
     */
    fun getWarningMessage(): String? {
        val mode = offlineMode.value
        val availability = featureAvailability.value
        
        return when {
            mode.isOffline && !mode.hasOfflineCapability -> 
                "无网络连接且未安装离线翻译模型，功能严重受限"
            !availability.hasBasicTranslationCapability -> 
                "翻译功能不可用，请检查网络连接或下载语言包"
            availability.availabilityPercentage < 50f -> 
                "多项功能不可用，建议检查网络连接"
            else -> null
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        // OfflineModeManager 的清理由依赖注入框架处理
    }
}

/**
 * 离线能力状态
 */
enum class OfflineCapabilityStatus {
    ONLINE,         // 在线状态
    FULLY_CAPABLE,  // 完全离线能力
    LIMITED,        // 受限离线能力
    UNKNOWN         // 未知状态
}

/**
 * 功能可用性摘要
 */
data class FeatureAvailabilitySummary(
    val totalFeatures: Int = 10,
    val availableFeatures: Int = 0,
    val availabilityPercentage: Float = 0f,
    val hasBasicTranslation: Boolean = false,
    val hasOfflineCapability: Boolean = false,
    val limitedFeatures: List<String> = emptyList()
) {
    val isHealthy: Boolean
        get() = availabilityPercentage >= 70f && hasBasicTranslation
    
    val statusLevel: StatusLevel
        get() = when {
            availabilityPercentage >= 90f -> StatusLevel.EXCELLENT
            availabilityPercentage >= 70f -> StatusLevel.GOOD
            availabilityPercentage >= 50f -> StatusLevel.FAIR
            else -> StatusLevel.POOR
        }
}

/**
 * 状态等级
 */
enum class StatusLevel {
    EXCELLENT,  // 优秀
    GOOD,       // 良好
    FAIR,       // 一般
    POOR        // 差
}
