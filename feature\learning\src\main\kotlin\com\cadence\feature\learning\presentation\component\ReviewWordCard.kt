package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.LearningProgress
import com.cadence.domain.model.learning.MasteryLevel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 复习单词卡片组件
 * 显示待复习单词的信息和操作
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReviewWordCard(
    progress: LearningProgress,
    onWordClick: () -> Unit,
    onStartReview: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onWordClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 顶部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 掌握程度标签
                MasteryLevelChip(masteryLevel = progress.masteryLevel)
                
                // 复习紧急程度
                ReviewUrgencyIndicator(progress = progress)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 单词信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "单词 ${progress.wordId.take(8)}...", // 简化显示
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    // 学习统计
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        StatChip(
                            icon = Icons.Default.TrendingUp,
                            label = "正确率",
                            value = "${(progress.accuracyRate * 100).toInt()}%"
                        )
                        
                        StatChip(
                            icon = Icons.Default.Repeat,
                            label = "练习",
                            value = "${progress.totalAttempts}次"
                        )
                    }
                }
                
                // 开始复习按钮
                FilledTonalButton(
                    onClick = onStartReview,
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("复习")
                }
            }
            
            // 复习时间信息
            progress.nextReviewAt?.let { nextReview ->
                Spacer(modifier = Modifier.height(12.dp))
                
                val isOverdue = nextReview < System.currentTimeMillis()
                val timeText = if (isOverdue) {
                    val overdueDays = (System.currentTimeMillis() - nextReview) / (24 * 60 * 60 * 1000L)
                    "已逾期 ${overdueDays}天"
                } else {
                    "计划复习时间: ${formatDateTime(nextReview)}"
                }
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (isOverdue) Icons.Default.Warning else Icons.Default.Schedule,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = if (isOverdue) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = timeText,
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isOverdue) {
                            MaterialTheme.colorScheme.error
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
        }
    }
}

/**
 * 掌握程度芯片
 */
@Composable
private fun MasteryLevelChip(
    masteryLevel: MasteryLevel,
    modifier: Modifier = Modifier
) {
    AssistChip(
        onClick = { },
        label = {
            Text(
                text = masteryLevel.displayName,
                style = MaterialTheme.typography.labelSmall
            )
        },
        leadingIcon = {
            Icon(
                imageVector = getMasteryIcon(masteryLevel),
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
        },
        modifier = modifier,
        colors = AssistChipDefaults.assistChipColors(
            containerColor = getMasteryColor(masteryLevel).copy(alpha = 0.2f),
            labelColor = getMasteryColor(masteryLevel),
            leadingIconContentColor = getMasteryColor(masteryLevel)
        )
    )
}

/**
 * 复习紧急程度指示器
 */
@Composable
private fun ReviewUrgencyIndicator(
    progress: LearningProgress,
    modifier: Modifier = Modifier
) {
    val urgency = calculateUrgency(progress)
    val (icon, color, text) = when (urgency) {
        ReviewUrgency.OVERDUE -> Triple(
            Icons.Default.Warning,
            MaterialTheme.colorScheme.error,
            "逾期"
        )
        ReviewUrgency.URGENT -> Triple(
            Icons.Default.PriorityHigh,
            MaterialTheme.colorScheme.primary,
            "紧急"
        )
        ReviewUrgency.NORMAL -> Triple(
            Icons.Default.Schedule,
            MaterialTheme.colorScheme.onSurfaceVariant,
            "正常"
        )
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            modifier = Modifier.size(16.dp),
            tint = color
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color
        )
    }
}

/**
 * 统计芯片
 */
@Composable
private fun StatChip(
    icon: ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(14.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.width(4.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 获取掌握程度对应的图标
 */
private fun getMasteryIcon(masteryLevel: MasteryLevel): ImageVector {
    return when (masteryLevel) {
        MasteryLevel.NEW -> Icons.Default.FiberNew
        MasteryLevel.LEARNING -> Icons.Default.School
        MasteryLevel.FAMILIAR -> Icons.Default.ThumbUp
        MasteryLevel.KNOWN -> Icons.Default.CheckCircle
        MasteryLevel.MASTERED -> Icons.Default.Star
    }
}

/**
 * 获取掌握程度对应的颜色
 */
@Composable
private fun getMasteryColor(masteryLevel: MasteryLevel): androidx.compose.ui.graphics.Color {
    return when (masteryLevel) {
        MasteryLevel.NEW -> MaterialTheme.colorScheme.outline
        MasteryLevel.LEARNING -> MaterialTheme.colorScheme.primary
        MasteryLevel.FAMILIAR -> MaterialTheme.colorScheme.secondary
        MasteryLevel.KNOWN -> MaterialTheme.colorScheme.tertiary
        MasteryLevel.MASTERED -> MaterialTheme.colorScheme.primary
    }
}

/**
 * 计算复习紧急程度
 */
private fun calculateUrgency(progress: LearningProgress): ReviewUrgency {
    val nextReview = progress.nextReviewAt ?: return ReviewUrgency.NORMAL
    val currentTime = System.currentTimeMillis()
    
    return when {
        nextReview < currentTime -> ReviewUrgency.OVERDUE
        nextReview - currentTime < 2 * 60 * 60 * 1000L -> ReviewUrgency.URGENT // 2小时内
        else -> ReviewUrgency.NORMAL
    }
}

/**
 * 复习紧急程度枚举
 */
private enum class ReviewUrgency {
    OVERDUE,  // 逾期
    URGENT,   // 紧急
    NORMAL    // 正常
}

/**
 * 格式化日期时间
 */
private fun formatDateTime(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM月dd日 HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}