package com.cadence.core.network.monitor

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络状态监控器
 * 提供实时的网络连接状态和网络类型信息
 */
@Singleton
class NetworkMonitor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    /**
     * 监控网络连接状态
     * @return 网络状态流
     */
    fun networkStatus(): Flow<NetworkStatus> = callbackFlow {
        val callback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                val status = getCurrentNetworkStatus()
                Timber.d("网络连接可用: $status")
                trySend(status)
            }
            
            override fun onLost(network: Network) {
                super.onLost(network)
                val status = NetworkStatus.Disconnected
                Timber.d("网络连接丢失")
                trySend(status)
            }
            
            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities)
                val status = getCurrentNetworkStatus()
                Timber.d("网络能力变化: $status")
                trySend(status)
            }
        }
        
        // 注册网络回调
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.registerDefaultNetworkCallback(callback)
        } else {
            val request = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            connectivityManager.registerNetworkCallback(request, callback)
        }
        
        // 发送当前网络状态
        trySend(getCurrentNetworkStatus())
        
        awaitClose {
            connectivityManager.unregisterNetworkCallback(callback)
        }
    }.distinctUntilChanged()
    
    /**
     * 获取当前网络状态
     */
    fun getCurrentNetworkStatus(): NetworkStatus {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        return if (networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)) {
            when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    NetworkStatus.Connected(NetworkType.WIFI)
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    NetworkStatus.Connected(NetworkType.CELLULAR)
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                    NetworkStatus.Connected(NetworkType.ETHERNET)
                }
                else -> {
                    NetworkStatus.Connected(NetworkType.OTHER)
                }
            }
        } else {
            NetworkStatus.Disconnected
        }
    }
    
    /**
     * 检查是否有网络连接
     */
    fun isConnected(): Boolean {
        return getCurrentNetworkStatus() is NetworkStatus.Connected
    }
    
    /**
     * 检查是否为WiFi连接
     */
    fun isWifiConnected(): Boolean {
        val status = getCurrentNetworkStatus()
        return status is NetworkStatus.Connected && status.type == NetworkType.WIFI
    }
    
    /**
     * 检查是否为移动网络连接
     */
    fun isCellularConnected(): Boolean {
        val status = getCurrentNetworkStatus()
        return status is NetworkStatus.Connected && status.type == NetworkType.CELLULAR
    }
    
    /**
     * 获取网络连接质量
     */
    fun getNetworkQuality(): NetworkQuality {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        return if (networkCapabilities != null) {
            when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    // WiFi通常质量较好
                    if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                        NetworkQuality.EXCELLENT
                    } else {
                        NetworkQuality.GOOD
                    }
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    // 移动网络质量取决于信号强度
                    if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                        NetworkQuality.GOOD
                    } else {
                        NetworkQuality.FAIR
                    }
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                    NetworkQuality.EXCELLENT
                }
                else -> NetworkQuality.POOR
            }
        } else {
            NetworkQuality.NO_CONNECTION
        }
    }
}

/**
 * 网络状态密封类
 */
sealed class NetworkStatus {
    /**
     * 已连接状态
     */
    data class Connected(val type: NetworkType) : NetworkStatus()
    
    /**
     * 未连接状态
     */
    object Disconnected : NetworkStatus()
}

/**
 * 网络类型枚举
 */
enum class NetworkType {
    WIFI,       // WiFi网络
    CELLULAR,   // 移动网络
    ETHERNET,   // 以太网
    OTHER       // 其他网络类型
}

/**
 * 网络质量枚举
 */
enum class NetworkQuality {
    NO_CONNECTION,  // 无连接
    POOR,          // 差
    FAIR,          // 一般
    GOOD,          // 良好
    EXCELLENT      // 优秀
}

/**
 * 网络状态扩展函数
 */
fun NetworkStatus.isConnected(): Boolean = this is NetworkStatus.Connected

fun NetworkStatus.isWifi(): Boolean = this is NetworkStatus.Connected && this.type == NetworkType.WIFI

fun NetworkStatus.isCellular(): Boolean = this is NetworkStatus.Connected && this.type == NetworkType.CELLULAR

fun NetworkStatus.getDisplayName(): String = when (this) {
    is NetworkStatus.Connected -> when (type) {
        NetworkType.WIFI -> "WiFi"
        NetworkType.CELLULAR -> "移动网络"
        NetworkType.ETHERNET -> "以太网"
        NetworkType.OTHER -> "其他网络"
    }
    NetworkStatus.Disconnected -> "无网络连接"
}

/**
 * 网络质量扩展函数
 */
fun NetworkQuality.getDisplayName(): String = when (this) {
    NetworkQuality.NO_CONNECTION -> "无连接"
    NetworkQuality.POOR -> "网络较差"
    NetworkQuality.FAIR -> "网络一般"
    NetworkQuality.GOOD -> "网络良好"
    NetworkQuality.EXCELLENT -> "网络优秀"
}

fun NetworkQuality.shouldUseCache(): Boolean = when (this) {
    NetworkQuality.NO_CONNECTION, NetworkQuality.POOR -> true
    else -> false
}

fun NetworkQuality.shouldLimitRequests(): Boolean = when (this) {
    NetworkQuality.NO_CONNECTION, NetworkQuality.POOR, NetworkQuality.FAIR -> true
    else -> false
}
