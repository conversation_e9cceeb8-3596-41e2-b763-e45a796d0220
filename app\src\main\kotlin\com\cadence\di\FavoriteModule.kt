package com.cadence.di

import com.cadence.core.database.CadenceDatabase
import com.cadence.core.database.dao.FavoriteDao
import com.cadence.data.repository.FavoriteRepositoryImpl
import com.cadence.domain.repository.FavoriteRepository
import com.cadence.domain.usecase.ManageFavoritesUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 收藏功能依赖注入模块
 * 提供收藏相关的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object FavoriteModule {
    
    /**
     * 提供收藏功能DAO
     */
    @Provides
    @Singleton
    fun provideFavoriteDao(database: CadenceDatabase): FavoriteDao {
        return database.favoriteDao()
    }
    
    /**
     * 提供收藏功能数据仓库
     */
    @Provides
    @Singleton
    fun provideFavoriteRepository(
        favoriteDao: FavoriteDao
    ): FavoriteRepository {
        return FavoriteRepositoryImpl(favoriteDao)
    }
    
    /**
     * 提供收藏功能用例
     */
    @Provides
    @Singleton
    fun provideManageFavoritesUseCase(
        favoriteRepository: FavoriteRepository,
        translationRepository: com.cadence.domain.repository.TranslationRepository
    ): ManageFavoritesUseCase {
        return ManageFavoritesUseCase(favoriteRepository, translationRepository)
    }
}
