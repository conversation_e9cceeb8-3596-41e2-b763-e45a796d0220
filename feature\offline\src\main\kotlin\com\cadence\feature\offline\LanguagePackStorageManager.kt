package com.cadence.feature.offline

import android.content.Context
import android.os.StatFs
import com.cadence.core.logging.StructuredLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语言包存储管理器
 * 负责语言包的存储空间管理、清理策略和存储优化
 */
@Singleton
class LanguagePackStorageManager @Inject constructor(
    private val context: Context,
    private val modelManager: TranslationModelManager,
    private val structuredLogger: StructuredLogger
) {
    
    companion object {
        private const val STORAGE_CONFIG_FILE = "storage_config.json"
        private const val MIN_FREE_SPACE_MB = 100L // 最小保留空间100MB
        private const val WARNING_THRESHOLD_PERCENT = 85 // 存储空间警告阈值85%
        private const val CRITICAL_THRESHOLD_PERCENT = 95 // 存储空间危险阈值95%
        private const val AUTO_CLEANUP_ENABLED = true
        private const val MAX_CACHE_AGE_DAYS = 30L // 缓存最大保留30天
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 存储状态管理
    private val _storageState = MutableStateFlow(StorageState())
    val storageState: StateFlow<StorageState> = _storageState.asStateFlow()
    
    // 存储配置
    private var storageConfig = StorageConfig()
    
    // JSON序列化器
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    init {
        initializeStorageManager()
    }
    
    /**
     * 初始化存储管理器
     */
    private fun initializeStorageManager() {
        scope.launch {
            try {
                // 加载存储配置
                loadStorageConfig()
                
                // 更新存储状态
                updateStorageState()
                
                // 启动定期监控
                startStorageMonitoring()
                
                structuredLogger.logInfo(
                    message = "存储管理器初始化完成",
                    context = mapOf(
                        "auto_cleanup" to storageConfig.autoCleanupEnabled.toString(),
                        "max_cache_age_days" to storageConfig.maxCacheAgeDays.toString(),
                        "min_free_space_mb" to storageConfig.minFreeSpaceMB.toString()
                    )
                )
                
                Timber.d("语言包存储管理器初始化完成")
                
            } catch (e: Exception) {
                Timber.e(e, "存储管理器初始化失败")
                structuredLogger.logError(
                    message = "存储管理器初始化失败",
                    error = e
                )
            }
        }
    }
    
    /**
     * 检查存储空间是否足够
     */
    fun checkStorageSpace(requiredSizeMB: Long): StorageCheckResult {
        val currentState = _storageState.value
        val availableSpaceMB = currentState.availableSpaceMB
        val freeSpaceAfterDownload = availableSpaceMB - requiredSizeMB
        
        return when {
            freeSpaceAfterDownload < storageConfig.minFreeSpaceMB -> {
                StorageCheckResult(
                    hasEnoughSpace = false,
                    availableSpaceMB = availableSpaceMB,
                    requiredSpaceMB = requiredSizeMB,
                    freeSpaceAfterMB = freeSpaceAfterDownload,
                    recommendation = StorageRecommendation.CLEANUP_REQUIRED,
                    message = "存储空间不足，需要清理${requiredSizeMB - availableSpaceMB + storageConfig.minFreeSpaceMB}MB空间"
                )
            }
            freeSpaceAfterDownload < (currentState.totalSpaceMB * (100 - CRITICAL_THRESHOLD_PERCENT) / 100) -> {
                StorageCheckResult(
                    hasEnoughSpace = true,
                    availableSpaceMB = availableSpaceMB,
                    requiredSpaceMB = requiredSizeMB,
                    freeSpaceAfterMB = freeSpaceAfterDownload,
                    recommendation = StorageRecommendation.CLEANUP_SUGGESTED,
                    message = "存储空间紧张，建议清理部分语言包"
                )
            }
            freeSpaceAfterDownload < (currentState.totalSpaceMB * (100 - WARNING_THRESHOLD_PERCENT) / 100) -> {
                StorageCheckResult(
                    hasEnoughSpace = true,
                    availableSpaceMB = availableSpaceMB,
                    requiredSpaceMB = requiredSizeMB,
                    freeSpaceAfterMB = freeSpaceAfterDownload,
                    recommendation = StorageRecommendation.MONITOR,
                    message = "存储空间充足，但建议关注空间使用情况"
                )
            }
            else -> {
                StorageCheckResult(
                    hasEnoughSpace = true,
                    availableSpaceMB = availableSpaceMB,
                    requiredSpaceMB = requiredSizeMB,
                    freeSpaceAfterMB = freeSpaceAfterDownload,
                    recommendation = StorageRecommendation.SUFFICIENT,
                    message = "存储空间充足"
                )
            }
        }
    }
    
    /**
     * 执行存储清理
     */
    suspend fun performCleanup(strategy: CleanupStrategy = CleanupStrategy.SMART): CleanupResult = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            var totalCleanedMB = 0L
            var cleanedItems = 0
            
            structuredLogger.logInfo(
                message = "开始存储清理",
                context = mapOf(
                    "strategy" to strategy.name,
                    "current_usage_mb" to _storageState.value.usedSpaceMB.toString()
                )
            )
            
            when (strategy) {
                CleanupStrategy.CACHE_ONLY -> {
                    val cacheResult = cleanupCache()
                    totalCleanedMB += cacheResult.cleanedSizeMB
                    cleanedItems += cacheResult.cleanedCount
                }
                CleanupStrategy.OLD_MODELS -> {
                    val modelsResult = cleanupOldModels()
                    totalCleanedMB += modelsResult.cleanedSizeMB
                    cleanedItems += modelsResult.cleanedCount
                }
                CleanupStrategy.SMART -> {
                    // 智能清理：先清理缓存，再清理旧模型
                    val cacheResult = cleanupCache()
                    totalCleanedMB += cacheResult.cleanedSizeMB
                    cleanedItems += cacheResult.cleanedCount
                    
                    // 如果空间仍然不足，清理旧模型
                    updateStorageState()
                    if (_storageState.value.usagePercentage > WARNING_THRESHOLD_PERCENT) {
                        val modelsResult = cleanupOldModels()
                        totalCleanedMB += modelsResult.cleanedSizeMB
                        cleanedItems += modelsResult.cleanedCount
                    }
                }
                CleanupStrategy.AGGRESSIVE -> {
                    // 激进清理：清理所有可清理内容
                    val cacheResult = cleanupCache()
                    val modelsResult = cleanupOldModels()
                    val tempResult = cleanupTempFiles()
                    
                    totalCleanedMB += cacheResult.cleanedSizeMB + modelsResult.cleanedSizeMB + tempResult.cleanedSizeMB
                    cleanedItems += cacheResult.cleanedCount + modelsResult.cleanedCount + tempResult.cleanedCount
                }
            }
            
            // 更新存储状态
            updateStorageState()
            
            val duration = System.currentTimeMillis() - startTime
            
            structuredLogger.logInfo(
                message = "存储清理完成",
                context = mapOf(
                    "strategy" to strategy.name,
                    "cleaned_mb" to totalCleanedMB.toString(),
                    "cleaned_items" to cleanedItems.toString(),
                    "duration_ms" to duration.toString(),
                    "new_usage_mb" to _storageState.value.usedSpaceMB.toString()
                )
            )
            
            CleanupResult(
                success = true,
                cleanedSizeMB = totalCleanedMB,
                cleanedCount = cleanedItems,
                duration = duration,
                strategy = strategy
            )
            
        } catch (e: Exception) {
            Timber.e(e, "存储清理失败")
            structuredLogger.logError(
                message = "存储清理失败",
                error = e,
                context = mapOf("strategy" to strategy.name)
            )
            
            CleanupResult(
                success = false,
                error = e.message ?: "清理失败"
            )
        }
    }
    
    /**
     * 获取存储使用详情
     */
    fun getStorageDetails(): StorageDetails {
        val currentState = _storageState.value
        val modelUsage = modelManager.getStorageUsage()
        
        return StorageDetails(
            totalSpaceMB = currentState.totalSpaceMB,
            usedSpaceMB = currentState.usedSpaceMB,
            availableSpaceMB = currentState.availableSpaceMB,
            usagePercentage = currentState.usagePercentage,
            modelCount = modelUsage.modelCount,
            modelsSizeMB = modelUsage.totalSize / (1024 * 1024),
            cacheSize = getCacheSize(),
            tempFilesSize = getTempFilesSize(),
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * 设置存储配置
     */
    suspend fun updateStorageConfig(newConfig: StorageConfig) = withContext(Dispatchers.IO) {
        try {
            storageConfig = newConfig
            saveStorageConfig()
            
            structuredLogger.logInfo(
                message = "存储配置已更新",
                context = mapOf(
                    "auto_cleanup" to newConfig.autoCleanupEnabled.toString(),
                    "max_cache_age_days" to newConfig.maxCacheAgeDays.toString(),
                    "min_free_space_mb" to newConfig.minFreeSpaceMB.toString()
                )
            )
            
            Timber.d("存储配置已更新")
            
        } catch (e: Exception) {
            Timber.e(e, "更新存储配置失败")
            structuredLogger.logError(
                message = "更新存储配置失败",
                error = e
            )
        }
    }
    
    /**
     * 获取清理建议
     */
    fun getCleanupSuggestions(): List<CleanupSuggestion> {
        val suggestions = mutableListOf<CleanupSuggestion>()
        val currentState = _storageState.value
        
        // 检查缓存大小
        val cacheSize = getCacheSize()
        if (cacheSize > 50 * 1024 * 1024) { // 50MB
            suggestions.add(
                CleanupSuggestion(
                    type = CleanupType.CACHE,
                    description = "清理缓存文件可释放${cacheSize / (1024 * 1024)}MB空间",
                    estimatedSavingMB = cacheSize / (1024 * 1024),
                    priority = CleanupPriority.LOW
                )
            )
        }
        
        // 检查临时文件
        val tempSize = getTempFilesSize()
        if (tempSize > 10 * 1024 * 1024) { // 10MB
            suggestions.add(
                CleanupSuggestion(
                    type = CleanupType.TEMP_FILES,
                    description = "清理临时文件可释放${tempSize / (1024 * 1024)}MB空间",
                    estimatedSavingMB = tempSize / (1024 * 1024),
                    priority = CleanupPriority.MEDIUM
                )
            )
        }
        
        // 检查存储空间使用率
        if (currentState.usagePercentage > CRITICAL_THRESHOLD_PERCENT) {
            suggestions.add(
                CleanupSuggestion(
                    type = CleanupType.OLD_MODELS,
                    description = "存储空间严重不足，建议删除不常用的语言包",
                    estimatedSavingMB = 0L, // 需要用户选择
                    priority = CleanupPriority.HIGH
                )
            )
        } else if (currentState.usagePercentage > WARNING_THRESHOLD_PERCENT) {
            suggestions.add(
                CleanupSuggestion(
                    type = CleanupType.OLD_MODELS,
                    description = "存储空间紧张，可考虑删除部分语言包",
                    estimatedSavingMB = 0L,
                    priority = CleanupPriority.MEDIUM
                )
            )
        }
        
        return suggestions.sortedByDescending { it.priority.ordinal }
    }

    /**
     * 清理缓存文件
     */
    private suspend fun cleanupCache(): CleanupItemResult = withContext(Dispatchers.IO) {
        try {
            val cacheDir = context.cacheDir
            var cleanedSize = 0L
            var cleanedCount = 0

            cacheDir.walkTopDown().forEach { file ->
                if (file.isFile && shouldCleanupCacheFile(file)) {
                    cleanedSize += file.length()
                    if (file.delete()) {
                        cleanedCount++
                    }
                }
            }

            CleanupItemResult(
                cleanedSizeMB = cleanedSize / (1024 * 1024),
                cleanedCount = cleanedCount
            )
        } catch (e: Exception) {
            Timber.e(e, "清理缓存失败")
            CleanupItemResult()
        }
    }

    /**
     * 清理旧模型
     */
    private suspend fun cleanupOldModels(): CleanupItemResult = withContext(Dispatchers.IO) {
        try {
            // TODO: 实现基于使用频率和时间的模型清理逻辑
            // 当前简单实现：不自动删除模型，需要用户手动选择
            CleanupItemResult()
        } catch (e: Exception) {
            Timber.e(e, "清理旧模型失败")
            CleanupItemResult()
        }
    }

    /**
     * 清理临时文件
     */
    private suspend fun cleanupTempFiles(): CleanupItemResult = withContext(Dispatchers.IO) {
        try {
            val tempDir = File(context.cacheDir, "temp")
            var cleanedSize = 0L
            var cleanedCount = 0

            if (tempDir.exists()) {
                tempDir.walkTopDown().forEach { file ->
                    if (file.isFile) {
                        cleanedSize += file.length()
                        if (file.delete()) {
                            cleanedCount++
                        }
                    }
                }
            }

            CleanupItemResult(
                cleanedSizeMB = cleanedSize / (1024 * 1024),
                cleanedCount = cleanedCount
            )
        } catch (e: Exception) {
            Timber.e(e, "清理临时文件失败")
            CleanupItemResult()
        }
    }

    /**
     * 判断是否应该清理缓存文件
     */
    private fun shouldCleanupCacheFile(file: File): Boolean {
        val maxAge = storageConfig.maxCacheAgeDays * 24 * 60 * 60 * 1000L
        val fileAge = System.currentTimeMillis() - file.lastModified()
        return fileAge > maxAge
    }

    /**
     * 获取缓存大小
     */
    private fun getCacheSize(): Long {
        return try {
            context.cacheDir.walkTopDown()
                .filter { it.isFile }
                .sumOf { it.length() }
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 获取临时文件大小
     */
    private fun getTempFilesSize(): Long {
        return try {
            val tempDir = File(context.cacheDir, "temp")
            if (tempDir.exists()) {
                tempDir.walkTopDown()
                    .filter { it.isFile }
                    .sumOf { it.length() }
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }

    /**
     * 更新存储状态
     */
    private suspend fun updateStorageState() = withContext(Dispatchers.IO) {
        try {
            val stat = StatFs(context.filesDir.absolutePath)
            val blockSize = stat.blockSizeLong
            val totalBlocks = stat.blockCountLong
            val availableBlocks = stat.availableBlocksLong

            val totalSpaceMB = (totalBlocks * blockSize) / (1024 * 1024)
            val availableSpaceMB = (availableBlocks * blockSize) / (1024 * 1024)
            val usedSpaceMB = totalSpaceMB - availableSpaceMB
            val usagePercentage = ((usedSpaceMB.toFloat() / totalSpaceMB.toFloat()) * 100).toInt()

            _storageState.value = StorageState(
                totalSpaceMB = totalSpaceMB,
                usedSpaceMB = usedSpaceMB,
                availableSpaceMB = availableSpaceMB,
                usagePercentage = usagePercentage,
                lastUpdated = System.currentTimeMillis()
            )

        } catch (e: Exception) {
            Timber.e(e, "更新存储状态失败")
        }
    }

    /**
     * 启动存储监控
     */
    private fun startStorageMonitoring() {
        scope.launch {
            while (isActive) {
                try {
                    updateStorageState()

                    // 检查是否需要自动清理
                    if (storageConfig.autoCleanupEnabled) {
                        val currentState = _storageState.value
                        if (currentState.usagePercentage > CRITICAL_THRESHOLD_PERCENT) {
                            Timber.i("存储空间不足，执行自动清理")
                            performCleanup(CleanupStrategy.SMART)
                        }
                    }

                    delay(60000) // 每分钟检查一次
                } catch (e: Exception) {
                    Timber.e(e, "存储监控异常")
                    delay(300000) // 出错时等待5分钟再重试
                }
            }
        }
    }

    /**
     * 加载存储配置
     */
    private suspend fun loadStorageConfig() = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, STORAGE_CONFIG_FILE)
            if (configFile.exists()) {
                val configJson = configFile.readText()
                storageConfig = json.decodeFromString<StorageConfig>(configJson)
            }
        } catch (e: Exception) {
            Timber.w(e, "加载存储配置失败，使用默认配置")
            storageConfig = StorageConfig()
        }
    }

    /**
     * 保存存储配置
     */
    private suspend fun saveStorageConfig() = withContext(Dispatchers.IO) {
        try {
            val configFile = File(context.filesDir, STORAGE_CONFIG_FILE)
            val configJson = json.encodeToString(StorageConfig.serializer(), storageConfig)
            configFile.writeText(configJson)
        } catch (e: Exception) {
            Timber.e(e, "保存存储配置失败")
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        Timber.d("语言包存储管理器已清理")
    }
}

// 数据类定义
data class StorageState(
    val totalSpaceMB: Long = 0L,
    val usedSpaceMB: Long = 0L,
    val availableSpaceMB: Long = 0L,
    val usagePercentage: Int = 0,
    val lastUpdated: Long = 0L
)

@Serializable
data class StorageConfig(
    val autoCleanupEnabled: Boolean = AUTO_CLEANUP_ENABLED,
    val minFreeSpaceMB: Long = MIN_FREE_SPACE_MB,
    val maxCacheAgeDays: Long = MAX_CACHE_AGE_DAYS,
    val warningThresholdPercent: Int = WARNING_THRESHOLD_PERCENT,
    val criticalThresholdPercent: Int = CRITICAL_THRESHOLD_PERCENT
)

data class StorageCheckResult(
    val hasEnoughSpace: Boolean,
    val availableSpaceMB: Long,
    val requiredSpaceMB: Long,
    val freeSpaceAfterMB: Long,
    val recommendation: StorageRecommendation,
    val message: String
)

enum class StorageRecommendation {
    SUFFICIENT,
    MONITOR,
    CLEANUP_SUGGESTED,
    CLEANUP_REQUIRED
}

data class CleanupResult(
    val success: Boolean,
    val cleanedSizeMB: Long = 0L,
    val cleanedCount: Int = 0,
    val duration: Long = 0L,
    val strategy: CleanupStrategy = CleanupStrategy.SMART,
    val error: String? = null
)

data class CleanupItemResult(
    val cleanedSizeMB: Long = 0L,
    val cleanedCount: Int = 0
)

enum class CleanupStrategy {
    CACHE_ONLY,
    OLD_MODELS,
    SMART,
    AGGRESSIVE
}

data class StorageDetails(
    val totalSpaceMB: Long,
    val usedSpaceMB: Long,
    val availableSpaceMB: Long,
    val usagePercentage: Int,
    val modelCount: Int,
    val modelsSizeMB: Long,
    val cacheSize: Long,
    val tempFilesSize: Long,
    val lastUpdated: Long
)

data class CleanupSuggestion(
    val type: CleanupType,
    val description: String,
    val estimatedSavingMB: Long,
    val priority: CleanupPriority
)

enum class CleanupType {
    CACHE,
    TEMP_FILES,
    OLD_MODELS,
    UNUSED_MODELS
}

enum class CleanupPriority {
    LOW,
    MEDIUM,
    HIGH
}
