package com.cadence.feature.translation.presentation.component

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.ui.graphics.Color
import com.cadence.core.speech.TextToSpeechService
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.Translation

/**
 * 翻译结果卡片组件
 * 显示翻译结果、文化背景解释和相关操作
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TranslationResultCard(
    translatedText: String,
    isLoading: Boolean,
    isFromCache: Boolean,
    processingTime: Long,
    culturalContext: String?,
    currentTranslation: Translation?,
    onCopyTranslation: () -> Unit,
    onShareTranslation: () -> Unit,
    onSpeakTranslation: () -> Unit,
    // 收藏功能相关参数
    onToggleFavorite: () -> Unit = {},
    isFavorite: Boolean = false,
    // 语音播放相关参数
    isPlayingTarget: Boolean = false,
    ttsResult: TextToSpeechService.TtsResult? = null,
    isTtsAvailable: Boolean = false,
    onPlayTargetText: () -> Unit = {},
    onStopSpeechPlayback: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    AnimatedVisibility(
        visible = translatedText.isNotEmpty() || isLoading,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut(),
        modifier = modifier
    ) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 标题栏
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "翻译结果",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    
                    // 状态指示器
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        if (isFromCache) {
                            Icon(
                                imageVector = Icons.Default.Cached,
                                contentDescription = "来自缓存",
                                modifier = Modifier.size(16.dp),
                                tint = MaterialTheme.colorScheme.secondary
                            )
                            Text(
                                text = "缓存",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.secondary
                            )
                        }
                        
                        if (processingTime > 0) {
                            Text(
                                text = "${processingTime}ms",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.outline
                            )
                        }
                    }
                }
                
                // 翻译结果内容
                if (isLoading) {
                    TranslationLoadingContent()
                } else {
                    TranslationContent(
                        translatedText = translatedText,
                        culturalContext = culturalContext,
                        currentTranslation = currentTranslation,
                        onCopyTranslation = onCopyTranslation,
                        onShareTranslation = onShareTranslation,
                        onSpeakTranslation = onSpeakTranslation,
                        onToggleFavorite = onToggleFavorite,
                        isFavorite = isFavorite,
                        // 语音播放相关参数
                        isPlayingTarget = isPlayingTarget,
                        ttsResult = ttsResult,
                        isTtsAvailable = isTtsAvailable,
                        onPlayTargetText = onPlayTargetText,
                        onStopSpeechPlayback = onStopSpeechPlayback
                    )
                }
            }
        }
    }
}

/**
 * 翻译加载中内容
 */
@Composable
private fun TranslationLoadingContent() {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(32.dp),
            strokeWidth = 3.dp
        )
        
        Text(
            text = "正在翻译中...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 翻译内容显示
 */
@Composable
private fun TranslationContent(
    translatedText: String,
    culturalContext: String?,
    currentTranslation: Translation?,
    onCopyTranslation: () -> Unit,
    onShareTranslation: () -> Unit,
    onSpeakTranslation: () -> Unit,
    onToggleFavorite: () -> Unit,
    isFavorite: Boolean,
    // 语音播放相关参数
    isPlayingTarget: Boolean,
    ttsResult: TextToSpeechService.TtsResult?,
    isTtsAvailable: Boolean,
    onPlayTargetText: () -> Unit,
    onStopSpeechPlayback: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 翻译文本
        SelectionContainer {
            Text(
                text = translatedText,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 60.dp)
                    .verticalScroll(rememberScrollState())
            )
        }
        
        // 文化背景解释
        if (!culturalContext.isNullOrBlank()) {
            CulturalContextCard(culturalContext = culturalContext)
        }
        
        // 操作按钮
        TranslationActionButtons(
            translatedText = translatedText,
            currentTranslation = currentTranslation,
            onCopyTranslation = onCopyTranslation,
            onShareTranslation = onShareTranslation,
            onSpeakTranslation = onSpeakTranslation,
            onToggleFavorite = onToggleFavorite,
            isFavorite = isFavorite,
            // 语音播放相关参数
            isPlayingTarget = isPlayingTarget,
            ttsResult = ttsResult,
            isTtsAvailable = isTtsAvailable,
            onPlayTargetText = onPlayTargetText,
            onStopSpeechPlayback = onStopSpeechPlayback
        )
    }
}

/**
 * 文化背景解释卡片
 */
@Composable
private fun CulturalContextCard(
    culturalContext: String,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                    Text(
                        text = "文化背景",
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
                
                IconButton(
                    onClick = { expanded = !expanded },
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (expanded) "收起" else "展开",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            AnimatedVisibility(
                visible = expanded,
                enter = expandVertically() + fadeIn(),
                exit = shrinkVertically() + fadeOut()
            ) {
                Text(
                    text = culturalContext,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * 翻译操作按钮
 */
@Composable
private fun TranslationActionButtons(
    translatedText: String,
    currentTranslation: Translation?,
    onCopyTranslation: () -> Unit,
    onShareTranslation: () -> Unit,
    onSpeakTranslation: () -> Unit,
    onToggleFavorite: () -> Unit,
    isFavorite: Boolean,
    // 语音播放相关参数
    isPlayingTarget: Boolean,
    ttsResult: TextToSpeechService.TtsResult?,
    isTtsAvailable: Boolean,
    onPlayTargetText: () -> Unit,
    onStopSpeechPlayback: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 第一行：复制、分享、语音播放
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 复制按钮
            OutlinedButton(
                onClick = onCopyTranslation,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.ContentCopy,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("复制")
            }

            // 分享按钮
            OutlinedButton(
                onClick = onShareTranslation,
                modifier = Modifier.weight(1f)
            ) {
                Icon(
                    imageVector = Icons.Default.Share,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("分享")
            }

            // 语音播放按钮
            SpeechPlayButton(
                text = translatedText,
                language = currentTranslation?.targetLanguage?.code ?: "zh-CN",
                isPlaying = isPlayingTarget,
                isAvailable = isTtsAvailable,
                onPlay = { text, language -> onPlayTargetText() },
                onStop = onStopSpeechPlayback,
                ttsResult = ttsResult,
                size = ButtonSize.Medium,
                modifier = Modifier.weight(1f)
            )
        }

        // 第二行：收藏按钮
        if (currentTranslation != null) {
            FavoriteButton(
                isFavorite = isFavorite,
                onToggleFavorite = onToggleFavorite,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 收藏按钮组件
 */
@Composable
private fun FavoriteButton(
    isFavorite: Boolean,
    onToggleFavorite: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedButton(
        onClick = onToggleFavorite,
        modifier = modifier,
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = if (isFavorite) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                Color.Transparent
            },
            contentColor = if (isFavorite) {
                MaterialTheme.colorScheme.onPrimaryContainer
            } else {
                MaterialTheme.colorScheme.primary
            }
        ),
        border = BorderStroke(
            width = 1.dp,
            color = if (isFavorite) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.outline
            }
        )
    ) {
        Icon(
            imageVector = if (isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
            contentDescription = null,
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = if (isFavorite) "已收藏" else "添加收藏",
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

/**
 * 翻译质量指示器
 */
@Composable
fun TranslationQualityIndicator(
    confidenceScore: Float,
    modifier: Modifier = Modifier
) {
    val qualityText = when {
        confidenceScore >= 0.9f -> "优秀"
        confidenceScore >= 0.7f -> "良好"
        confidenceScore >= 0.5f -> "一般"
        else -> "较差"
    }
    
    val qualityColor = when {
        confidenceScore >= 0.9f -> MaterialTheme.colorScheme.primary
        confidenceScore >= 0.7f -> MaterialTheme.colorScheme.secondary
        confidenceScore >= 0.5f -> MaterialTheme.colorScheme.tertiary
        else -> MaterialTheme.colorScheme.error
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        LinearProgressIndicator(
            progress = { confidenceScore },
            modifier = Modifier
                .width(60.dp)
                .height(4.dp),
            color = qualityColor,
        )
        
        Text(
            text = qualityText,
            style = MaterialTheme.typography.labelSmall,
            color = qualityColor
        )
    }
}
