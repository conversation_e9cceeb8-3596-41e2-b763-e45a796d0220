package com.cadence.domain.usecase

import com.cadence.domain.model.*
import com.cadence.domain.repository.FavoriteRepository
import com.cadence.domain.repository.TranslationRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 收藏管理用例
 * 处理收藏夹和收藏项的业务逻辑
 */
@Singleton
class ManageFavoritesUseCase @Inject constructor(
    private val favoriteRepository: FavoriteRepository,
    private val translationRepository: TranslationRepository
) {
    
    // ========== 收藏夹管理 ==========
    
    /**
     * 获取所有收藏夹
     */
    fun getAllFolders(): Flow<List<FavoriteFolder>> {
        return favoriteRepository.getAllFolders()
    }
    
    /**
     * 创建收藏夹
     */
    suspend fun createFolder(
        name: String,
        description: String? = null,
        color: String = "#2196F3",
        icon: String? = null
    ): Result<String> {
        return try {
            if (name.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏夹名称不能为空"))
            }
            
            val folder = FavoriteFolder(
                id = UUID.randomUUID().toString(),
                name = name.trim(),
                description = description?.trim(),
                color = color,
                icon = icon,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            
            val result = favoriteRepository.createFolder(folder)
            
            if (result.isSuccess) {
                Timber.d("创建收藏夹成功: $name")
            } else {
                Timber.e("创建收藏夹失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "创建收藏夹过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 更新收藏夹
     */
    suspend fun updateFolder(folder: FavoriteFolder): Result<Unit> {
        return try {
            if (folder.name.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏夹名称不能为空"))
            }
            
            val updatedFolder = folder.copy(
                name = folder.name.trim(),
                description = folder.description?.trim(),
                updatedAt = System.currentTimeMillis()
            )
            
            val result = favoriteRepository.updateFolder(updatedFolder)
            
            if (result.isSuccess) {
                Timber.d("更新收藏夹成功: ${folder.name}")
            } else {
                Timber.e("更新收藏夹失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新收藏夹过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 删除收藏夹
     */
    suspend fun deleteFolder(folderId: String, moveItemsToFolderId: String? = null): Result<Unit> {
        return try {
            if (folderId.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏夹ID不能为空"))
            }
            
            val result = favoriteRepository.deleteFolder(folderId, moveItemsToFolderId)
            
            if (result.isSuccess) {
                Timber.d("删除收藏夹成功: $folderId")
            } else {
                Timber.e("删除收藏夹失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "删除收藏夹过程中发生异常")
            Result.failure(e)
        }
    }
    
    // ========== 收藏项管理 ==========
    
    /**
     * 获取收藏夹中的项目（包含翻译详情）
     */
    fun getFolderItemsWithTranslations(folderId: String): Flow<List<Pair<FavoriteItem, Translation?>>> {
        return favoriteRepository.getFolderItems(folderId).map { items ->
            items.map { item ->
                val translation = try {
                    translationRepository.getTranslationById(item.translationId).getOrNull()
                } catch (e: Exception) {
                    Timber.w(e, "获取翻译记录失败: ${item.translationId}")
                    null
                }
                Pair(item, translation)
            }
        }
    }
    
    /**
     * 根据查询条件获取收藏项（包含翻译详情）
     */
    fun getFavoriteItemsWithTranslations(query: FavoriteQuery): Flow<List<Pair<FavoriteItem, Translation?>>> {
        return favoriteRepository.getFavoriteItems(query).map { items ->
            items.map { item ->
                val translation = try {
                    translationRepository.getTranslationById(item.translationId).getOrNull()
                } catch (e: Exception) {
                    Timber.w(e, "获取翻译记录失败: ${item.translationId}")
                    null
                }
                Pair(item, translation)
            }
        }
    }
    
    /**
     * 添加翻译记录到收藏夹
     */
    suspend fun addTranslationToFavorites(
        translationId: String,
        folderId: String? = null,
        note: String? = null,
        tags: List<String> = emptyList(),
        priority: FavoritePriority = FavoritePriority.NORMAL
    ): Result<String> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译记录ID不能为空"))
            }
            
            // 检查翻译记录是否存在
            val translationExists = translationRepository.getTranslationById(translationId).isSuccess
            if (!translationExists) {
                return Result.failure(IllegalArgumentException("翻译记录不存在"))
            }
            
            // 检查是否已经收藏
            val isAlreadyFavorited = favoriteRepository.isTranslationFavorited(translationId).getOrElse { false }
            if (isAlreadyFavorited) {
                return Result.failure(IllegalStateException("翻译记录已经收藏"))
            }
            
            val result = favoriteRepository.addTranslationToFavorites(
                translationId = translationId,
                folderId = folderId,
                note = note?.trim(),
                tags = tags.map { it.trim() }.filter { it.isNotBlank() },
                priority = priority
            )
            
            if (result.isSuccess) {
                // 同时更新翻译记录的收藏状态
                translationRepository.updateFavoriteStatus(translationId, true)
                Timber.d("添加收藏成功: $translationId")
            } else {
                Timber.e("添加收藏失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "添加收藏过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 从收藏夹移除翻译记录
     */
    suspend fun removeTranslationFromFavorites(translationId: String): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译记录ID不能为空"))
            }
            
            val result = favoriteRepository.removeTranslationFromFavorites(translationId)
            
            if (result.isSuccess) {
                // 同时更新翻译记录的收藏状态
                translationRepository.updateFavoriteStatus(translationId, false)
                Timber.d("移除收藏成功: $translationId")
            } else {
                Timber.e("移除收藏失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "移除收藏过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 根据翻译ID移除收藏
     */
    suspend fun removeFavoriteByTranslationId(translationId: String): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译记录ID不能为空"))
            }

            val result = favoriteRepository.removeTranslationFromFavorites(translationId)

            if (result.isSuccess) {
                // 同时更新翻译记录的收藏状态
                translationRepository.updateFavoriteStatus(translationId, false)
                Timber.d("根据翻译ID移除收藏成功: $translationId")
            } else {
                Timber.e("根据翻译ID移除收藏失败: ${result.exceptionOrNull()?.message}")
            }

            result
        } catch (e: Exception) {
            Timber.e(e, "根据翻译ID移除收藏过程中发生异常")
            Result.failure(e)
        }
    }

    /**
     * 根据ID获取收藏项
     */
    suspend fun getFavoriteItemById(favoriteId: String): Result<FavoriteItem> {
        return try {
            if (favoriteId.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏项ID不能为空"))
            }

            val result = favoriteRepository.getFavoriteItemById(favoriteId)
            if (result.isSuccess) {
                val item = result.getOrNull()
                if (item != null) {
                    Result.success(item)
                } else {
                    Result.failure(Exception("收藏项不存在"))
                }
            } else {
                result.map { it!! } // 这里不会执行，因为已经检查了isSuccess
            }
        } catch (e: Exception) {
            Timber.e(e, "获取收藏项详情过程中发生异常")
            Result.failure(e)
        }
    }

    /**
     * 更新收藏项
     */
    suspend fun updateFavoriteItem(item: FavoriteItem): Result<Unit> {
        return try {
            val updatedItem = item.copy(
                note = item.note?.trim(),
                tags = item.tags.map { it.trim() }.filter { it.isNotBlank() },
                updatedAt = System.currentTimeMillis()
            )

            val result = favoriteRepository.updateFavoriteItem(updatedItem)
            
            if (result.isSuccess) {
                Timber.d("更新收藏项成功: ${item.id}")
            } else {
                Timber.e("更新收藏项失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新收藏项过程中发生异常")
            Result.failure(e)
        }
    }

    /**
     * 更新收藏项（重载方法）
     */
    suspend fun updateFavoriteItem(
        favoriteId: String,
        note: String?,
        tags: List<String>,
        priority: FavoritePriority
    ): Result<Unit> {
        return try {
            if (favoriteId.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏项ID不能为空"))
            }

            // 先获取现有的收藏项
            val existingItemResult = favoriteRepository.getFavoriteItemById(favoriteId)
            if (existingItemResult.isFailure) {
                return Result.failure(existingItemResult.exceptionOrNull() ?: Exception("获取收藏项失败"))
            }

            val existingItem = existingItemResult.getOrThrow()
            val updatedItem = existingItem.copy(
                note = note?.trim(),
                tags = tags.map { it.trim() }.filter { it.isNotBlank() },
                priority = priority,
                updatedAt = System.currentTimeMillis()
            )

            val result = favoriteRepository.updateFavoriteItem(updatedItem)

            if (result.isSuccess) {
                Timber.d("更新收藏项成功: $favoriteId")
            } else {
                Timber.e("更新收藏项失败: ${result.exceptionOrNull()?.message}")
            }

            result
        } catch (e: Exception) {
            Timber.e(e, "更新收藏项过程中发生异常")
            Result.failure(e)
        }
    }

    /**
     * 移动收藏项到其他收藏夹
     */
    suspend fun moveFavoriteItem(itemId: String, targetFolderId: String): Result<Unit> {
        return try {
            if (itemId.isBlank() || targetFolderId.isBlank()) {
                return Result.failure(IllegalArgumentException("收藏项ID和目标收藏夹ID不能为空"))
            }
            
            val result = favoriteRepository.moveFavoriteItem(itemId, targetFolderId)
            
            if (result.isSuccess) {
                Timber.d("移动收藏项成功: $itemId -> $targetFolderId")
            } else {
                Timber.e("移动收藏项失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "移动收藏项过程中发生异常")
            Result.failure(e)
        }
    }
    
    // ========== 搜索和筛选 ==========
    
    /**
     * 搜索收藏项（包含翻译详情）
     */
    fun searchFavoriteItemsWithTranslations(
        searchQuery: String,
        folderId: String? = null
    ): Flow<List<Pair<FavoriteItem, Translation?>>> {
        return favoriteRepository.searchFavoriteItems(searchQuery, folderId).map { items ->
            items.map { item ->
                val translation = try {
                    translationRepository.getTranslationById(item.translationId).getOrNull()
                } catch (e: Exception) {
                    Timber.w(e, "获取翻译记录失败: ${item.translationId}")
                    null
                }
                Pair(item, translation)
            }
        }
    }
    
    /**
     * 获取所有使用的标签
     */
    fun getAllUsedTags(): Flow<List<String>> {
        return favoriteRepository.getAllUsedTags()
    }
    
    /**
     * 获取搜索建议
     */
    suspend fun getSearchSuggestions(query: String): Result<List<FavoriteSearchSuggestion>> {
        return try {
            if (query.isBlank()) {
                return Result.success(emptyList())
            }
            
            favoriteRepository.getSearchSuggestions(query.trim())
        } catch (e: Exception) {
            Timber.e(e, "获取搜索建议过程中发生异常")
            Result.failure(e)
        }
    }
    
    // ========== 统计信息 ==========
    
    /**
     * 获取收藏夹统计信息
     */
    suspend fun getFavoriteStatistics(): Result<FavoriteStatistics> {
        return try {
            favoriteRepository.getFavoriteStatistics()
        } catch (e: Exception) {
            Timber.e(e, "获取收藏统计信息过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取收藏夹概览（收藏夹列表 + 统计信息）
     */
    fun getFavoriteOverview(): Flow<Pair<List<FavoriteFolder>, FavoriteStatistics?>> {
        return favoriteRepository.getAllFolders().map { folders ->
            val statistics = try {
                favoriteRepository.getFavoriteStatistics().getOrNull()
            } catch (e: Exception) {
                Timber.w(e, "获取统计信息失败")
                null
            }
            Pair(folders, statistics)
        }
    }
    
    // ========== 数据管理 ==========
    
    /**
     * 导出收藏夹数据
     */
    suspend fun exportFavoriteData(folderIds: List<String> = emptyList()): Result<FavoriteExportData> {
        return try {
            favoriteRepository.exportFavoriteData(folderIds)
        } catch (e: Exception) {
            Timber.e(e, "导出收藏数据过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 导入收藏夹数据
     */
    suspend fun importFavoriteData(
        exportData: FavoriteExportData,
        mergeStrategy: String = "skip"
    ): Result<FavoriteImportResult> {
        return try {
            favoriteRepository.importFavoriteData(exportData, mergeStrategy)
        } catch (e: Exception) {
            Timber.e(e, "导入收藏数据过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 清理无效的收藏项（对应的翻译记录已删除）
     */
    suspend fun cleanupInvalidFavoriteItems(): Result<Int> {
        return try {
            // 这里需要实现清理逻辑
            // 获取所有收藏项，检查对应的翻译记录是否存在
            // 删除无效的收藏项
            Timber.d("清理无效收藏项功能待实现")
            Result.success(0)
        } catch (e: Exception) {
            Timber.e(e, "清理无效收藏项过程中发生异常")
            Result.failure(e)
        }
    }
}
