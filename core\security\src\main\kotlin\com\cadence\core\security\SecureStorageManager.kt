package com.cadence.core.security

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 安全存储管理器
 * 负责敏感数据的安全存储和访问
 */
@Singleton
class SecureStorageManager @Inject constructor(
    private val context: Context,
    private val cryptoManager: CryptoManager
) {
    
    companion object {
        private const val SECURE_PREFS_NAME = "cadence_secure_prefs"
        private const val USER_PREFS_NAME = "cadence_user_prefs"
        private const val TRANSLATION_CACHE_PREFS = "cadence_translation_cache"
    }
    
    // 加密的SharedPreferences实例
    private val securePrefs: SharedPreferences by lazy {
        cryptoManager.createEncryptedSharedPreferences(SECURE_PREFS_NAME)
    }
    
    private val userPrefs: SharedPreferences by lazy {
        cryptoManager.createEncryptedSharedPreferences(USER_PREFS_NAME)
    }
    
    private val translationCachePrefs: SharedPreferences by lazy {
        cryptoManager.createEncryptedSharedPreferences(TRANSLATION_CACHE_PREFS)
    }
    
    /**
     * 存储API密钥
     */
    suspend fun storeApiKey(apiKey: String) = withContext(Dispatchers.IO) {
        try {
            val encryptedKey = cryptoManager.encrypt(apiKey)
            val keyData = "${encryptedKey.encryptedData.joinToString(",")};${encryptedKey.iv.joinToString(",")}"
            
            securePrefs.edit()
                .putString("api_key", keyData)
                .apply()
            
            Timber.d("API密钥已安全存储")
        } catch (e: Exception) {
            Timber.e(e, "API密钥存储失败")
            throw SecurityException("API密钥存储失败", e)
        }
    }
    
    /**
     * 获取API密钥
     */
    suspend fun getApiKey(): String? = withContext(Dispatchers.IO) {
        try {
            val keyData = securePrefs.getString("api_key", null) ?: return@withContext null
            
            val parts = keyData.split(";")
            if (parts.size != 2) return@withContext null
            
            val encryptedData = parts[0].split(",").map { it.toByte() }.toByteArray()
            val iv = parts[1].split(",").map { it.toByte() }.toByteArray()
            
            val encryptedKey = EncryptedData(encryptedData, iv)
            cryptoManager.decrypt(encryptedKey)
        } catch (e: Exception) {
            Timber.e(e, "API密钥获取失败")
            null
        }
    }
    
    /**
     * 存储用户认证令牌
     */
    suspend fun storeAuthToken(token: String) = withContext(Dispatchers.IO) {
        try {
            val encryptedToken = cryptoManager.encrypt(token)
            val tokenData = "${encryptedToken.encryptedData.joinToString(",")};${encryptedToken.iv.joinToString(",")}"
            
            securePrefs.edit()
                .putString("auth_token", tokenData)
                .putLong("token_timestamp", System.currentTimeMillis())
                .apply()
            
            Timber.d("认证令牌已安全存储")
        } catch (e: Exception) {
            Timber.e(e, "认证令牌存储失败")
            throw SecurityException("认证令牌存储失败", e)
        }
    }
    
    /**
     * 获取用户认证令牌
     */
    suspend fun getAuthToken(): String? = withContext(Dispatchers.IO) {
        try {
            val tokenData = securePrefs.getString("auth_token", null) ?: return@withContext null
            val timestamp = securePrefs.getLong("token_timestamp", 0)
            
            // 检查令牌是否过期（24小时）
            if (System.currentTimeMillis() - timestamp > 24 * 60 * 60 * 1000) {
                clearAuthToken()
                return@withContext null
            }
            
            val parts = tokenData.split(";")
            if (parts.size != 2) return@withContext null
            
            val encryptedData = parts[0].split(",").map { it.toByte() }.toByteArray()
            val iv = parts[1].split(",").map { it.toByte() }.toByteArray()
            
            val encryptedToken = EncryptedData(encryptedData, iv)
            cryptoManager.decrypt(encryptedToken)
        } catch (e: Exception) {
            Timber.e(e, "认证令牌获取失败")
            null
        }
    }
    
    /**
     * 清除认证令牌
     */
    fun clearAuthToken() {
        securePrefs.edit()
            .remove("auth_token")
            .remove("token_timestamp")
            .apply()
        Timber.d("认证令牌已清除")
    }
    
    /**
     * 存储用户偏好设置
     */
    fun storeUserPreference(key: String, value: String) {
        userPrefs.edit()
            .putString(key, value)
            .apply()
    }
    
    fun storeUserPreference(key: String, value: Boolean) {
        userPrefs.edit()
            .putBoolean(key, value)
            .apply()
    }
    
    fun storeUserPreference(key: String, value: Int) {
        userPrefs.edit()
            .putInt(key, value)
            .apply()
    }
    
    fun storeUserPreference(key: String, value: Long) {
        userPrefs.edit()
            .putLong(key, value)
            .apply()
    }
    
    /**
     * 获取用户偏好设置
     */
    fun getUserPreference(key: String, defaultValue: String): String {
        return userPrefs.getString(key, defaultValue) ?: defaultValue
    }
    
    fun getUserPreference(key: String, defaultValue: Boolean): Boolean {
        return userPrefs.getBoolean(key, defaultValue)
    }
    
    fun getUserPreference(key: String, defaultValue: Int): Int {
        return userPrefs.getInt(key, defaultValue)
    }
    
    fun getUserPreference(key: String, defaultValue: Long): Long {
        return userPrefs.getLong(key, defaultValue)
    }
    
    /**
     * 存储翻译缓存
     */
    suspend fun storeTranslationCache(key: String, translation: String) = withContext(Dispatchers.IO) {
        try {
            val encryptedTranslation = cryptoManager.encrypt(translation)
            val translationData = "${encryptedTranslation.encryptedData.joinToString(",")};${encryptedTranslation.iv.joinToString(",")}"
            
            translationCachePrefs.edit()
                .putString(key, translationData)
                .putLong("${key}_timestamp", System.currentTimeMillis())
                .apply()
            
            Timber.d("翻译缓存已存储: $key")
        } catch (e: Exception) {
            Timber.e(e, "翻译缓存存储失败")
        }
    }
    
    /**
     * 获取翻译缓存
     */
    suspend fun getTranslationCache(key: String): String? = withContext(Dispatchers.IO) {
        try {
            val translationData = translationCachePrefs.getString(key, null) ?: return@withContext null
            val timestamp = translationCachePrefs.getLong("${key}_timestamp", 0)
            
            // 检查缓存是否过期（7天）
            if (System.currentTimeMillis() - timestamp > 7 * 24 * 60 * 60 * 1000) {
                clearTranslationCache(key)
                return@withContext null
            }
            
            val parts = translationData.split(";")
            if (parts.size != 2) return@withContext null
            
            val encryptedData = parts[0].split(",").map { it.toByte() }.toByteArray()
            val iv = parts[1].split(",").map { it.toByte() }.toByteArray()
            
            val encryptedTranslation = EncryptedData(encryptedData, iv)
            cryptoManager.decrypt(encryptedTranslation)
        } catch (e: Exception) {
            Timber.e(e, "翻译缓存获取失败")
            null
        }
    }
    
    /**
     * 清除翻译缓存
     */
    fun clearTranslationCache(key: String) {
        translationCachePrefs.edit()
            .remove(key)
            .remove("${key}_timestamp")
            .apply()
    }
    
    /**
     * 清除所有翻译缓存
     */
    fun clearAllTranslationCache() {
        translationCachePrefs.edit().clear().apply()
        Timber.d("所有翻译缓存已清除")
    }
    
    /**
     * 清除所有用户数据
     */
    fun clearAllUserData() {
        securePrefs.edit().clear().apply()
        userPrefs.edit().clear().apply()
        translationCachePrefs.edit().clear().apply()
        Timber.d("所有用户数据已清除")
    }
    
    /**
     * 获取存储使用情况
     */
    fun getStorageUsage(): StorageUsage {
        val securePrefsSize = securePrefs.all.size
        val userPrefsSize = userPrefs.all.size
        val cacheSize = translationCachePrefs.all.size
        
        return StorageUsage(
            secureDataCount = securePrefsSize,
            userPreferencesCount = userPrefsSize,
            translationCacheCount = cacheSize,
            totalItems = securePrefsSize + userPrefsSize + cacheSize
        )
    }
    
    /**
     * 检查存储健康状态
     */
    fun checkStorageHealth(): StorageHealthStatus {
        return try {
            // 尝试读写测试
            val testKey = "health_check_test"
            val testValue = "test_value_${System.currentTimeMillis()}"
            
            userPrefs.edit().putString(testKey, testValue).apply()
            val retrievedValue = userPrefs.getString(testKey, null)
            userPrefs.edit().remove(testKey).apply()
            
            if (retrievedValue == testValue) {
                StorageHealthStatus.HEALTHY
            } else {
                StorageHealthStatus.CORRUPTED
            }
        } catch (e: Exception) {
            Timber.e(e, "存储健康检查失败")
            StorageHealthStatus.ERROR
        }
    }
}

/**
 * 存储使用情况
 */
data class StorageUsage(
    val secureDataCount: Int,
    val userPreferencesCount: Int,
    val translationCacheCount: Int,
    val totalItems: Int
)

/**
 * 存储健康状态
 */
enum class StorageHealthStatus {
    HEALTHY, CORRUPTED, ERROR
}
