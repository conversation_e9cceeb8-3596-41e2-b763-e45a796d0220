package com.cadence.feature.offline

import android.content.Context
import com.cadence.core.logging.StructuredLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.*
import okio.buffer
import okio.sink
import timber.log.Timber
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语言包下载管理器
 * 负责语言包的下载、进度管理和状态跟踪
 */
@Singleton
class LanguagePackDownloader @Inject constructor(
    private val context: Context,
    private val modelManager: TranslationModelManager,
    private val structuredLogger: StructuredLogger,
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val DOWNLOAD_TIMEOUT_MS = 300000L // 5分钟
        private const val MAX_CONCURRENT_DOWNLOADS = 2
        private const val RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 2000L
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 下载状态管理
    private val _downloadState = MutableStateFlow(DownloadManagerState())
    val downloadState: StateFlow<DownloadManagerState> = _downloadState.asStateFlow()
    
    // 活跃下载任务
    private val activeDownloads = mutableMapOf<String, Job>()
    private val downloadLock = Any()
    
    init {
        Timber.d("语言包下载管理器已初始化")
    }
    
    /**
     * 开始下载语言包
     */
    suspend fun downloadLanguagePack(modelInfo: ModelInfo): DownloadResult = withContext(Dispatchers.IO) {
        try {
            synchronized(downloadLock) {
                if (activeDownloads.containsKey(modelInfo.key)) {
                    return@withContext DownloadResult(
                        success = false,
                        modelKey = modelInfo.key,
                        error = "下载已在进行中"
                    )
                }
                
                if (activeDownloads.size >= MAX_CONCURRENT_DOWNLOADS) {
                    return@withContext DownloadResult(
                        success = false,
                        modelKey = modelInfo.key,
                        error = "达到最大并发下载数限制"
                    )
                }
            }
            
            val downloadJob = scope.launch {
                performDownload(modelInfo)
            }
            
            synchronized(downloadLock) {
                activeDownloads[modelInfo.key] = downloadJob
            }
            
            // 更新下载状态
            updateDownloadState { state ->
                state.copy(
                    activeDownloads = state.activeDownloads + (modelInfo.key to DownloadProgress(
                        modelKey = modelInfo.key,
                        modelName = modelInfo.name,
                        totalSize = modelInfo.size,
                        downloadedSize = 0,
                        progress = 0f,
                        status = DownloadStatus.DOWNLOADING,
                        startTime = System.currentTimeMillis()
                    ))
                )
            }
            
            structuredLogger.logInfo(
                message = "开始下载语言包",
                context = mapOf(
                    "model_key" to modelInfo.key,
                    "model_name" to modelInfo.name,
                    "model_size" to modelInfo.size.toString(),
                    "download_url" to modelInfo.downloadUrl
                )
            )
            
            // 等待下载完成
            val result = downloadJob.await()
            
            synchronized(downloadLock) {
                activeDownloads.remove(modelInfo.key)
            }
            
            result
            
        } catch (e: Exception) {
            Timber.e(e, "下载语言包失败: ${modelInfo.key}")
            
            synchronized(downloadLock) {
                activeDownloads.remove(modelInfo.key)
            }
            
            updateDownloadState { state ->
                state.copy(
                    activeDownloads = state.activeDownloads - modelInfo.key,
                    failedDownloads = state.failedDownloads + (modelInfo.key to e.message.orEmpty())
                )
            }
            
            DownloadResult(
                success = false,
                modelKey = modelInfo.key,
                error = e.message ?: "下载失败"
            )
        }
    }
    
    /**
     * 取消下载
     */
    fun cancelDownload(modelKey: String): Boolean {
        return synchronized(downloadLock) {
            val downloadJob = activeDownloads[modelKey]
            if (downloadJob != null) {
                downloadJob.cancel()
                activeDownloads.remove(modelKey)
                
                updateDownloadState { state ->
                    state.copy(
                        activeDownloads = state.activeDownloads - modelKey
                    )
                }
                
                structuredLogger.logInfo(
                    message = "下载已取消",
                    context = mapOf("model_key" to modelKey)
                )
                
                true
            } else {
                false
            }
        }
    }
    
    /**
     * 暂停下载
     */
    fun pauseDownload(modelKey: String): Boolean {
        return synchronized(downloadLock) {
            val downloadJob = activeDownloads[modelKey]
            if (downloadJob != null && downloadJob.isActive) {
                // TODO: 实现下载暂停逻辑
                // 当前简单实现为取消下载
                downloadJob.cancel()
                
                updateDownloadState { state ->
                    val currentProgress = state.activeDownloads[modelKey]
                    if (currentProgress != null) {
                        state.copy(
                            activeDownloads = state.activeDownloads + (modelKey to currentProgress.copy(
                                status = DownloadStatus.PAUSED
                            ))
                        )
                    } else {
                        state
                    }
                }
                
                true
            } else {
                false
            }
        }
    }
    
    /**
     * 恢复下载
     */
    suspend fun resumeDownload(modelKey: String): Boolean {
        // TODO: 实现断点续传逻辑
        // 当前简单实现为重新开始下载
        val modelInfo = modelManager.getModelInfo(modelKey)
        return if (modelInfo != null) {
            val result = downloadLanguagePack(modelInfo)
            result.success
        } else {
            false
        }
    }
    
    /**
     * 获取下载进度
     */
    fun getDownloadProgress(modelKey: String): DownloadProgress? {
        return _downloadState.value.activeDownloads[modelKey]
    }
    
    /**
     * 获取所有活跃下载
     */
    fun getActiveDownloads(): Map<String, DownloadProgress> {
        return _downloadState.value.activeDownloads
    }
    
    /**
     * 清理失败的下载记录
     */
    fun clearFailedDownloads() {
        updateDownloadState { state ->
            state.copy(failedDownloads = emptyMap())
        }
    }
    
    /**
     * 执行实际的下载操作
     */
    private suspend fun performDownload(modelInfo: ModelInfo): DownloadResult = withContext(Dispatchers.IO) {
        var attempt = 0
        var lastException: Exception? = null
        
        while (attempt < RETRY_COUNT) {
            try {
                attempt++
                
                structuredLogger.logInfo(
                    message = "下载尝试",
                    context = mapOf(
                        "model_key" to modelInfo.key,
                        "attempt" to attempt.toString(),
                        "max_attempts" to RETRY_COUNT.toString()
                    )
                )
                
                val result = downloadWithProgress(modelInfo)
                
                if (result.success) {
                    // 下载成功，安装模型
                    val modelData = File(result.downloadPath!!).readBytes()
                    val installResult = modelManager.installModel(modelInfo, modelData)
                    
                    // 清理临时下载文件
                    File(result.downloadPath).delete()
                    
                    if (installResult.success) {
                        updateDownloadState { state ->
                            state.copy(
                                activeDownloads = state.activeDownloads - modelInfo.key,
                                completedDownloads = state.completedDownloads + modelInfo.key
                            )
                        }
                        
                        structuredLogger.logInfo(
                            message = "语言包下载和安装成功",
                            context = mapOf(
                                "model_key" to modelInfo.key,
                                "attempts" to attempt.toString(),
                                "install_path" to installResult.installedPath
                            )
                        )
                        
                        return@withContext DownloadResult(
                            success = true,
                            modelKey = modelInfo.key,
                            downloadPath = installResult.installedPath
                        )
                    } else {
                        throw Exception("模型安装失败: ${installResult.error}")
                    }
                } else {
                    throw Exception(result.error ?: "下载失败")
                }
                
            } catch (e: Exception) {
                lastException = e
                Timber.w(e, "下载尝试失败 (${attempt}/${RETRY_COUNT}): ${modelInfo.key}")
                
                if (attempt < RETRY_COUNT) {
                    delay(RETRY_DELAY_MS * attempt) // 指数退避
                }
            }
        }
        
        // 所有重试都失败了
        structuredLogger.logError(
            message = "语言包下载失败，已达最大重试次数",
            error = lastException,
            context = mapOf(
                "model_key" to modelInfo.key,
                "total_attempts" to RETRY_COUNT.toString()
            )
        )
        
        updateDownloadState { state ->
            state.copy(
                activeDownloads = state.activeDownloads - modelInfo.key,
                failedDownloads = state.failedDownloads + (modelInfo.key to (lastException?.message ?: "下载失败"))
            )
        }
        
        DownloadResult(
            success = false,
            modelKey = modelInfo.key,
            error = lastException?.message ?: "下载失败，已达最大重试次数"
        )
    }

    /**
     * 带进度的下载实现
     */
    private suspend fun downloadWithProgress(modelInfo: ModelInfo): DownloadResult = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url(modelInfo.downloadUrl)
                .build()

            val response = withTimeout(DOWNLOAD_TIMEOUT_MS) {
                okHttpClient.newCall(request).execute()
            }

            if (!response.isSuccessful) {
                throw IOException("下载失败: HTTP ${response.code}")
            }

            val responseBody = response.body ?: throw IOException("响应体为空")
            val contentLength = responseBody.contentLength()

            // 创建临时下载文件
            val tempFile = File(context.cacheDir, "${modelInfo.key}.tmp")
            val sink = tempFile.sink().buffer()

            val source = responseBody.source()
            var totalBytesRead = 0L
            val bufferSize = 8192L

            try {
                while (!source.exhausted()) {
                    val bytesRead = source.read(sink.buffer, bufferSize)
                    if (bytesRead != -1L) {
                        totalBytesRead += bytesRead
                        sink.emit()

                        // 更新下载进度
                        val progress = if (contentLength > 0) {
                            (totalBytesRead.toFloat() / contentLength.toFloat())
                        } else {
                            0f
                        }

                        updateDownloadProgress(modelInfo.key, totalBytesRead, progress)
                    }

                    // 检查是否被取消
                    ensureActive()
                }

                sink.close()
                source.close()

                DownloadResult(
                    success = true,
                    modelKey = modelInfo.key,
                    downloadPath = tempFile.absolutePath
                )

            } catch (e: Exception) {
                sink.close()
                source.close()
                tempFile.delete() // 清理临时文件
                throw e
            }

        } catch (e: Exception) {
            throw e
        }
    }

    /**
     * 更新下载进度
     */
    private fun updateDownloadProgress(modelKey: String, downloadedSize: Long, progress: Float) {
        updateDownloadState { state ->
            val currentProgress = state.activeDownloads[modelKey]
            if (currentProgress != null) {
                state.copy(
                    activeDownloads = state.activeDownloads + (modelKey to currentProgress.copy(
                        downloadedSize = downloadedSize,
                        progress = progress,
                        lastUpdateTime = System.currentTimeMillis()
                    ))
                )
            } else {
                state
            }
        }
    }

    /**
     * 更新下载状态
     */
    private fun updateDownloadState(update: (DownloadManagerState) -> DownloadManagerState) {
        _downloadState.value = update(_downloadState.value)
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        synchronized(downloadLock) {
            activeDownloads.values.forEach { it.cancel() }
            activeDownloads.clear()
        }
        Timber.d("语言包下载管理器已清理")
    }
}

// 数据类定义
data class DownloadManagerState(
    val activeDownloads: Map<String, DownloadProgress> = emptyMap(),
    val completedDownloads: Set<String> = emptySet(),
    val failedDownloads: Map<String, String> = emptyMap()
)

data class DownloadProgress(
    val modelKey: String,
    val modelName: String,
    val totalSize: Long,
    val downloadedSize: Long,
    val progress: Float,
    val status: DownloadStatus,
    val startTime: Long,
    val lastUpdateTime: Long = System.currentTimeMillis(),
    val estimatedTimeRemaining: Long = 0L
) {
    val downloadSpeed: Long
        get() {
            val elapsedTime = (lastUpdateTime - startTime) / 1000L
            return if (elapsedTime > 0) downloadedSize / elapsedTime else 0L
        }

    val isCompleted: Boolean
        get() = status == DownloadStatus.COMPLETED

    val isFailed: Boolean
        get() = status == DownloadStatus.FAILED

    val isPaused: Boolean
        get() = status == DownloadStatus.PAUSED
}

enum class DownloadStatus {
    PENDING,
    DOWNLOADING,
    PAUSED,
    COMPLETED,
    FAILED,
    CANCELLED
}

data class DownloadResult(
    val success: Boolean,
    val modelKey: String,
    val downloadPath: String? = null,
    val error: String? = null
)
