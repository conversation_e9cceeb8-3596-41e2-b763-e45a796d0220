package com.cadence.core.logging

import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 错误分类器
 * 负责对异常进行分类和严重性评估
 */
@Singleton
class ErrorClassifier @Inject constructor() {
    
    companion object {
        // 网络相关异常
        private val NETWORK_EXCEPTIONS = setOf(
            "IOException",
            "SocketTimeoutException",
            "UnknownHostException",
            "ConnectException",
            "HttpException",
            "SSLException",
            "NetworkOnMainThreadException"
        )
        
        // 内存相关异常
        private val MEMORY_EXCEPTIONS = setOf(
            "OutOfMemoryError",
            "StackOverflowError"
        )
        
        // 安全相关异常
        private val SECURITY_EXCEPTIONS = setOf(
            "SecurityException",
            "CertificateException",
            "SSLHandshakeException",
            "InvalidKeyException"
        )
        
        // 数据库相关异常
        private val DATABASE_EXCEPTIONS = setOf(
            "SQLiteException",
            "SQLiteDatabaseCorruptException",
            "SQLiteFullException",
            "SQLiteDiskIOException"
        )
        
        // UI相关异常
        private val UI_EXCEPTIONS = setOf(
            "WindowManager\$BadTokenException",
            "IllegalStateException",
            "ActivityNotFoundException",
            "FragmentManager\$IllegalStateException"
        )
        
        // 并发相关异常
        private val CONCURRENCY_EXCEPTIONS = setOf(
            "ConcurrentModificationException",
            "IllegalMonitorStateException",
            "DeadlockException"
        )
        
        // 配置相关异常
        private val CONFIGURATION_EXCEPTIONS = setOf(
            "MissingResourceException",
            "ClassNotFoundException",
            "NoSuchMethodException",
            "IllegalAccessException"
        )
    }
    
    /**
     * 分类错误
     */
    fun classifyError(exception: Throwable): ErrorClassification {
        val exceptionName = exception.javaClass.simpleName
        val message = exception.message ?: ""
        val stackTrace = getStackTraceString(exception)
        
        // 确定错误类别
        val category = determineCategory(exceptionName, message, stackTrace)
        
        // 确定严重性
        val severity = determineSeverity(exception, category)
        
        // 确定是否可恢复
        val isRecoverable = determineRecoverability(exception, category)
        
        // 确定用户影响
        val userImpact = determineUserImpact(exception, category, severity)
        
        // 生成建议
        val suggestions = generateSuggestions(exception, category)
        
        return ErrorClassification(
            category = category,
            severity = severity,
            isRecoverable = isRecoverable,
            userImpact = userImpact,
            suggestions = suggestions,
            tags = generateTags(exception, category)
        )
    }
    
    /**
     * 确定错误类别
     */
    private fun determineCategory(exceptionName: String, message: String, stackTrace: String): String {
        return when {
            NETWORK_EXCEPTIONS.contains(exceptionName) -> "NETWORK"
            MEMORY_EXCEPTIONS.contains(exceptionName) -> "MEMORY"
            SECURITY_EXCEPTIONS.contains(exceptionName) -> "SECURITY"
            DATABASE_EXCEPTIONS.contains(exceptionName) -> "DATABASE"
            UI_EXCEPTIONS.contains(exceptionName) -> "UI"
            CONCURRENCY_EXCEPTIONS.contains(exceptionName) -> "CONCURRENCY"
            CONFIGURATION_EXCEPTIONS.contains(exceptionName) -> "CONFIGURATION"
            
            // 基于消息内容的分类
            message.contains("permission", ignoreCase = true) -> "PERMISSION"
            message.contains("timeout", ignoreCase = true) -> "TIMEOUT"
            message.contains("parse", ignoreCase = true) -> "PARSING"
            message.contains("json", ignoreCase = true) -> "SERIALIZATION"
            message.contains("null", ignoreCase = true) -> "NULL_POINTER"
            
            // 基于堆栈跟踪的分类
            stackTrace.contains("retrofit", ignoreCase = true) -> "NETWORK"
            stackTrace.contains("room", ignoreCase = true) -> "DATABASE"
            stackTrace.contains("compose", ignoreCase = true) -> "UI"
            stackTrace.contains("coroutines", ignoreCase = true) -> "CONCURRENCY"
            
            else -> "UNKNOWN"
        }
    }
    
    /**
     * 确定严重性级别
     */
    private fun determineSeverity(exception: Throwable, category: String): ErrorSeverity {
        return when {
            // 致命错误
            exception is OutOfMemoryError -> ErrorSeverity.CRITICAL
            exception is StackOverflowError -> ErrorSeverity.CRITICAL
            category == "SECURITY" -> ErrorSeverity.CRITICAL
            category == "DATABASE" && exception.message?.contains("corrupt") == true -> ErrorSeverity.CRITICAL
            
            // 高严重性错误
            category == "MEMORY" -> ErrorSeverity.HIGH
            category == "DATABASE" -> ErrorSeverity.HIGH
            exception is RuntimeException && category == "UI" -> ErrorSeverity.HIGH
            
            // 中等严重性错误
            category == "NETWORK" -> ErrorSeverity.MEDIUM
            category == "TIMEOUT" -> ErrorSeverity.MEDIUM
            category == "PARSING" -> ErrorSeverity.MEDIUM
            category == "SERIALIZATION" -> ErrorSeverity.MEDIUM
            
            // 低严重性错误
            category == "PERMISSION" -> ErrorSeverity.LOW
            category == "CONFIGURATION" -> ErrorSeverity.LOW
            exception is IllegalArgumentException -> ErrorSeverity.LOW
            
            else -> ErrorSeverity.MEDIUM
        }
    }
    
    /**
     * 确定是否可恢复
     */
    private fun determineRecoverability(exception: Throwable, category: String): Boolean {
        return when {
            // 不可恢复的错误
            exception is OutOfMemoryError -> false
            exception is StackOverflowError -> false
            category == "SECURITY" -> false
            category == "DATABASE" && exception.message?.contains("corrupt") == true -> false
            
            // 可恢复的错误
            category == "NETWORK" -> true
            category == "TIMEOUT" -> true
            category == "PERMISSION" -> true
            category == "PARSING" -> true
            category == "SERIALIZATION" -> true
            category == "UI" -> true
            
            else -> true
        }
    }
    
    /**
     * 确定用户影响
     */
    private fun determineUserImpact(exception: Throwable, category: String, severity: ErrorSeverity): UserImpact {
        return when (severity) {
            ErrorSeverity.CRITICAL -> UserImpact.SEVERE
            ErrorSeverity.HIGH -> when (category) {
                "UI" -> UserImpact.SEVERE
                "DATABASE" -> UserImpact.MAJOR
                else -> UserImpact.MAJOR
            }
            ErrorSeverity.MEDIUM -> when (category) {
                "NETWORK" -> UserImpact.MINOR
                "TIMEOUT" -> UserImpact.MINOR
                else -> UserImpact.MODERATE
            }
            ErrorSeverity.LOW -> UserImpact.MINIMAL
        }
    }
    
    /**
     * 生成修复建议
     */
    private fun generateSuggestions(exception: Throwable, category: String): List<String> {
        val suggestions = mutableListOf<String>()
        
        when (category) {
            "NETWORK" -> {
                suggestions.add("检查网络连接")
                suggestions.add("重试网络请求")
                suggestions.add("使用缓存数据")
                suggestions.add("显示离线模式")
            }
            "MEMORY" -> {
                suggestions.add("释放未使用的资源")
                suggestions.add("优化内存使用")
                suggestions.add("增加内存限制")
                suggestions.add("使用内存分析工具")
            }
            "DATABASE" -> {
                suggestions.add("检查数据库完整性")
                suggestions.add("重建数据库索引")
                suggestions.add("清理过期数据")
                suggestions.add("备份和恢复数据")
            }
            "UI" -> {
                suggestions.add("检查UI状态")
                suggestions.add("重新初始化UI组件")
                suggestions.add("使用默认UI状态")
                suggestions.add("显示错误提示")
            }
            "SECURITY" -> {
                suggestions.add("检查权限配置")
                suggestions.add("更新安全证书")
                suggestions.add("重新认证用户")
                suggestions.add("联系安全团队")
            }
            "TIMEOUT" -> {
                suggestions.add("增加超时时间")
                suggestions.add("重试操作")
                suggestions.add("优化性能")
                suggestions.add("使用异步处理")
            }
            "PARSING" -> {
                suggestions.add("验证数据格式")
                suggestions.add("使用默认值")
                suggestions.add("更新解析逻辑")
                suggestions.add("记录原始数据")
            }
            "PERMISSION" -> {
                suggestions.add("请求必要权限")
                suggestions.add("提供权限说明")
                suggestions.add("使用替代方案")
                suggestions.add("引导用户设置")
            }
            else -> {
                suggestions.add("重启应用")
                suggestions.add("清除应用缓存")
                suggestions.add("更新应用版本")
                suggestions.add("联系技术支持")
            }
        }
        
        return suggestions
    }
    
    /**
     * 生成标签
     */
    private fun generateTags(exception: Throwable, category: String): List<String> {
        val tags = mutableListOf<String>()
        
        tags.add(category.lowercase())
        tags.add(exception.javaClass.simpleName.lowercase())
        
        // 添加特定标签
        when (category) {
            "NETWORK" -> {
                if (exception is SocketTimeoutException) tags.add("timeout")
                if (exception is UnknownHostException) tags.add("dns")
                if (exception is IOException) tags.add("io")
            }
            "UI" -> {
                if (exception.message?.contains("token") == true) tags.add("window_token")
                if (exception.message?.contains("fragment") == true) tags.add("fragment")
            }
            "DATABASE" -> {
                if (exception.message?.contains("constraint") == true) tags.add("constraint")
                if (exception.message?.contains("foreign") == true) tags.add("foreign_key")
            }
        }
        
        return tags
    }
    
    /**
     * 获取堆栈跟踪字符串
     */
    private fun getStackTraceString(exception: Throwable): String {
        return try {
            val stringWriter = java.io.StringWriter()
            val printWriter = java.io.PrintWriter(stringWriter)
            exception.printStackTrace(printWriter)
            stringWriter.toString()
        } catch (e: Exception) {
            "Unable to get stack trace"
        }
    }
}

/**
 * 错误分类结果
 */
data class ErrorClassification(
    val category: String,
    val severity: ErrorSeverity,
    val isRecoverable: Boolean,
    val userImpact: UserImpact,
    val suggestions: List<String>,
    val tags: List<String>
)

/**
 * 错误严重性级别
 */
enum class ErrorSeverity {
    LOW,        // 低：不影响核心功能
    MEDIUM,     // 中：影响部分功能
    HIGH,       // 高：影响主要功能
    CRITICAL    // 致命：导致应用崩溃
}

/**
 * 用户影响级别
 */
enum class UserImpact {
    MINIMAL,    // 最小：用户几乎感觉不到
    MINOR,      // 轻微：用户可能注意到但不影响使用
    MODERATE,   // 中等：影响用户体验但可以继续使用
    MAJOR,      // 严重：严重影响用户体验
    SEVERE      // 极严重：用户无法正常使用
}
