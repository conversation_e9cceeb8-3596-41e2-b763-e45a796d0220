package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 标签领域模型
 * 表示用户创建的标签信息
 */
@Serializable
data class Tag(
    val id: String,
    val name: String,
    val color: String? = null,
    val description: String? = null,
    val usageCount: Int = 0,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 标签创建请求模型
 */
@Serializable
data class CreateTagRequest(
    val name: String,
    val color: String? = null,
    val description: String? = null
)

/**
 * 标签更新请求模型
 */
@Serializable
data class UpdateTagRequest(
    val name: String? = null,
    val color: String? = null,
    val description: String? = null
)

/**
 * 标签查询参数
 */
data class TagQuery(
    val searchQuery: String? = null,
    val popularOnly: Boolean = false,
    val limit: Int = 50,
    val offset: Int = 0
)

/**
 * 标签统计信息
 */
@Serializable
data class TagStatistics(
    val totalTags: Int,
    val totalUsage: Int,
    val mostUsedTag: Tag?,
    val recentTags: List<Tag>
)
