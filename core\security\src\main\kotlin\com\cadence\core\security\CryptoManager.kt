package com.cadence.core.security

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.nio.charset.StandardCharsets
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 加密管理器
 * 负责应用内所有数据的加密和解密操作
 */
@Singleton
class CryptoManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS = "CadenceSecretKey"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
    }
    
    private val keyStore: KeyStore by lazy {
        KeyStore.getInstance(ANDROID_KEYSTORE).apply {
            load(null)
        }
    }
    
    private val masterKey: MasterKey by lazy {
        MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
    }
    
    init {
        generateSecretKey()
    }
    
    /**
     * 生成密钥
     */
    private fun generateSecretKey() {
        try {
            if (!keyStore.containsAlias(KEY_ALIAS)) {
                val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
                val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                    KEY_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                )
                    .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                    .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                    .setRandomizedEncryptionRequired(true)
                    .setUserAuthenticationRequired(false)
                    .build()
                
                keyGenerator.init(keyGenParameterSpec)
                keyGenerator.generateKey()
                
                Timber.d("密钥生成成功")
            }
        } catch (e: Exception) {
            Timber.e(e, "密钥生成失败")
            throw SecurityException("无法生成加密密钥", e)
        }
    }
    
    /**
     * 获取密钥
     */
    private fun getSecretKey(): SecretKey {
        return keyStore.getKey(KEY_ALIAS, null) as SecretKey
    }
    
    /**
     * 加密字符串
     */
    suspend fun encrypt(plainText: String): EncryptedData = withContext(Dispatchers.Default) {
        try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey())
            
            val iv = cipher.iv
            val encryptedBytes = cipher.doFinal(plainText.toByteArray(StandardCharsets.UTF_8))
            
            EncryptedData(
                encryptedData = encryptedBytes,
                iv = iv
            )
        } catch (e: Exception) {
            Timber.e(e, "加密失败")
            throw SecurityException("数据加密失败", e)
        }
    }
    
    /**
     * 解密字符串
     */
    suspend fun decrypt(encryptedData: EncryptedData): String = withContext(Dispatchers.Default) {
        try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val gcmParameterSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, encryptedData.iv)
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(), gcmParameterSpec)
            
            val decryptedBytes = cipher.doFinal(encryptedData.encryptedData)
            String(decryptedBytes, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            Timber.e(e, "解密失败")
            throw SecurityException("数据解密失败", e)
        }
    }
    
    /**
     * 加密字节数组
     */
    suspend fun encryptBytes(data: ByteArray): EncryptedData = withContext(Dispatchers.Default) {
        try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey())
            
            val iv = cipher.iv
            val encryptedBytes = cipher.doFinal(data)
            
            EncryptedData(
                encryptedData = encryptedBytes,
                iv = iv
            )
        } catch (e: Exception) {
            Timber.e(e, "字节数组加密失败")
            throw SecurityException("字节数组加密失败", e)
        }
    }
    
    /**
     * 解密字节数组
     */
    suspend fun decryptBytes(encryptedData: EncryptedData): ByteArray = withContext(Dispatchers.Default) {
        try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val gcmParameterSpec = GCMParameterSpec(GCM_TAG_LENGTH * 8, encryptedData.iv)
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(), gcmParameterSpec)
            
            cipher.doFinal(encryptedData.encryptedData)
        } catch (e: Exception) {
            Timber.e(e, "字节数组解密失败")
            throw SecurityException("字节数组解密失败", e)
        }
    }
    
    /**
     * 创建加密的SharedPreferences
     */
    fun createEncryptedSharedPreferences(fileName: String): android.content.SharedPreferences {
        return try {
            EncryptedSharedPreferences.create(
                context,
                fileName,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            )
        } catch (e: Exception) {
            Timber.e(e, "创建加密SharedPreferences失败")
            throw SecurityException("无法创建加密存储", e)
        }
    }
    
    /**
     * 生成随机盐值
     */
    fun generateSalt(): ByteArray {
        val salt = ByteArray(32)
        java.security.SecureRandom().nextBytes(salt)
        return salt
    }
    
    /**
     * 计算数据哈希值
     */
    suspend fun calculateHash(data: String): String = withContext(Dispatchers.Default) {
        try {
            val digest = java.security.MessageDigest.getInstance("SHA-256")
            val hashBytes = digest.digest(data.toByteArray(StandardCharsets.UTF_8))
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            Timber.e(e, "哈希计算失败")
            throw SecurityException("哈希计算失败", e)
        }
    }
    
    /**
     * 验证数据完整性
     */
    suspend fun verifyIntegrity(data: String, expectedHash: String): Boolean = withContext(Dispatchers.Default) {
        try {
            val actualHash = calculateHash(data)
            actualHash == expectedHash
        } catch (e: Exception) {
            Timber.e(e, "完整性验证失败")
            false
        }
    }
    
    /**
     * 清理密钥（用于安全退出）
     */
    fun clearKeys() {
        try {
            if (keyStore.containsAlias(KEY_ALIAS)) {
                keyStore.deleteEntry(KEY_ALIAS)
                Timber.d("密钥已清理")
            }
        } catch (e: Exception) {
            Timber.e(e, "密钥清理失败")
        }
    }
    
    /**
     * 检查加密功能是否可用
     */
    fun isEncryptionAvailable(): Boolean {
        return try {
            keyStore.containsAlias(KEY_ALIAS) || run {
                generateSecretKey()
                true
            }
        } catch (e: Exception) {
            Timber.e(e, "加密功能不可用")
            false
        }
    }
}

/**
 * 加密数据容器
 */
data class EncryptedData(
    val encryptedData: ByteArray,
    val iv: ByteArray
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EncryptedData

        if (!encryptedData.contentEquals(other.encryptedData)) return false
        if (!iv.contentEquals(other.iv)) return false

        return true
    }

    override fun hashCode(): Int {
        var result = encryptedData.contentHashCode()
        result = 31 * result + iv.contentHashCode()
        return result
    }
}
