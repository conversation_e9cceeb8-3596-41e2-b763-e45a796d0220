package com.cadence.core.database.dao.culture

import androidx.room.*
import com.cadence.core.database.entity.culture.CulturalLearningProgressEntity
import kotlinx.coroutines.flow.Flow

/**
 * 文化学习进度DAO
 */
@Dao
interface CulturalLearningProgressDao {
    
    // 基础CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProgress(progress: CulturalLearningProgressEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProgresses(progresses: List<CulturalLearningProgressEntity>)
    
    @Update
    suspend fun updateProgress(progress: CulturalLearningProgressEntity)
    
    @Delete
    suspend fun deleteProgress(progress: CulturalLearningProgressEntity)
    
    @Query("DELETE FROM cultural_learning_progress WHERE user_id = :userId AND context_id = :contextId")
    suspend fun deleteProgressByUserAndContext(userId: String, contextId: String)
    
    // 查询操作
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId AND context_id = :contextId")
    suspend fun getProgressByUserAndContext(userId: String, contextId: String): CulturalLearningProgressEntity?
    
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId ORDER BY last_accessed_at DESC")
    suspend fun getProgressByUser(userId: String): List<CulturalLearningProgressEntity>
    
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId AND is_learned = :isLearned")
    suspend fun getProgressByUserAndLearned(userId: String, isLearned: Boolean): List<CulturalLearningProgressEntity>
    
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId AND bookmarked = 1")
    suspend fun getBookmarkedProgressByUser(userId: String): List<CulturalLearningProgressEntity>
    
    @Query("""
        SELECT * FROM cultural_learning_progress 
        WHERE user_id = :userId 
        AND difficulty = :difficulty 
        ORDER BY last_accessed_at DESC
    """)
    suspend fun getProgressByUserAndDifficulty(userId: String, difficulty: String): List<CulturalLearningProgressEntity>
    
    @Query("""
        SELECT * FROM cultural_learning_progress 
        WHERE user_id = :userId 
        ORDER BY last_accessed_at DESC 
        LIMIT :limit OFFSET :offset
    """)
    suspend fun getProgressByUserPaged(userId: String, limit: Int, offset: Int): List<CulturalLearningProgressEntity>
    
    @Query("""
        SELECT * FROM cultural_learning_progress 
        WHERE user_id = :userId 
        AND last_accessed_at >= :startTime 
        AND last_accessed_at <= :endTime 
        ORDER BY last_accessed_at DESC
    """)
    suspend fun getProgressByUserAndTimeRange(
        userId: String,
        startTime: Long,
        endTime: Long
    ): List<CulturalLearningProgressEntity>
    
    // Flow查询
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId ORDER BY last_accessed_at DESC")
    fun getProgressByUserFlow(userId: String): Flow<List<CulturalLearningProgressEntity>>
    
    @Query("SELECT * FROM cultural_learning_progress WHERE user_id = :userId AND bookmarked = 1")
    fun getBookmarkedProgressByUserFlow(userId: String): Flow<List<CulturalLearningProgressEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM cultural_learning_progress WHERE user_id = :userId")
    suspend fun getProgressCountByUser(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM cultural_learning_progress WHERE user_id = :userId AND is_learned = 1")
    suspend fun getLearnedCountByUser(userId: String): Int
    
    @Query("SELECT COUNT(*) FROM cultural_learning_progress WHERE user_id = :userId AND bookmarked = 1")
    suspend fun getBookmarkedCountByUser(userId: String): Int
    
    @Query("""
        SELECT difficulty, COUNT(*) as count 
        FROM cultural_learning_progress 
        WHERE user_id = :userId 
        GROUP BY difficulty 
        ORDER BY count DESC
    """)
    suspend fun getProgressCountByDifficulties(userId: String): List<DifficultyProgressCount>
    
    @Query("""
        SELECT AVG(access_count) 
        FROM cultural_learning_progress 
        WHERE user_id = :userId
    """)
    suspend fun getAverageAccessCount(userId: String): Double
    
    // 更新操作
    @Query("""
        UPDATE cultural_learning_progress 
        SET last_accessed_at = :accessTime, access_count = access_count + 1 
        WHERE user_id = :userId AND context_id = :contextId
    """)
    suspend fun updateLastAccessed(userId: String, contextId: String, accessTime: Long)
    
    @Query("""
        UPDATE cultural_learning_progress 
        SET is_learned = :isLearned 
        WHERE user_id = :userId AND context_id = :contextId
    """)
    suspend fun updateLearned(userId: String, contextId: String, isLearned: Boolean)
    
    @Query("""
        UPDATE cultural_learning_progress 
        SET bookmarked = :bookmarked 
        WHERE user_id = :userId AND context_id = :contextId
    """)
    suspend fun updateBookmarked(userId: String, contextId: String, bookmarked: Boolean)
    
    @Query("""
        UPDATE cultural_learning_progress 
        SET notes = :notes 
        WHERE user_id = :userId AND context_id = :contextId
    """)
    suspend fun updateNotes(userId: String, contextId: String, notes: String)
}

/**
 * 难度进度统计数据
 */
data class DifficultyProgressCount(
    val difficulty: String,
    val count: Int
)