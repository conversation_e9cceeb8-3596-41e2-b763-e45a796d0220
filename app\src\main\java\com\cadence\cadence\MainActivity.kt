package com.cadence.cadence

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.lifecycleScope
import com.cadence.cadence.navigation.CadenceNavigation
import com.cadence.cadence.ui.theme.CadenceTheme
import com.cadence.core.performance.StartupTimeTracker
import com.cadence.core.performance.LazyInitializationManager
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Cadence应用程序主Activity
 *
 * 负责：
 * - 设置应用主题和UI
 * - 配置导航系统
 * - 处理系统UI（状态栏、导航栏）
 * - 启动画面管理
 * - 启动性能监控
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var startupTimeTracker: StartupTimeTracker

    @Inject
    lateinit var lazyInitializationManager: LazyInitializationManager

    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装启动画面
        val splashScreen = installSplashScreen()

        // 配置启动画面保持条件
        splashScreen.setKeepOnScreenCondition {
            // 可以根据初始化状态决定是否保持启动画面
            false
        }

        super.onCreate(savedInstanceState)

        // 启用边到边显示
        enableEdgeToEdge()

        // 记录MainActivity创建完成
        startupTimeTracker.recordMainActivityCreated()

        Timber.d("MainActivity创建完成")

        setContent {
            CadenceTheme {
                CadenceApp()
            }
        }

        // 启动延迟初始化
        lifecycleScope.launch {
            lazyInitializationManager.markApplicationReady()
        }
    }

    override fun onResume() {
        super.onResume()

        // 记录首帧渲染完成（近似）
        startupTimeTracker.recordFirstFrameRendered()
    }
}

/**
 * Cadence应用程序主Composable
 */
@Composable
private fun CadenceApp() {
    val systemUiController = rememberSystemUiController()

    // 配置系统UI颜色
    systemUiController.setSystemBarsColor(
        color = MaterialTheme.colorScheme.background,
        darkIcons = !isSystemInDarkTheme()
    )

    // 记录首帧渲染（更精确的时机）
    LaunchedEffect(Unit) {
        // 这里可以添加更精确的首帧渲染时间记录
        Timber.d("Compose UI首次渲染完成")
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        CadenceNavigation()
    }
}

/**
 * 检查系统是否处于深色主题模式
 */
@Composable
private fun isSystemInDarkTheme(): Boolean {
    return androidx.compose.foundation.isSystemInDarkTheme()
}