package com.cadence.core.network.interceptor

import com.cadence.core.network.monitor.NetworkPerformanceMonitor
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton
import javax.net.ssl.SSLException

/**
 * 增强的网络错误处理拦截器
 * 统一处理网络请求错误、重试逻辑和性能监控
 */
@Singleton
class ErrorHandlingInterceptor @Inject constructor(
    private val performanceMonitor: NetworkPerformanceMonitor
) : Interceptor {
    
    companion object {
        private const val MAX_RETRY_COUNT = 3
        private const val RETRY_DELAY_MS = 1000L
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url.toString()
        val method = request.method

        // 开始性能监控
        val tracker = performanceMonitor.recordRequestStart(url, method)

        var response: Response? = null
        var lastException: IOException? = null

        // 重试逻辑
        repeat(MAX_RETRY_COUNT) { attempt ->
            try {
                response?.close() // 关闭之前的响应
                response = chain.proceed(request)

                // 检查响应状态
                if (response!!.isSuccessful) {
                    // 记录成功
                    tracker.markSuccess(response!!.code, response!!.body?.contentLength())
                    return response!!
                } else {
                    val errorInfo = handleHttpError(response!!, attempt)

                    // 如果是最后一次尝试或不应该重试，记录失败并返回
                    if (attempt == MAX_RETRY_COUNT - 1 || !shouldRetryHttpError(response!!.code)) {
                        tracker.markFailure(response!!.code, errorInfo.message)
                        return response!!
                    }

                    // 等待后重试
                    waitBeforeRetry(attempt, errorInfo.retryDelay)
                }

            } catch (e: IOException) {
                lastException = e
                val errorInfo = handleNetworkError(e, attempt)

                Timber.w(e, "网络请求失败，尝试次数: ${attempt + 1}, 错误类型: ${errorInfo.type}")

                // 最后一次尝试，记录失败并抛出异常
                if (attempt == MAX_RETRY_COUNT - 1) {
                    tracker.markFailure(null, errorInfo.message)
                    throw NetworkException(errorInfo.message, e, errorInfo.type)
                }

                // 判断是否需要重试
                if (!shouldRetryNetworkError(e)) {
                    tracker.markFailure(null, errorInfo.message)
                    throw NetworkException(errorInfo.message, e, errorInfo.type)
                }

                // 等待后重试
                waitBeforeRetry(attempt, errorInfo.retryDelay)
            }
        }

        // 如果到这里，说明所有重试都失败了
        val finalException = lastException ?: IOException("未知网络错误")
        tracker.markFailure(null, finalException.message)
        throw finalException
    }
    
    /**
     * 处理HTTP错误响应
     */
    private fun handleHttpError(response: Response, attempt: Int): ErrorInfo {
        return when (response.code) {
            400 -> {
                val message = "请求参数错误: ${response.message}"
                Timber.e(message)
                ErrorInfo(
                    type = NetworkErrorType.CLIENT_ERROR,
                    message = message,
                    retryDelay = 0L // 客户端错误不重试
                )
            }
            401 -> {
                val message = "API密钥无效或已过期"
                Timber.e(message)
                ErrorInfo(
                    type = NetworkErrorType.AUTHENTICATION_ERROR,
                    message = message,
                    retryDelay = 0L
                )
            }
            403 -> {
                val message = "API访问被拒绝，请检查权限"
                Timber.e(message)
                ErrorInfo(
                    type = NetworkErrorType.AUTHORIZATION_ERROR,
                    message = message,
                    retryDelay = 0L
                )
            }
            404 -> {
                val message = "API端点不存在: ${response.request.url}"
                Timber.e(message)
                ErrorInfo(
                    type = NetworkErrorType.NOT_FOUND_ERROR,
                    message = message,
                    retryDelay = 0L
                )
            }
            429 -> {
                val message = "API请求频率限制，尝试次数: ${attempt + 1}"
                Timber.w(message)
                ErrorInfo(
                    type = NetworkErrorType.RATE_LIMIT_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * 2 * (attempt + 1) // 指数退避
                )
            }
            in 500..599 -> {
                val message = "服务器错误: ${response.code}, 尝试次数: ${attempt + 1}"
                Timber.w(message)
                ErrorInfo(
                    type = NetworkErrorType.SERVER_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * (attempt + 1)
                )
            }
            else -> {
                val message = "未知HTTP错误: ${response.code} - ${response.message}"
                Timber.e(message)
                ErrorInfo(
                    type = NetworkErrorType.UNKNOWN_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * (attempt + 1)
                )
            }
        }
    }

    /**
     * 处理网络错误
     */
    private fun handleNetworkError(exception: IOException, attempt: Int): ErrorInfo {
        return when (exception) {
            is SocketTimeoutException -> {
                val message = "请求超时，尝试次数: ${attempt + 1}"
                ErrorInfo(
                    type = NetworkErrorType.TIMEOUT_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * (attempt + 1)
                )
            }
            is UnknownHostException -> {
                val message = "网络不可达或DNS解析失败"
                ErrorInfo(
                    type = NetworkErrorType.NETWORK_UNAVAILABLE,
                    message = message,
                    retryDelay = 0L // 网络不可达不重试
                )
            }
            is ConnectException -> {
                val message = "连接失败，服务器可能不可用"
                ErrorInfo(
                    type = NetworkErrorType.CONNECTION_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * (attempt + 1)
                )
            }
            is SSLException -> {
                val message = "SSL/TLS连接错误"
                ErrorInfo(
                    type = NetworkErrorType.SSL_ERROR,
                    message = message,
                    retryDelay = 0L // SSL错误通常不重试
                )
            }
            else -> {
                val message = "网络IO错误: ${exception.message}"
                ErrorInfo(
                    type = NetworkErrorType.IO_ERROR,
                    message = message,
                    retryDelay = RETRY_DELAY_MS * (attempt + 1)
                )
            }
        }
    }
    
    /**
     * 判断HTTP错误是否应该重试
     */
    private fun shouldRetryHttpError(code: Int): Boolean {
        return when (code) {
            in 400..499 -> false // 客户端错误不重试（除了429）
            429 -> true // 频率限制重试
            in 500..599 -> true // 服务器错误重试
            else -> false
        }
    }

    /**
     * 判断网络错误是否应该重试
     */
    private fun shouldRetryNetworkError(exception: IOException): Boolean {
        return when (exception) {
            is SocketTimeoutException -> true // 超时重试
            is UnknownHostException -> false // 网络不可达，不重试
            is ConnectException -> true // 连接错误重试
            is SSLException -> false // SSL错误不重试
            else -> true // 其他IO异常重试
        }
    }

    /**
     * 等待后重试
     */
    private fun waitBeforeRetry(attempt: Int, customDelay: Long = 0L) {
        val delay = if (customDelay > 0) customDelay else RETRY_DELAY_MS * (attempt + 1)
        try {
            Thread.sleep(delay)
        } catch (ie: InterruptedException) {
            Thread.currentThread().interrupt()
            throw IOException("请求被中断", ie)
        }
    }
}

/**
 * 错误信息数据类
 */
data class ErrorInfo(
    val type: NetworkErrorType,
    val message: String,
    val retryDelay: Long = 0L
)

/**
 * 网络错误类型枚举
 */
enum class NetworkErrorType {
    TIMEOUT_ERROR,          // 超时错误
    NETWORK_UNAVAILABLE,    // 网络不可用
    CONNECTION_ERROR,       // 连接错误
    SSL_ERROR,              // SSL错误
    IO_ERROR,               // IO错误
    CLIENT_ERROR,           // 客户端错误（4xx）
    SERVER_ERROR,           // 服务器错误（5xx）
    AUTHENTICATION_ERROR,   // 认证错误（401）
    AUTHORIZATION_ERROR,    // 授权错误（403）
    NOT_FOUND_ERROR,        // 资源不存在（404）
    RATE_LIMIT_ERROR,       // 频率限制（429）
    UNKNOWN_ERROR           // 未知错误
}

/**
 * 自定义网络异常
 */
class NetworkException(
    message: String,
    cause: Throwable? = null,
    val errorType: NetworkErrorType = NetworkErrorType.UNKNOWN_ERROR
) : IOException(message, cause) {

    /**
     * 获取用户友好的错误信息
     */
    fun getUserFriendlyMessage(): String {
        return when (errorType) {
            NetworkErrorType.TIMEOUT_ERROR -> "请求超时，请检查网络连接"
            NetworkErrorType.NETWORK_UNAVAILABLE -> "网络不可用，请检查网络设置"
            NetworkErrorType.CONNECTION_ERROR -> "连接失败，请稍后重试"
            NetworkErrorType.SSL_ERROR -> "安全连接错误，请检查网络设置"
            NetworkErrorType.AUTHENTICATION_ERROR -> "身份验证失败，请检查API密钥"
            NetworkErrorType.AUTHORIZATION_ERROR -> "访问权限不足"
            NetworkErrorType.NOT_FOUND_ERROR -> "请求的资源不存在"
            NetworkErrorType.RATE_LIMIT_ERROR -> "请求过于频繁，请稍后重试"
            NetworkErrorType.SERVER_ERROR -> "服务器错误，请稍后重试"
            NetworkErrorType.CLIENT_ERROR -> "请求参数错误"
            NetworkErrorType.IO_ERROR -> "网络IO错误，请检查网络连接"
            NetworkErrorType.UNKNOWN_ERROR -> "未知网络错误，请稍后重试"
        }
    }

    /**
     * 判断是否可以重试
     */
    fun isRetryable(): Boolean {
        return when (errorType) {
            NetworkErrorType.TIMEOUT_ERROR,
            NetworkErrorType.CONNECTION_ERROR,
            NetworkErrorType.RATE_LIMIT_ERROR,
            NetworkErrorType.SERVER_ERROR,
            NetworkErrorType.IO_ERROR -> true
            else -> false
        }
    }
}
