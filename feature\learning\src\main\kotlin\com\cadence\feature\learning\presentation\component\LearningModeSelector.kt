package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.cadence.feature.learning.presentation.viewmodel.LearningMode

/**
 * 学习模式选择器组件
 * 允许用户选择不同的学习模式
 */
@Composable
fun LearningModeSelector(
    onModeSelected: (LearningMode) -> Unit,
    modifier: Modifier = Modifier,
    selectedMode: LearningMode = LearningMode.NEW_WORDS
) {
    var currentMode by remember { mutableStateOf(selectedMode) }
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "学习模式",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectableGroup(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                LearningMode.values().forEach { mode ->
                    LearningModeChip(
                        mode = mode,
                        isSelected = currentMode == mode,
                        onClick = {
                            currentMode = mode
                            onModeSelected(mode)
                        },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 学习模式芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun LearningModeChip(
    mode: LearningMode,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        selected = isSelected,
        onClick = onClick,
        label = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.padding(vertical = 4.dp)
            ) {
                Icon(
                    imageVector = getModeIcon(mode),
                    contentDescription = mode.displayName,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = mode.displayName,
                    style = MaterialTheme.typography.labelSmall
                )
            }
        },
        modifier = modifier,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = MaterialTheme.colorScheme.onPrimary
        )
    )
}

/**
 * 获取学习模式对应的图标
 */
private fun getModeIcon(mode: LearningMode): ImageVector {
    return when (mode) {
        LearningMode.NEW_WORDS -> Icons.Default.FiberNew
        LearningMode.REVIEW -> Icons.Default.Refresh
        LearningMode.PRACTICE -> Icons.Default.Quiz
        LearningMode.BOOKMARKED -> Icons.Default.Bookmark
    }
}