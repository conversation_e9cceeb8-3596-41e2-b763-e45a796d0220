package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.DailyProgress
import java.text.SimpleDateFormat
import java.util.*

/**
 * 学习进度图表组件
 * 显示每日学习进度的趋势图
 */
@Composable
fun ProgressChart(
    dailyProgress: List<DailyProgress>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "每日学习进度",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Icon(
                    imageVector = Icons.Default.TrendingUp,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (dailyProgress.isEmpty()) {
                // 空状态
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "暂无学习数据",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                // 图表
                ProgressLineChart(
                    data = dailyProgress,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // 图例
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    ChartLegendItem(
                        color = MaterialTheme.colorScheme.primary,
                        label = "学习单词数",
                        modifier = Modifier.weight(1f)
                    )
                    ChartLegendItem(
                        color = MaterialTheme.colorScheme.secondary,
                        label = "正确率",
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * 进度折线图
 */
@Composable
private fun ProgressLineChart(
    data: List<DailyProgress>,
    modifier: Modifier = Modifier
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val secondaryColor = MaterialTheme.colorScheme.secondary
    
    Canvas(modifier = modifier) {
        if (data.isEmpty()) return@Canvas
        
        val width = size.width
        val height = size.height
        val padding = 20.dp.toPx()
        
        val chartWidth = width - 2 * padding
        val chartHeight = height - 2 * padding
        
        // 计算数据范围
        val maxWordsStudied = data.maxOfOrNull { it.wordsStudied } ?: 1
        val maxAccuracy = 100f // 正确率最大值为100%
        
        // 绘制学习单词数折线
        if (data.size > 1) {
            val wordsPath = Path()
            data.forEachIndexed { index, progress ->
                val x = padding + (index.toFloat() / (data.size - 1)) * chartWidth
                val y = padding + chartHeight - (progress.wordsStudied.toFloat() / maxWordsStudied) * chartHeight
                
                if (index == 0) {
                    wordsPath.moveTo(x, y)
                } else {
                    wordsPath.lineTo(x, y)
                }
            }
            
            drawPath(
                path = wordsPath,
                color = primaryColor,
                style = Stroke(width = 3.dp.toPx())
            )
        }
        
        // 绘制正确率折线
        if (data.size > 1) {
            val accuracyPath = Path()
            data.forEachIndexed { index, progress ->
                val x = padding + (index.toFloat() / (data.size - 1)) * chartWidth
                val y = padding + chartHeight - (progress.accuracy / maxAccuracy) * chartHeight
                
                if (index == 0) {
                    accuracyPath.moveTo(x, y)
                } else {
                    accuracyPath.lineTo(x, y)
                }
            }
            
            drawPath(
                path = accuracyPath,
                color = secondaryColor,
                style = Stroke(width = 3.dp.toPx())
            )
        }
        
        // 绘制数据点
        data.forEachIndexed { index, progress ->
            val x = padding + (index.toFloat() / (data.size - 1)) * chartWidth
            
            // 学习单词数点
            val wordsY = padding + chartHeight - (progress.wordsStudied.toFloat() / maxWordsStudied) * chartHeight
            drawCircle(
                color = primaryColor,
                radius = 4.dp.toPx(),
                center = Offset(x, wordsY)
            )
            
            // 正确率点
            val accuracyY = padding + chartHeight - (progress.accuracy / maxAccuracy) * chartHeight
            drawCircle(
                color = secondaryColor,
                radius = 4.dp.toPx(),
                center = Offset(x, accuracyY)
            )
        }
    }
}

/**
 * 图例项
 */
@Composable
private fun ChartLegendItem(
    color: Color,
    label: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, RoundedCornerShape(6.dp))
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 格式化日期
 */
private fun formatDate(timestamp: Long): String {
    val formatter = SimpleDateFormat("MM/dd", Locale.getDefault())
    return formatter.format(Date(timestamp))
}