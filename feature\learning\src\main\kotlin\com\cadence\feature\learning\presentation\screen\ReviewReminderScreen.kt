package com.cadence.feature.learning.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.learning.presentation.viewmodel.ReviewReminderViewModel
import com.cadence.feature.learning.presentation.component.ReviewWordCard
import com.cadence.feature.learning.presentation.component.ReminderSettingsCard
import java.text.SimpleDateFormat
import java.util.*

/**
 * 复习提醒界面
 * 显示待复习单词和提醒设置
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReviewReminderScreen(
    onNavigateBack: () -> Unit,
    onNavigateToStudy: (List<String>) -> Unit,
    onNavigateToWordDetail: (String) -> Unit,
    viewModel: ReviewReminderViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val wordsToReview by viewModel.wordsToReview.collectAsStateWithLifecycle()
    val reminderSettings by viewModel.reminderSettings.collectAsStateWithLifecycle()
    val upcomingReviews by viewModel.upcomingReviews.collectAsStateWithLifecycle()
    
    LaunchedEffect(Unit) {
        viewModel.loadReviewData()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("复习提醒") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = { viewModel.refreshReviewData() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新"
                    )
                }
            }
        )
        
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 待复习单词概览
                item {
                    ReviewOverviewCard(
                        totalReviews = wordsToReview.size,
                        onStartReview = {
                            if (wordsToReview.isNotEmpty()) {
                                onNavigateToStudy(wordsToReview.map { it.wordId })
                            }
                        }
                    )
                }
                
                // 提醒设置
                item {
                    ReminderSettingsCard(
                        settings = reminderSettings,
                        onSettingsChange = { newSettings ->
                            viewModel.updateReminderSettings(newSettings)
                        }
                    )
                }
                
                // 今日待复习单词
                if (wordsToReview.isNotEmpty()) {
                    item {
                        Text(
                            text = "今日待复习 (${wordsToReview.size})",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    items(wordsToReview.take(10)) { progress -> // 最多显示10个
                        ReviewWordCard(
                            progress = progress,
                            onWordClick = { onNavigateToWordDetail(progress.wordId) },
                            onStartReview = { 
                                onNavigateToStudy(listOf(progress.wordId))
                            }
                        )
                    }
                    
                    if (wordsToReview.size > 10) {
                        item {
                            Card(
                                onClick = { 
                                    onNavigateToStudy(wordsToReview.map { it.wordId })
                                },
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.primaryContainer
                                )
                            ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "查看全部 ${wordsToReview.size} 个待复习单词",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.onPrimaryContainer
                                    )
                                }
                            }
                        }
                    }
                }
                
                // 即将到期的复习
                if (upcomingReviews.isNotEmpty()) {
                    item {
                        Text(
                            text = "即将到期",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    items(upcomingReviews.take(5)) { progress ->
                        UpcomingReviewCard(
                            progress = progress,
                            onWordClick = { onNavigateToWordDetail(progress.wordId) }
                        )
                    }
                }
                
                // 空状态
                if (wordsToReview.isEmpty() && upcomingReviews.isEmpty()) {
                    item {
                        EmptyReviewState()
                    }
                }
            }
        }
        
        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误提示
            }
        }
    }
}

/**
 * 复习概览卡片
 */
@Composable
private fun ReviewOverviewCard(
    totalReviews: Int,
    onStartReview: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (totalReviews > 0) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = if (totalReviews > 0) Icons.Default.Schedule else Icons.Default.CheckCircle,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = if (totalReviews > 0) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Text(
                text = if (totalReviews > 0) "$totalReviews 个单词待复习" else "今日复习已完成",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = if (totalReviews > 0) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
            
            if (totalReviews > 0) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = onStartReview,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("开始复习")
                }
            }
        }
    }
}

/**
 * 即将到期复习卡片
 */
@Composable
private fun UpcomingReviewCard(
    progress: com.cadence.domain.model.learning.LearningProgress,
    onWordClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onWordClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.AccessTime,
                contentDescription = null,
                modifier = Modifier.size(24.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "单词 ${progress.wordId.take(8)}...",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium
                )
                
                Text(
                    text = "掌握程度: ${progress.masteryLevel.displayName}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Column(
                horizontalAlignment = Alignment.End
            ) {
                progress.nextReviewAt?.let { nextReview ->
                    val timeUntilReview = nextReview - System.currentTimeMillis()
                    val hoursUntilReview = timeUntilReview / (1000 * 60 * 60)
                    
                    Text(
                        text = when {
                            hoursUntilReview <= 0 -> "现在"
                            hoursUntilReview < 24 -> "${hoursUntilReview}小时后"
                            else -> "${hoursUntilReview / 24}天后"
                        },
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * 空状态组件
 */
@Composable
private fun EmptyReviewState(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "太棒了！",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "目前没有需要复习的单词",
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}