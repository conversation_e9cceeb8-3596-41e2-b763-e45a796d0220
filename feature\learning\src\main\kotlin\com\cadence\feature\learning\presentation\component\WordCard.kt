package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.Word
import com.cadence.domain.model.learning.LearningProgress
import com.cadence.domain.model.learning.MasteryLevel

/**
 * 单词卡片组件
 * 显示单词信息和学习进度
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WordCard(
    word: Word,
    progress: LearningProgress?,
    onClick: () -> Unit,
    onBookmarkToggle: () -> Unit,
    onStudy: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 顶部行：单词和操作按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    // 单词文本
                    Text(
                        text = word.text,
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    // 发音（如果有）
                    word.pronunciation?.let { pronunciation ->
                        Text(
                            text = "[$pronunciation]",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Row {
                    // 收藏按钮
                    IconButton(onClick = onBookmarkToggle) {
                        Icon(
                            imageVector = if (progress?.isBookmarked == true) {
                                Icons.Default.Bookmark
                            } else {
                                Icons.Default.BookmarkBorder
                            },
                            contentDescription = "收藏",
                            tint = if (progress?.isBookmarked == true) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )
                    }
                    
                    // 学习按钮
                    IconButton(onClick = onStudy) {
                        Icon(
                            imageVector = Icons.Default.School,
                            contentDescription = "学习",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 翻译
            Text(
                text = word.translation,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // 定义
            Text(
                text = word.definition,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            // 例句（如果有）
            word.example?.let { example ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "例句：$example",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 底部信息行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 难度和分类标签
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // 难度标签
                    AssistChip(
                        onClick = { },
                        label = {
                            Text(
                                text = word.difficulty.displayName,
                                style = MaterialTheme.typography.labelSmall
                            )
                        },
                        colors = AssistChipDefaults.assistChipColors(
                            containerColor = getDifficultyColor(word.difficulty),
                            labelColor = MaterialTheme.colorScheme.onSurface
                        )
                    )
                    
                    // 分类标签
                    AssistChip(
                        onClick = { },
                        label = {
                            Text(
                                text = word.category.displayName,
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    )
                }
                
                // 学习进度
                progress?.let { prog ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        // 掌握程度图标
                        Icon(
                            imageVector = getMasteryIcon(prog.masteryLevel),
                            contentDescription = prog.masteryLevel.displayName,
                            modifier = Modifier.size(16.dp),
                            tint = getMasteryColor(prog.masteryLevel)
                        )
                        
                        // 正确率
                        Text(
                            text = "${(prog.accuracyRate * 100).toInt()}%",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun getDifficultyColor(difficulty: com.cadence.domain.model.learning.WordDifficulty): androidx.compose.ui.graphics.Color {
    return when (difficulty) {
        com.cadence.domain.model.learning.WordDifficulty.EASY -> MaterialTheme.colorScheme.primaryContainer
        com.cadence.domain.model.learning.WordDifficulty.MEDIUM -> MaterialTheme.colorScheme.secondaryContainer
        com.cadence.domain.model.learning.WordDifficulty.HARD -> MaterialTheme.colorScheme.tertiaryContainer
        com.cadence.domain.model.learning.WordDifficulty.EXPERT -> MaterialTheme.colorScheme.errorContainer
    }
}

@Composable
private fun getMasteryIcon(masteryLevel: MasteryLevel): androidx.compose.ui.graphics.vector.ImageVector {
    return when (masteryLevel) {
        MasteryLevel.NEW -> Icons.Default.FiberNew
        MasteryLevel.LEARNING -> Icons.Default.School
        MasteryLevel.FAMILIAR -> Icons.Default.ThumbUp
        MasteryLevel.KNOWN -> Icons.Default.CheckCircle
        MasteryLevel.MASTERED -> Icons.Default.Star
    }
}

@Composable
private fun getMasteryColor(masteryLevel: MasteryLevel): androidx.compose.ui.graphics.Color {
    return when (masteryLevel) {
        MasteryLevel.NEW -> MaterialTheme.colorScheme.outline
        MasteryLevel.LEARNING -> MaterialTheme.colorScheme.primary
        MasteryLevel.FAMILIAR -> MaterialTheme.colorScheme.secondary
        MasteryLevel.KNOWN -> MaterialTheme.colorScheme.tertiary
        MasteryLevel.MASTERED -> MaterialTheme.colorScheme.primary
    }
}