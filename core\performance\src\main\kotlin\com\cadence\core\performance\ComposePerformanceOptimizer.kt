package com.cadence.core.performance

import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.Snapshot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Density
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Compose性能优化器
 * 负责优化Jetpack Compose的渲染性能，减少不必要的重组
 */
@Singleton
class ComposePerformanceOptimizer @Inject constructor() {
    
    private val scope = CoroutineScope(Dispatchers.Main)
    
    // 重组监控
    private val recompositionMetrics = ConcurrentHashMap<String, RecompositionMetrics>()
    private val _recompositionState = MutableStateFlow(RecompositionState())
    val recompositionState: StateFlow<RecompositionState> = _recompositionState.asStateFlow()
    
    // 性能优化配置
    private val optimizationConfig = ComposeOptimizationConfig(
        enableRecompositionTracking = true,
        enableStabilityChecking = true,
        enableLazyListOptimization = true,
        recompositionThreshold = 10
    )
    
    /**
     * 跟踪Composable的重组次数
     */
    @Composable
    fun TrackRecomposition(
        name: String,
        content: @Composable () -> Unit
    ) {
        if (!optimizationConfig.enableRecompositionTracking) {
            content()
            return
        }
        
        val recompositionCount = remember { mutableStateOf(0) }
        
        SideEffect {
            recompositionCount.value++
            recordRecomposition(name, recompositionCount.value)
        }
        
        content()
    }
    
    /**
     * 稳定性检查装饰器
     */
    @Composable
    fun <T> StabilityChecker(
        value: T,
        name: String,
        content: @Composable (T) -> Unit
    ) {
        if (!optimizationConfig.enableStabilityChecking) {
            content(value)
            return
        }
        
        val previousValue = remember { mutableStateOf(value) }
        
        LaunchedEffect(value) {
            if (previousValue.value != value) {
                checkStability(name, previousValue.value, value)
                previousValue.value = value
            }
        }
        
        content(value)
    }
    
    /**
     * 优化的记忆化状态
     */
    @Composable
    fun <T> rememberOptimized(
        key: Any? = null,
        calculation: () -> T
    ): T {
        return remember(key) {
            calculation()
        }
    }
    
    /**
     * 优化的派生状态
     */
    @Composable
    fun <T, R> derivedStateOptimized(
        value: T,
        transform: (T) -> R
    ): State<R> {
        return remember(value) {
            derivedStateOf { transform(value) }
        }
    }
    
    /**
     * 延迟状态更新（防抖）
     */
    @Composable
    fun <T> debouncedState(
        initialValue: T,
        delayMs: Long = 300
    ): MutableState<T> {
        val state = remember { mutableStateOf(initialValue) }
        val debouncedValue = remember { mutableStateOf(initialValue) }
        
        LaunchedEffect(state.value) {
            kotlinx.coroutines.delay(delayMs)
            debouncedValue.value = state.value
        }
        
        return object : MutableState<T> {
            override var value: T
                get() = debouncedValue.value
                set(value) {
                    state.value = value
                }
            
            override fun component1(): T = value
            override fun component2(): (T) -> Unit = { value = it }
        }
    }
    
    /**
     * 批量状态更新
     */
    fun batchStateUpdates(updates: () -> Unit) {
        Snapshot.withMutableSnapshot {
            updates()
        }
    }
    
    /**
     * 检查列表性能
     */
    @Composable
    fun <T> OptimizedLazyList(
        items: List<T>,
        key: ((item: T) -> Any)? = null,
        contentType: (item: T) -> Any? = { null },
        itemContent: @Composable (item: T) -> Unit
    ) {
        if (!optimizationConfig.enableLazyListOptimization) {
            items.forEach { item ->
                itemContent(item)
            }
            return
        }
        
        // 检查列表性能问题
        LaunchedEffect(items.size) {
            if (items.size > 1000) {
                Timber.w("大列表检测: ${items.size} 项，建议使用分页或虚拟化")
            }
        }
        
        // 检查key函数的稳定性
        if (key != null) {
            LaunchedEffect(items) {
                checkKeyStability(items, key)
            }
        }
        
        items.forEach { item ->
            key(key?.invoke(item))
            itemContent(item)
        }
    }
    
    /**
     * 性能分析装饰器
     */
    @Composable
    fun PerformanceProfiler(
        name: String,
        content: @Composable () -> Unit
    ) {
        val startTime = remember { System.nanoTime() }
        
        content()
        
        SideEffect {
            val endTime = System.nanoTime()
            val duration = (endTime - startTime) / 1_000_000 // 转换为毫秒
            
            if (duration > 16) { // 超过一帧的时间
                Timber.w("Compose性能警告: $name 渲染耗时 ${duration}ms")
            }
            
            recordRenderingTime(name, duration)
        }
    }
    
    /**
     * 内存优化的图片加载
     */
    @Composable
    fun OptimizedImageLoader(
        imageUrl: String,
        contentDescription: String? = null,
        placeholder: @Composable (() -> Unit)? = null,
        error: @Composable (() -> Unit)? = null
    ) {
        val density = LocalDensity.current
        val view = LocalView.current
        
        // 根据屏幕密度和视图大小优化图片加载
        val optimizedUrl = remember(imageUrl, density) {
            optimizeImageUrl(imageUrl, density, view.width, view.height)
        }
        
        // 这里可以集成具体的图片加载库（如Coil、Glide等）
        // 示例代码，实际实现需要根据使用的图片库调整
        Timber.d("加载优化图片: $optimizedUrl")
    }
    
    /**
     * 记录重组信息
     */
    private fun recordRecomposition(name: String, count: Int) {
        val metrics = recompositionMetrics.getOrPut(name) {
            RecompositionMetrics(name, 0, System.currentTimeMillis())
        }
        
        recompositionMetrics[name] = metrics.copy(
            count = count,
            lastRecompositionTime = System.currentTimeMillis()
        )
        
        // 检查重组频率
        if (count > optimizationConfig.recompositionThreshold) {
            Timber.w("高频重组检测: $name 已重组 $count 次")
            
            scope.launch {
                _recompositionState.value = _recompositionState.value.copy(
                    highFrequencyRecompositions = _recompositionState.value.highFrequencyRecompositions + name
                )
            }
        }
    }
    
    /**
     * 检查稳定性
     */
    private fun <T> checkStability(name: String, oldValue: T, newValue: T) {
        if (oldValue == newValue) return
        
        // 检查是否为不稳定的对象引用变化
        if (oldValue != null && newValue != null && 
            oldValue.javaClass == newValue.javaClass &&
            oldValue.toString() == newValue.toString()) {
            
            Timber.w("稳定性警告: $name 的值内容相同但对象引用不同，可能导致不必要的重组")
        }
    }
    
    /**
     * 检查Key函数的稳定性
     */
    private fun <T> checkKeyStability(items: List<T>, keyFunction: (T) -> Any) {
        val keys = items.map { keyFunction(it) }
        val uniqueKeys = keys.toSet()
        
        if (keys.size != uniqueKeys.size) {
            Timber.w("Key稳定性警告: 检测到重复的key，可能导致列表项错误复用")
        }
        
        // 检查key的类型稳定性
        val keyTypes = keys.map { it.javaClass }.toSet()
        if (keyTypes.size > 1) {
            Timber.w("Key类型警告: 检测到不同类型的key，建议使用统一类型")
        }
    }
    
    /**
     * 记录渲染时间
     */
    private fun recordRenderingTime(name: String, durationMs: Long) {
        scope.launch {
            val currentState = _recompositionState.value
            val renderingTimes = currentState.renderingTimes.toMutableMap()
            renderingTimes[name] = durationMs
            
            _recompositionState.value = currentState.copy(
                renderingTimes = renderingTimes,
                lastRenderingTime = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 优化图片URL
     */
    private fun optimizeImageUrl(url: String, density: Density, width: Int, height: Int): String {
        // 根据屏幕密度和尺寸优化图片URL
        // 这里是示例实现，实际需要根据图片服务的API调整
        val scaleFactor = density.density
        val optimizedWidth = (width * scaleFactor).toInt()
        val optimizedHeight = (height * scaleFactor).toInt()
        
        return if (url.contains("?")) {
            "$url&w=$optimizedWidth&h=$optimizedHeight&q=80"
        } else {
            "$url?w=$optimizedWidth&h=$optimizedHeight&q=80"
        }
    }
    
    /**
     * 获取性能报告
     */
    fun getPerformanceReport(): ComposePerformanceReport {
        val totalRecompositions = recompositionMetrics.values.sumOf { it.count }
        val averageRecompositions = if (recompositionMetrics.isNotEmpty()) {
            totalRecompositions.toFloat() / recompositionMetrics.size
        } else 0f
        
        val highFrequencyComponents = recompositionMetrics.values
            .filter { it.count > optimizationConfig.recompositionThreshold }
            .map { it.name }
        
        val averageRenderingTime = _recompositionState.value.renderingTimes.values
            .takeIf { it.isNotEmpty() }
            ?.average() ?: 0.0
        
        return ComposePerformanceReport(
            totalRecompositions = totalRecompositions,
            averageRecompositions = averageRecompositions,
            highFrequencyComponents = highFrequencyComponents,
            averageRenderingTime = averageRenderingTime,
            componentMetrics = recompositionMetrics.values.toList()
        )
    }
    
    /**
     * 清理过期数据
     */
    fun cleanup() {
        recompositionMetrics.clear()
        _recompositionState.value = RecompositionState()
        Timber.d("Compose性能优化器已清理")
    }
}

/**
 * 重组指标
 */
data class RecompositionMetrics(
    val name: String,
    val count: Int,
    val lastRecompositionTime: Long
)

/**
 * 重组状态
 */
data class RecompositionState(
    val highFrequencyRecompositions: List<String> = emptyList(),
    val renderingTimes: Map<String, Long> = emptyMap(),
    val lastRenderingTime: Long = 0
)

/**
 * Compose优化配置
 */
data class ComposeOptimizationConfig(
    val enableRecompositionTracking: Boolean,
    val enableStabilityChecking: Boolean,
    val enableLazyListOptimization: Boolean,
    val recompositionThreshold: Int
)

/**
 * Compose性能报告
 */
data class ComposePerformanceReport(
    val totalRecompositions: Int,
    val averageRecompositions: Float,
    val highFrequencyComponents: List<String>,
    val averageRenderingTime: Double,
    val componentMetrics: List<RecompositionMetrics>
)
