package com.cadence.feature.offline

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle

/**
 * 冲突解决界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConflictResolutionScreen(
    onNavigateBack: () -> Unit,
    viewModel: ConflictResolutionViewModel = hiltViewModel()
) {
    val conflicts by viewModel.conflicts.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val error by viewModel.error.collectAsStateWithLifecycle()
    
    var selectedConflict by remember { mutableStateOf<ConflictLog?>(null) }
    
    // 显示错误消息
    LaunchedEffect(error) {
        error?.let {
            // TODO: 显示错误Snackbar
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("冲突解决") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { viewModel.refresh() }) {
                    Icon(Icons.Default.Refresh, contentDescription = "刷新")
                }
            }
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (conflicts.isEmpty()) {
            // 无冲突状态
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "没有待解决的冲突",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "所有数据同步正常",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            // 冲突列表
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(conflicts) { conflict ->
                    ConflictItem(
                        conflict = conflict,
                        onResolve = { resolution ->
                            viewModel.resolveConflict(conflict.id, resolution)
                        },
                        onViewDetails = { selectedConflict = conflict }
                    )
                }
            }
        }
    }
    
    // 冲突详情对话框
    selectedConflict?.let { conflict ->
        ConflictDetailsDialog(
            conflict = conflict,
            onDismiss = { selectedConflict = null },
            onResolve = { resolution ->
                viewModel.resolveConflict(conflict.id, resolution)
                selectedConflict = null
            }
        )
    }
}

/**
 * 冲突项目
 */
@Composable
private fun ConflictItem(
    conflict: ConflictLog,
    onResolve: (ConflictResolution) -> Unit,
    onViewDetails: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            Icons.Default.Warning,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.error
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = getDataTypeDisplayName(conflict.dataType),
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    
                    Text(
                        text = "项目ID: ${conflict.itemId}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "检测时间: ${formatTimestamp(conflict.detectedTime)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                IconButton(onClick = onViewDetails) {
                    Icon(
                        Icons.Default.Visibility,
                        contentDescription = "查看详情",
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 快速解决按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = { onResolve(ConflictResolution.USE_LOCAL) },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("使用本地", maxLines = 1, overflow = TextOverflow.Ellipsis)
                }
                
                OutlinedButton(
                    onClick = { onResolve(ConflictResolution.USE_SERVER) },
                    modifier = Modifier.weight(1f)
                ) {
                    Text("使用服务器", maxLines = 1, overflow = TextOverflow.Ellipsis)
                }
                
                Button(
                    onClick = onViewDetails,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("详情", maxLines = 1, overflow = TextOverflow.Ellipsis)
                }
            }
        }
    }
}

/**
 * 冲突详情对话框
 */
@Composable
private fun ConflictDetailsDialog(
    conflict: ConflictLog,
    onDismiss: () -> Unit,
    onResolve: (ConflictResolution) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Warning,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("数据冲突详情")
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
            ) {
                // 基本信息
                InfoSection(
                    title = "基本信息",
                    content = {
                        InfoRow("数据类型", getDataTypeDisplayName(conflict.dataType))
                        InfoRow("项目ID", conflict.itemId)
                        InfoRow("检测时间", formatTimestamp(conflict.detectedTime))
                    }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 本地数据
                DataSection(
                    title = "本地数据",
                    timestamp = conflict.localTimestamp,
                    data = conflict.localData,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 服务器数据
                DataSection(
                    title = "服务器数据",
                    timestamp = conflict.serverTimestamp,
                    data = conflict.serverData,
                    color = MaterialTheme.colorScheme.tertiary
                )
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = { onResolve(ConflictResolution.USE_LOCAL) }
                ) {
                    Text("使用本地")
                }
                
                TextButton(
                    onClick = { onResolve(ConflictResolution.USE_SERVER) }
                ) {
                    Text("使用服务器")
                }
                
                if (canMergeData(conflict.dataType)) {
                    TextButton(
                        onClick = { onResolve(ConflictResolution.MERGE) }
                    ) {
                        Text("合并数据")
                    }
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 信息区域
 */
@Composable
private fun InfoSection(
    title: String,
    content: @Composable () -> Unit
) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.height(8.dp))
        content()
    }
}

/**
 * 信息行
 */
@Composable
private fun InfoRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 数据区域
 */
@Composable
private fun DataSection(
    title: String,
    timestamp: Long,
    data: String,
    color: Color
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Bold,
                color = color
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = formatTimestamp(timestamp),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Card(
            colors = CardDefaults.cardColors(
                containerColor = color.copy(alpha = 0.1f)
            )
        ) {
            Text(
                text = data,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(12.dp)
            )
        }
    }
}

/**
 * 获取数据类型显示名称
 */
private fun getDataTypeDisplayName(dataType: SyncDataType): String {
    return when (dataType) {
        SyncDataType.TRANSLATION_HISTORY -> "翻译历史"
        SyncDataType.FAVORITES -> "收藏夹"
        SyncDataType.LEARNING_PROGRESS -> "学习进度"
        SyncDataType.USER_SETTINGS -> "用户设置"
        SyncDataType.CUSTOM_DICTIONARY -> "自定义词典"
    }
}

/**
 * 格式化时间戳
 */
private fun formatTimestamp(timestamp: Long): String {
    // TODO: 实现真实的时间格式化
    return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
        .format(java.util.Date(timestamp))
}

/**
 * 检查是否可以合并数据
 */
private fun canMergeData(dataType: SyncDataType): Boolean {
    return when (dataType) {
        SyncDataType.TRANSLATION_HISTORY -> true
        SyncDataType.FAVORITES -> true
        SyncDataType.LEARNING_PROGRESS -> true
        SyncDataType.USER_SETTINGS -> false // 设置通常不能合并
        SyncDataType.CUSTOM_DICTIONARY -> true
    }
}
