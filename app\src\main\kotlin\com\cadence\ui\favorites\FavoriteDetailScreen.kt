package com.cadence.ui.favorites

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.R
import com.cadence.domain.model.*
import com.cadence.ui.components.ErrorMessage
import com.cadence.ui.components.LoadingIndicator
import com.cadence.ui.favorites.components.PriorityChip
import com.cadence.ui.favorites.components.TagChips
import com.cadence.ui.favorites.components.EditFavoriteDialog
import com.cadence.ui.favorites.components.MoveFavoriteDialog

/**
 * 收藏详情界面
 * 显示收藏项的详细信息并支持编辑
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoriteDetailScreen(
    favoriteId: String,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: FavoriteDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val favoriteItem by viewModel.favoriteItem.collectAsStateWithLifecycle()
    val translation by viewModel.translation.collectAsStateWithLifecycle()
    val folders by viewModel.folders.collectAsStateWithLifecycle()
    
    var showEditDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showMoveDialog by remember { mutableStateOf(false) }
    
    // 加载收藏项详情
    LaunchedEffect(favoriteId) {
        viewModel.loadFavoriteDetail(favoriteId)
    }
    
    // 显示错误或成功消息
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // 这里可以显示Snackbar或其他提示
        }
    }
    
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // 这里可以显示Snackbar或其他提示
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = stringResource(R.string.favorite_detail),
                        fontWeight = FontWeight.Bold
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = stringResource(R.string.back)
                        )
                    }
                },
                actions = {
                    // 编辑按钮
                    IconButton(
                        onClick = { showEditDialog = true },
                        enabled = favoriteItem != null
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = stringResource(R.string.edit)
                        )
                    }
                    
                    // 更多操作
                    var showMenu by remember { mutableStateOf(false) }
                    Box {
                        IconButton(
                            onClick = { showMenu = true },
                            enabled = favoriteItem != null
                        ) {
                            Icon(
                                imageVector = Icons.Default.MoreVert,
                                contentDescription = stringResource(R.string.more_options)
                            )
                        }
                        
                        DropdownMenu(
                            expanded = showMenu,
                            onDismissRequest = { showMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.move_to_folder)) },
                                onClick = {
                                    showMoveDialog = true
                                    showMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.DriveFileMove,
                                        contentDescription = null
                                    )
                                }
                            )
                            
                            DropdownMenuItem(
                                text = { Text(stringResource(R.string.delete)) },
                                onClick = {
                                    showDeleteDialog = true
                                    showMenu = false
                                },
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = null
                                    )
                                }
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                favoriteItem != null && translation != null -> {
                    FavoriteDetailContent(
                        favoriteItem = favoriteItem!!,
                        translation = translation!!,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                            .verticalScroll(rememberScrollState())
                    )
                }
                
                else -> {
                    Text(
                        text = stringResource(R.string.favorite_not_found),
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
            
            // 错误信息
            uiState.error?.let { error ->
                ErrorMessage(
                    message = error,
                    onDismiss = viewModel::clearError,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(16.dp)
                )
            }
        }
    }
    
    // 编辑对话框
    if (showEditDialog && favoriteItem != null) {
        EditFavoriteDialog(
            favoriteItem = favoriteItem!!,
            onDismiss = { showEditDialog = false },
            onConfirm = { note, tags, priority ->
                viewModel.updateFavoriteItem(
                    favoriteId = favoriteItem!!.id,
                    note = note,
                    tags = tags,
                    priority = priority
                )
                showEditDialog = false
            }
        )
    }
    
    // 移动对话框
    if (showMoveDialog && favoriteItem != null) {
        MoveFavoriteDialog(
            folders = folders.filter { it.id != favoriteItem!!.folderId },
            onDismiss = { showMoveDialog = false },
            onConfirm = { targetFolderId ->
                viewModel.moveFavoriteItem(favoriteItem!!.id, targetFolderId)
                showMoveDialog = false
            }
        )
    }
    
    // 删除确认对话框
    if (showDeleteDialog && favoriteItem != null) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.delete_favorite_title),
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(stringResource(R.string.delete_favorite_message))
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.deleteFavoriteItem(favoriteItem!!.id)
                        showDeleteDialog = false
                        onNavigateBack() // 删除后返回
                    }
                ) {
                    Text(
                        text = stringResource(R.string.delete),
                        color = MaterialTheme.colorScheme.error
                    )
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

/**
 * 收藏详情内容
 */
@Composable
private fun FavoriteDetailContent(
    favoriteItem: FavoriteItem,
    translation: Translation,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 翻译内容卡片
        TranslationContentCard(
            translation = translation,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 收藏信息卡片
        FavoriteInfoCard(
            favoriteItem = favoriteItem,
            modifier = Modifier.fillMaxWidth()
        )
        
        // 统计信息卡片
        FavoriteStatsCard(
            favoriteItem = favoriteItem,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * 翻译内容卡片
 */
@Composable
private fun TranslationContentCard(
    translation: Translation,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = stringResource(R.string.translation_content),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            // 原文
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = stringResource(R.string.source_text),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = translation.sourceText,
                    style = MaterialTheme.typography.bodyLarge
                )
            }
            
            // 译文
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = stringResource(R.string.translated_text),
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = translation.translatedText,
                    style = MaterialTheme.typography.bodyLarge
                )
            }
            
            // 语言信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "${translation.sourceLanguage.name} → ${translation.targetLanguage.name}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                translation.confidenceScore?.let { score ->
                    Text(
                        text = stringResource(R.string.confidence_score, (score * 100).toInt()),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 收藏信息卡片
 */
@Composable
private fun FavoriteInfoCard(
    favoriteItem: FavoriteItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = stringResource(R.string.favorite_info),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // 优先级
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.priority),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                PriorityChip(priority = favoriteItem.priority)
            }

            // 备注
            favoriteItem.note?.let { note ->
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = stringResource(R.string.note),
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = note,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // 标签
            if (favoriteItem.tags.isNotEmpty()) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = stringResource(R.string.tags),
                        style = MaterialTheme.typography.labelMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    TagChips(
                        tags = favoriteItem.tags,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 收藏统计卡片
 */
@Composable
private fun FavoriteStatsCard(
    favoriteItem: FavoriteItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = stringResource(R.string.statistics),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // 创建时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.created_at),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = formatDateTime(favoriteItem.createdAt),
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 更新时间
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.updated_at),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = formatDateTime(favoriteItem.updatedAt),
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 访问次数
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.access_count),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = favoriteItem.accessCount.toString(),
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

/**
 * 格式化日期时间
 */
private fun formatDateTime(timestamp: Long): String {
    val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
    return formatter.format(java.util.Date(timestamp))
}
