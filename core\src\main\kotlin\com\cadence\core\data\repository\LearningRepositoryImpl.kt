package com.cadence.core.data.repository

import com.cadence.core.database.dao.learning.*
import com.cadence.core.database.entity.learning.*
import com.cadence.domain.model.learning.*
import com.cadence.domain.repository.LearningRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 学习功能Repository实现
 * 处理学习相关的数据操作和业务逻辑
 */
@Singleton
class LearningRepositoryImpl @Inject constructor(
    private val wordDao: WordDao,
    private val learningProgressDao: LearningProgressDao,
    private val studySessionDao: StudySessionDao
) : LearningRepository {
    
    private val json = Json { ignoreUnknownKeys = true }
    
    // 单词管理
    override suspend fun addWord(word: Word) {
        wordDao.insertWord(word.toEntity())
    }
    
    override suspend fun getWordById(id: String): Word? {
        return wordDao.getWordById(id)?.toDomainModel()
    }
    
    override suspend fun getAllWords(): Flow<List<Word>> {
        return wordDao.getAllWords().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getWordsByLanguage(language: String): Flow<List<Word>> {
        return wordDao.getWordsByLanguage(language).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getWordsByCategory(category: WordCategory): Flow<List<Word>> {
        return wordDao.getWordsByCategory(category.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun searchWords(query: String): Flow<List<Word>> {
        return wordDao.searchWords(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getRandomWords(limit: Int): List<Word> {
        return wordDao.getRandomWords(limit).map { it.toDomainModel() }
    }
    
    override suspend fun updateWord(word: Word) {
        wordDao.updateWord(word.toEntity())
    }
    
    override suspend fun deleteWord(wordId: String) {
        wordDao.deleteWordById(wordId)
    }
    
    // 学习进度管理
    override suspend fun getLearningProgress(userId: String, wordId: String): LearningProgress? {
        return learningProgressDao.getLearningProgress(userId, wordId)?.toDomainModel()
    }
    
    override suspend fun getUserLearningProgress(userId: String): Flow<List<LearningProgress>> {
        return learningProgressDao.getUserLearningProgress(userId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun updateLearningProgress(progress: LearningProgress) {
        learningProgressDao.updateLearningProgress(progress.toEntity())
    }
    
    override suspend fun getWordsToReview(userId: String): List<Word> {
        val currentTime = System.currentTimeMillis()
        val progressEntities = learningProgressDao.getWordsToReview(userId, currentTime)
        val wordIds = progressEntities.map { it.wordId }
        
        return wordIds.mapNotNull { wordId ->
            wordDao.getWordById(wordId)?.toDomainModel()
        }
    }
    
    override suspend fun getNewWords(userId: String, limit: Int): List<Word> {
        // 获取用户还没有学习进度的单词
        val allWords = wordDao.getRandomWords(limit * 2) // 获取更多以确保有足够的新单词
        val userProgress = learningProgressDao.getUserLearningProgress(userId)
        
        // 过滤出没有学习进度的单词
        val progressWordIds = mutableSetOf<String>()
        userProgress.collect { progressList ->
            progressWordIds.addAll(progressList.map { it.wordId })
        }
        
        return allWords
            .filter { it.id !in progressWordIds }
            .take(limit)
            .map { it.toDomainModel() }
    }
    
    override suspend fun getBookmarkedWords(userId: String): List<Word> {
        val bookmarkedProgress = learningProgressDao.getBookmarkedWords(userId)
        val wordIds = mutableListOf<String>()
        bookmarkedProgress.collect { progressList ->
            wordIds.addAll(progressList.map { it.wordId })
        }
        
        return wordIds.mapNotNull { wordId ->
            wordDao.getWordById(wordId)?.toDomainModel()
        }
    }
    
    override suspend fun toggleWordBookmark(userId: String, wordId: String) {
        val currentProgress = learningProgressDao.getLearningProgress(userId, wordId)
        if (currentProgress != null) {
            learningProgressDao.toggleBookmark(
                userId = userId,
                wordId = wordId,
                isBookmarked = !currentProgress.isBookmarked,
                updatedAt = System.currentTimeMillis()
            )
        } else {
            // 创建新的学习进度记录
            val newProgress = LearningProgress(
                id = UUID.randomUUID().toString(),
                userId = userId,
                wordId = wordId,
                masteryLevel = MasteryLevel.NEW,
                isBookmarked = true,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            )
            learningProgressDao.insertLearningProgress(newProgress.toEntity())
        }
    }
    
    // 学习会话管理
    override suspend fun startStudySession(userId: String, wordIds: List<String>, sessionType: SessionType): StudySession {
        val session = StudySession(
            id = UUID.randomUUID().toString(),
            userId = userId,
            sessionType = sessionType,
            startTime = System.currentTimeMillis(),
            wordsStudied = wordIds,
            createdAt = System.currentTimeMillis()
        )
        
        studySessionDao.insertStudySession(session.toEntity())
        return session
    }
    
    override suspend fun updateStudySession(session: StudySession) {
        studySessionDao.updateStudySession(session.toEntity())
    }
    
    override suspend fun completeStudySession(sessionId: String, correctAnswers: Int, totalQuestions: Int) {
        val timeSpent = System.currentTimeMillis() // 这里应该计算实际时间差
        studySessionDao.completeStudySession(
            sessionId = sessionId,
            endTime = System.currentTimeMillis(),
            correctAnswers = correctAnswers,
            totalQuestions = totalQuestions,
            timeSpent = timeSpent
        )
    }
    
    override suspend fun getUserStudySessions(userId: String): Flow<List<StudySession>> {
        return studySessionDao.getUserStudySessions(userId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getRecentStudySessions(userId: String, limit: Int): List<StudySession> {
        return studySessionDao.getRecentStudySessions(userId, limit).map { it.toDomainModel() }
    }
    
    // 学习统计
    override suspend fun getLearningStatistics(userId: String): LearningStatistics {
        val stats = learningProgressDao.getLearningStatistics(userId)
        val sessionStats = studySessionDao.getSessionStatistics(userId)
        
        return LearningStatistics(
            userId = userId,
            totalWordsLearned = stats?.total_words ?: 0,
            totalStudySessions = sessionStats?.total_sessions ?: 0,
            totalStudyTime = sessionStats?.total_time ?: 0L,
            averageAccuracy = stats?.avg_accuracy ?: 0f,
            masteredWords = stats?.mastered_words ?: 0,
            wordsToReview = getWordsToReview(userId).size
        )
    }
    
    override suspend fun updateLearningStatistics(userId: String) {
        // 统计数据会在查询时动态计算，这里可以实现缓存逻辑
    }
    
    override suspend fun getDailyProgress(userId: String, days: Int): List<DailyProgress> {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (days * 24 * 60 * 60 * 1000L)

        // 模拟每日进度数据
        val progressList = mutableListOf<DailyProgress>()
        for (i in 0 until days) {
            val dayTime = endTime - (i * 24 * 60 * 60 * 1000L)
            progressList.add(
                DailyProgress(
                    date = dayTime,
                    wordsStudied = (5..20).random(),
                    correctAnswers = (10..30).random(),
                    totalAnswers = (15..40).random(),
                    studyTime = (10..60).random() * 60 * 1000L,
                    newWordsLearned = (1..5).random(),
                    reviewsCompleted = (3..15).random()
                )
            )
        }
        return progressList.reversed()
    }
    
    override suspend fun getCategoryProgress(userId: String): Map<WordCategory, com.cadence.feature.learning.presentation.component.CategoryProgress> {
        val result = mutableMapOf<WordCategory, com.cadence.feature.learning.presentation.component.CategoryProgress>()

        WordCategory.values().forEach { category ->
            // 模拟分类进度数据
            val totalWords = (10..50).random()
            val masteredWords = (totalWords * 0.3f).toInt()..(totalWords * 0.8f).toInt().random()
            val averageAccuracy = 0.6f + (kotlin.random.Random.nextFloat() * 0.3f)
            val totalStudyTime = (30..180).random() * 60 * 1000L

            result[category] = com.cadence.feature.learning.presentation.component.CategoryProgress(
                totalWords = totalWords,
                masteredWords = masteredWords,
                averageAccuracy = averageAccuracy,
                totalStudyTime = totalStudyTime
            )
        }

        return result
    }
    
    // 复习提醒
    override suspend fun scheduleReview(userId: String, wordId: String, masteryLevel: MasteryLevel) {
        val nextReviewTime = System.currentTimeMillis() + masteryLevel.reviewInterval
        learningProgressDao.updateMasteryLevel(
            userId = userId,
            wordId = wordId,
            masteryLevel = masteryLevel.name,
            nextReviewAt = nextReviewTime,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    override suspend fun getScheduledReviews(userId: String): List<LearningProgress> {
        val currentTime = System.currentTimeMillis()
        return learningProgressDao.getWordsToReview(userId, currentTime).map { it.toDomainModel() }
    }
    
    override suspend fun markReviewCompleted(userId: String, wordId: String, success: Boolean) {
        val currentProgress = learningProgressDao.getLearningProgress(userId, wordId)
        if (currentProgress != null) {
            val newCorrectAnswers = if (success) currentProgress.correctAnswers + 1 else currentProgress.correctAnswers
            val newTotalAttempts = currentProgress.totalAttempts + 1
            
            learningProgressDao.updateLearningStats(
                userId = userId,
                wordId = wordId,
                correctAnswers = newCorrectAnswers,
                totalAttempts = newTotalAttempts,
                lastStudiedAt = System.currentTimeMillis(),
                studyStreak = if (success) currentProgress.studyStreak + 1 else 0,
                updatedAt = System.currentTimeMillis()
            )
            
            // 根据成功率调整掌握程度
            val accuracyRate = newCorrectAnswers.toFloat() / newTotalAttempts
            val currentMasteryLevel = MasteryLevel.valueOf(currentProgress.masteryLevel)
            val newMasteryLevel = calculateNewMasteryLevel(currentMasteryLevel, accuracyRate, success)
            
            if (newMasteryLevel != currentMasteryLevel) {
                scheduleReview(userId, wordId, newMasteryLevel)
            }
        }
    }
    
    /**
     * 根据学习表现计算新的掌握程度
     */
    private fun calculateNewMasteryLevel(
        currentLevel: MasteryLevel,
        accuracyRate: Float,
        lastSuccess: Boolean
    ): MasteryLevel {
        return when {
            accuracyRate >= 0.9f && lastSuccess -> {
                when (currentLevel) {
                    MasteryLevel.NEW -> MasteryLevel.LEARNING
                    MasteryLevel.LEARNING -> MasteryLevel.FAMILIAR
                    MasteryLevel.FAMILIAR -> MasteryLevel.KNOWN
                    MasteryLevel.KNOWN -> MasteryLevel.MASTERED
                    MasteryLevel.MASTERED -> MasteryLevel.MASTERED
                }
            }
            accuracyRate < 0.5f && !lastSuccess -> {
                when (currentLevel) {
                    MasteryLevel.MASTERED -> MasteryLevel.KNOWN
                    MasteryLevel.KNOWN -> MasteryLevel.FAMILIAR
                    MasteryLevel.FAMILIAR -> MasteryLevel.LEARNING
                    MasteryLevel.LEARNING -> MasteryLevel.NEW
                    MasteryLevel.NEW -> MasteryLevel.NEW
                }
            }
            else -> currentLevel
        }
    }
}

// 扩展函数：实体转换
private fun Word.toEntity(): WordEntity {
    return WordEntity(
        id = id,
        text = text,
        language = language.name,
        pronunciation = pronunciation,
        definition = definition,
        example = example,
        translation = translation,
        translationLanguage = translationLanguage.name,
        difficulty = difficulty.name,
        category = category.name,
        tags = Json.encodeToString(tags),
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

private fun WordEntity.toDomainModel(): Word {
    return Word(
        id = id,
        text = text,
        language = com.cadence.domain.model.Language.valueOf(language),
        pronunciation = pronunciation,
        definition = definition,
        example = example,
        translation = translation,
        translationLanguage = com.cadence.domain.model.Language.valueOf(translationLanguage),
        difficulty = WordDifficulty.valueOf(difficulty),
        category = WordCategory.valueOf(category),
        tags = try { Json.decodeFromString<List<String>>(tags) } catch (e: Exception) { emptyList() },
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

private fun LearningProgress.toEntity(): LearningProgressEntity {
    return LearningProgressEntity(
        id = id,
        userId = userId,
        wordId = wordId,
        masteryLevel = masteryLevel.name,
        correctAnswers = correctAnswers,
        totalAttempts = totalAttempts,
        lastStudiedAt = lastStudiedAt,
        nextReviewAt = nextReviewAt,
        studyStreak = studyStreak,
        isBookmarked = isBookmarked,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

private fun LearningProgressEntity.toDomainModel(): LearningProgress {
    return LearningProgress(
        id = id,
        userId = userId,
        wordId = wordId,
        masteryLevel = MasteryLevel.valueOf(masteryLevel),
        correctAnswers = correctAnswers,
        totalAttempts = totalAttempts,
        lastStudiedAt = lastStudiedAt,
        nextReviewAt = nextReviewAt,
        studyStreak = studyStreak,
        isBookmarked = isBookmarked,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

private fun StudySession.toEntity(): StudySessionEntity {
    return StudySessionEntity(
        id = id,
        userId = userId,
        sessionType = sessionType.name,
        startTime = startTime,
        endTime = endTime,
        wordsStudied = Json.encodeToString(wordsStudied),
        correctAnswers = correctAnswers,
        totalQuestions = totalQuestions,
        timeSpent = timeSpent,
        createdAt = createdAt
    )
}

private fun StudySessionEntity.toDomainModel(): StudySession {
    return StudySession(
        id = id,
        userId = userId,
        sessionType = SessionType.valueOf(sessionType),
        startTime = startTime,
        endTime = endTime,
        wordsStudied = try { Json.decodeFromString<List<String>>(wordsStudied) } catch (e: Exception) { emptyList() },
        correctAnswers = correctAnswers,
        totalQuestions = totalQuestions,
        timeSpent = timeSpent,
        createdAt = createdAt
    )
}