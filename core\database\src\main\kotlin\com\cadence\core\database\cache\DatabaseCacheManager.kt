package com.cadence.core.database.cache

import com.cadence.core.database.dao.TranslationCacheDao
import com.cadence.core.database.dao.UserPreferenceDao
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据库缓存管理器
 * 负责管理翻译缓存的生命周期、清理策略和性能优化
 */
@Singleton
class DatabaseCacheManager @Inject constructor(
    private val translationCacheDao: TranslationCacheDao,
    private val userPreferenceDao: UserPreferenceDao
) {
    
    private val cacheScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 缓存配置
    private var maxCacheSize: Int = 10000 // 最大缓存条目数
    private var cacheExpiryHours: Long = 24 // 缓存过期时间（小时）
    private var cleanupThreshold: Float = 0.8f // 清理阈值（80%时开始清理）
    
    init {
        // 启动时加载缓存配置
        loadCacheConfiguration()
        
        // 启动定期清理任务
        startPeriodicCleanup()
    }
    
    /**
     * 加载缓存配置
     */
    private fun loadCacheConfiguration() {
        cacheScope.launch {
            try {
                // 从用户偏好中加载缓存配置
                val cacheExpiryPref = userPreferenceDao.getPreference("cache_expiry_hours").first()
                cacheExpiryPref?.let { 
                    cacheExpiryHours = it.value.toLongOrNull() ?: 24
                }
                
                val maxCacheSizePref = userPreferenceDao.getPreference("max_cache_size").first()
                maxCacheSizePref?.let {
                    maxCacheSize = it.value.toIntOrNull() ?: 10000
                }
                
                Timber.d("缓存配置加载完成: 过期时间=${cacheExpiryHours}小时, 最大大小=${maxCacheSize}")
            } catch (e: Exception) {
                Timber.e(e, "加载缓存配置失败，使用默认值")
            }
        }
    }
    
    /**
     * 启动定期清理任务
     */
    private fun startPeriodicCleanup() {
        cacheScope.launch {
            try {
                // 每小时执行一次清理检查
                kotlinx.coroutines.delay(3600_000) // 1小时
                performCleanupCheck()
                
                // 递归调用以实现定期清理
                startPeriodicCleanup()
            } catch (e: Exception) {
                Timber.e(e, "定期清理任务异常")
                // 重新启动清理任务
                kotlinx.coroutines.delay(60_000) // 1分钟后重试
                startPeriodicCleanup()
            }
        }
    }
    
    /**
     * 执行清理检查
     */
    private suspend fun performCleanupCheck() {
        try {
            val currentCacheCount = translationCacheDao.getCacheCount()
            val threshold = (maxCacheSize * cleanupThreshold).toInt()
            
            Timber.d("缓存检查: 当前数量=$currentCacheCount, 阈值=$threshold")
            
            if (currentCacheCount > threshold) {
                performCacheCleanup()
            }
            
            // 清理过期缓存
            cleanupExpiredCache()
            
        } catch (e: Exception) {
            Timber.e(e, "执行清理检查失败")
        }
    }
    
    /**
     * 执行缓存清理
     */
    private suspend fun performCacheCleanup() {
        try {
            val currentTime = System.currentTimeMillis()
            val expiryTime = currentTime - (cacheExpiryHours * 3600_000)
            
            // 1. 清理过期缓存
            val expiredCount = translationCacheDao.deleteExpiredCache(expiryTime)
            Timber.d("清理过期缓存: $expiredCount 条")
            
            // 2. 如果仍然超过阈值，使用LRU策略清理
            val remainingCount = translationCacheDao.getCacheCount()
            if (remainingCount > maxCacheSize) {
                val deleteCount = remainingCount - (maxCacheSize * 0.7).toInt() // 清理到70%
                val deletedLRU = translationCacheDao.deleteLRUCache(deleteCount)
                Timber.d("LRU清理缓存: $deletedLRU 条")
            }
            
            // 3. 清理低优先级缓存
            val lowPriorityCount = translationCacheDao.deleteLowPriorityCache(100) // 最多清理100条
            Timber.d("清理低优先级缓存: $lowPriorityCount 条")
            
        } catch (e: Exception) {
            Timber.e(e, "执行缓存清理失败")
        }
    }
    
    /**
     * 清理过期缓存
     */
    private suspend fun cleanupExpiredCache() {
        try {
            val currentTime = System.currentTimeMillis()
            val expiryTime = currentTime - (cacheExpiryHours * 3600_000)
            
            val deletedCount = translationCacheDao.deleteExpiredCache(expiryTime)
            if (deletedCount > 0) {
                Timber.d("定期清理过期缓存: $deletedCount 条")
            }
        } catch (e: Exception) {
            Timber.e(e, "清理过期缓存失败")
        }
    }
    
    /**
     * 手动触发缓存清理
     */
    suspend fun manualCleanup() {
        Timber.d("手动触发缓存清理")
        performCacheCleanup()
    }
    
    /**
     * 获取缓存统计信息
     */
    suspend fun getCacheStatistics(): CacheStatistics {
        return try {
            val totalCount = translationCacheDao.getCacheCount()
            val currentTime = System.currentTimeMillis()
            val expiryTime = currentTime - (cacheExpiryHours * 3600_000)
            val expiredCount = translationCacheDao.getExpiredCacheCount(expiryTime)
            val favoriteCount = translationCacheDao.getFavoriteCacheCount()
            val pinnedCount = translationCacheDao.getPinnedCacheCount()
            
            CacheStatistics(
                totalCount = totalCount,
                expiredCount = expiredCount,
                favoriteCount = favoriteCount,
                pinnedCount = pinnedCount,
                usagePercentage = if (maxCacheSize > 0) (totalCount.toFloat() / maxCacheSize) else 0f,
                maxCacheSize = maxCacheSize,
                cacheExpiryHours = cacheExpiryHours
            )
        } catch (e: Exception) {
            Timber.e(e, "获取缓存统计信息失败")
            CacheStatistics()
        }
    }
    
    /**
     * 更新缓存配置
     */
    suspend fun updateCacheConfiguration(
        newMaxCacheSize: Int? = null,
        newCacheExpiryHours: Long? = null
    ) {
        try {
            newMaxCacheSize?.let { 
                maxCacheSize = it
                userPreferenceDao.setPreference("max_cache_size", it.toString())
            }
            
            newCacheExpiryHours?.let { 
                cacheExpiryHours = it
                userPreferenceDao.setPreference("cache_expiry_hours", it.toString())
            }
            
            Timber.d("缓存配置已更新: 最大大小=$maxCacheSize, 过期时间=${cacheExpiryHours}小时")
        } catch (e: Exception) {
            Timber.e(e, "更新缓存配置失败")
        }
    }
}

/**
 * 缓存统计信息数据类
 */
data class CacheStatistics(
    val totalCount: Int = 0,
    val expiredCount: Int = 0,
    val favoriteCount: Int = 0,
    val pinnedCount: Int = 0,
    val usagePercentage: Float = 0f,
    val maxCacheSize: Int = 0,
    val cacheExpiryHours: Long = 0
)
