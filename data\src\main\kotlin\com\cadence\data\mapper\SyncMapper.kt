package com.cadence.data.mapper

import com.cadence.core.database.entity.*
import com.cadence.domain.model.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import java.util.UUID

/**
 * 同步数据映射器
 * 负责同步相关实体与领域模型之间的转换
 */
object SyncMapper {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // ========== SyncConfig 映射 ==========
    
    /**
     * 将SyncConfigEntity转换为SyncConfig领域模型
     */
    fun SyncConfigEntity.toDomain(): SyncConfig {
        return SyncConfig(
            isEnabled = isEnabled,
            autoSyncEnabled = autoSyncEnabled,
            syncInterval = syncInterval,
            wifiOnlySync = wifiOnlySync,
            lastSyncTime = lastSyncTime,
            userId = userId,
            deviceId = deviceId
        )
    }
    
    /**
     * 将SyncConfig领域模型转换为SyncConfigEntity
     */
    fun SyncConfig.toEntity(): SyncConfigEntity {
        val currentTime = System.currentTimeMillis()
        return SyncConfigEntity(
            id = "default",
            isEnabled = isEnabled,
            autoSyncEnabled = autoSyncEnabled,
            syncInterval = syncInterval,
            wifiOnlySync = wifiOnlySync,
            lastSyncTime = lastSyncTime,
            userId = userId,
            deviceId = deviceId,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== SyncResult 映射 ==========
    
    /**
     * 将SyncHistoryEntity转换为SyncResult领域模型
     */
    fun SyncHistoryEntity.toDomain(): SyncResult {
        val details = try {
            if (details.isNullOrBlank()) {
                emptyList()
            } else {
                json.decodeFromString<List<SyncItemResult>>(details)
            }
        } catch (e: Exception) {
            emptyList()
        }
        
        return SyncResult(
            syncType = SyncType.valueOf(syncType),
            status = SyncStatus.valueOf(status),
            startTime = startTime,
            endTime = endTime,
            uploadedCount = uploadedCount,
            downloadedCount = downloadedCount,
            conflictCount = conflictCount,
            errorMessage = errorMessage,
            details = details
        )
    }
    
    /**
     * 将SyncResult领域模型转换为SyncHistoryEntity
     */
    fun SyncResult.toEntity(): SyncHistoryEntity {
        val detailsJson = try {
            if (details.isEmpty()) {
                null
            } else {
                json.encodeToString(details)
            }
        } catch (e: Exception) {
            null
        }
        
        return SyncHistoryEntity(
            id = UUID.randomUUID().toString(),
            syncType = syncType.name,
            status = status.name,
            startTime = startTime,
            endTime = endTime,
            uploadedCount = uploadedCount,
            downloadedCount = downloadedCount,
            conflictCount = conflictCount,
            errorMessage = errorMessage,
            details = detailsJson,
            createdAt = System.currentTimeMillis()
        )
    }
    
    // ========== SyncConflict 映射 ==========
    
    /**
     * 将SyncConflictEntity转换为SyncConflict领域模型
     */
    inline fun <reified T> SyncConflictEntity.toDomain(): SyncConflict<T> {
        val localData = json.decodeFromString<SyncData<T>>(localData)
        val remoteData = json.decodeFromString<SyncData<T>>(remoteData)
        
        return SyncConflict(
            itemId = itemId,
            localData = localData,
            remoteData = remoteData,
            conflictType = ConflictType.valueOf(conflictType)
        )
    }
    
    /**
     * 将SyncConflict领域模型转换为SyncConflictEntity
     */
    fun <T> SyncConflict<T>.toEntity(): SyncConflictEntity {
        return SyncConflictEntity(
            id = UUID.randomUUID().toString(),
            itemId = itemId,
            itemType = localData.data!!::class.simpleName ?: "Unknown",
            conflictType = conflictType.name,
            localData = json.encodeToString(localData),
            remoteData = json.encodeToString(remoteData),
            resolved = false,
            resolution = null,
            createdAt = System.currentTimeMillis(),
            resolvedAt = null
        )
    }
    
    // ========== DeviceInfo 映射 ==========
    
    /**
     * 将DeviceInfoEntity转换为DeviceInfo领域模型
     */
    fun DeviceInfoEntity.toDomain(): DeviceInfo {
        return DeviceInfo(
            deviceId = deviceId,
            deviceName = deviceName,
            platform = platform,
            appVersion = appVersion,
            lastActiveTime = lastActiveTime,
            isCurrentDevice = isCurrentDevice
        )
    }
    
    /**
     * 将DeviceInfo领域模型转换为DeviceInfoEntity
     */
    fun DeviceInfo.toEntity(): DeviceInfoEntity {
        val currentTime = System.currentTimeMillis()
        return DeviceInfoEntity(
            id = UUID.randomUUID().toString(),
            deviceId = deviceId,
            deviceName = deviceName,
            platform = platform,
            appVersion = appVersion,
            lastActiveTime = lastActiveTime,
            isCurrentDevice = isCurrentDevice,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== SyncStatistics 映射 ==========
    
    /**
     * 将SyncStatisticsEntity转换为SyncStatistics领域模型
     */
    fun SyncStatisticsEntity.toDomain(): SyncStatistics {
        val syncsByType = try {
            json.decodeFromString<Map<String, Int>>(syncsByType)
                .mapKeys { SyncType.valueOf(it.key) }
        } catch (e: Exception) {
            emptyMap()
        }
        
        return SyncStatistics(
            totalSyncs = totalSyncs,
            successfulSyncs = successfulSyncs,
            failedSyncs = failedSyncs,
            lastSyncTime = lastSyncTime,
            totalDataSynced = totalDataSynced,
            averageSyncDuration = averageSyncDuration,
            syncsByType = syncsByType
        )
    }
    
    /**
     * 将SyncStatistics领域模型转换为SyncStatisticsEntity
     */
    fun SyncStatistics.toEntity(): SyncStatisticsEntity {
        val syncsByTypeJson = try {
            json.encodeToString(syncsByType.mapKeys { it.key.name })
        } catch (e: Exception) {
            "{}"
        }
        
        val currentTime = System.currentTimeMillis()
        return SyncStatisticsEntity(
            id = "default",
            totalSyncs = totalSyncs,
            successfulSyncs = successfulSyncs,
            failedSyncs = failedSyncs,
            lastSyncTime = lastSyncTime,
            totalDataSynced = totalDataSynced,
            averageSyncDuration = averageSyncDuration,
            syncsByType = syncsByTypeJson,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== SyncQueue 映射 ==========
    
    /**
     * 将SyncQueueEntity转换为领域模型数据
     */
    inline fun <reified T> SyncQueueEntity.getDataAs(): T? {
        return try {
            json.decodeFromString<T>(data)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 创建SyncQueueEntity
     */
    fun <T> createSyncQueueEntity(
        itemId: String,
        itemType: String,
        action: SyncAction,
        data: T,
        priority: Int = 0
    ): SyncQueueEntity {
        val currentTime = System.currentTimeMillis()
        return SyncQueueEntity(
            id = UUID.randomUUID().toString(),
            itemId = itemId,
            itemType = itemType,
            action = action.name,
            priority = priority,
            data = json.encodeToString(data),
            retryCount = 0,
            maxRetries = 3,
            lastError = null,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== DataVersion 映射 ==========
    
    /**
     * 创建DataVersionEntity
     */
    fun createDataVersionEntity(
        dataType: String,
        itemId: String,
        version: Int,
        lastModified: Long,
        deviceId: String,
        checksum: String? = null
    ): DataVersionEntity {
        val currentTime = System.currentTimeMillis()
        return DataVersionEntity(
            id = UUID.randomUUID().toString(),
            dataType = dataType,
            itemId = itemId,
            version = version,
            lastModified = lastModified,
            deviceId = deviceId,
            checksum = checksum,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== 批量转换方法 ==========
    
    /**
     * 批量转换SyncHistoryEntity列表
     */
    fun List<SyncHistoryEntity>.toDomainList(): List<SyncResult> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换DeviceInfoEntity列表
     */
    fun List<DeviceInfoEntity>.toDomainList(): List<DeviceInfo> {
        return map { it.toDomain() }
    }
}
