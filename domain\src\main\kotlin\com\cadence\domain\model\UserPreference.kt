package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 用户偏好设置领域模型
 */
@Serializable
data class UserPreference(
    val id: String = "default_user",
    val defaultSourceLanguage: Language,
    val defaultTargetLanguage: Language,
    val autoDetectLanguage: Boolean = true,
    val saveTranslationHistory: Boolean = true,
    val enableCulturalContext: Boolean = true,
    val themeMode: ThemeMode = ThemeMode.SYSTEM,
    val fontSize: FontSize = FontSize.MEDIUM,
    val appLanguage: AppLanguage = AppLanguage.SYSTEM,
    val themeColor: ThemeColor = ThemeColor.DEFAULT,
    val enableDynamicColor: Boolean = true,
    val enableVoiceInput: Boolean = true,
    val enableVoiceOutput: Boolean = true,
    val enableOcr: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 主题模式枚举
 */
@Serializable
enum class ThemeMode {
    LIGHT,   // 浅色主题
    DARK,    // 深色主题
    SYSTEM   // 跟随系统
}

/**
 * 字体大小枚举
 */
@Serializable
enum class FontSize {
    SMALL,   // 小字体
    MEDIUM,  // 中等字体
    LARGE    // 大字体
}

/**
 * 应用界面语言枚举
 */
@Serializable
enum class AppLanguage(val code: String, val displayName: String) {
    SYSTEM("system", "跟随系统"),
    CHINESE("zh", "中文"),
    ENGLISH("en", "English"),
    JAPANESE("ja", "日本語"),
    KOREAN("ko", "한국어")
}

/**
 * 主题色彩枚举
 */
@Serializable
enum class ThemeColor(val code: String, val displayName: String) {
    DEFAULT("default", "默认蓝色"),
    CLASSIC_BLUE("classic_blue", "经典蓝色"),
    WARM_ORANGE("warm_orange", "温暖橙色"),
    NATURE_GREEN("nature_green", "自然绿色"),
    ELEGANT_PURPLE("elegant_purple", "优雅紫色"),
    MODERN_TEAL("modern_teal", "现代青色"),
    VIBRANT_PINK("vibrant_pink", "活力粉色")
}

/**
 * 语言偏好设置
 */
@Serializable
data class LanguagePreference(
    val language: Language,
    val isPreferred: Boolean = true,
    val usageCount: Int = 0,
    val lastUsedAt: Long? = null
)

/**
 * 翻译偏好设置
 */
@Serializable
data class TranslationPreference(
    val enableAutoTranslation: Boolean = false,
    val enableRealTimeTranslation: Boolean = false,
    val maxHistoryCount: Int = 1000,
    val cacheExpirationDays: Int = 30,
    val enableOfflineMode: Boolean = false
)

/**
 * 隐私设置
 */
@Serializable
data class PrivacySettings(
    val enableDataCollection: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableAnalytics: Boolean = true,
    val autoDeleteHistoryDays: Int? = null // null表示不自动删除
)

/**
 * 通知设置
 */
@Serializable
data class NotificationSettings(
    val enableTranslationComplete: Boolean = true,
    val enableDailyReminder: Boolean = false,
    val enableWeeklyStats: Boolean = false,
    val reminderTime: String = "09:00" // HH:mm格式
)

/**
 * 完整的用户设置模型
 */
@Serializable
data class UserSettings(
    val userPreference: UserPreference,
    val languagePreferences: List<LanguagePreference> = emptyList(),
    val translationPreference: TranslationPreference = TranslationPreference(),
    val privacySettings: PrivacySettings = PrivacySettings(),
    val notificationSettings: NotificationSettings = NotificationSettings()
)
