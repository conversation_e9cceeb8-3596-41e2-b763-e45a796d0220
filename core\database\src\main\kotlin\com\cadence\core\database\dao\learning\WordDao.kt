package com.cadence.core.database.dao.learning

import androidx.room.*
import com.cadence.core.database.entity.learning.WordEntity
import kotlinx.coroutines.flow.Flow

/**
 * 单词数据访问对象
 */
@Dao
interface WordDao {
    
    /**
     * 获取所有单词
     */
    @Query("SELECT * FROM words ORDER BY created_at DESC")
    fun getAllWords(): Flow<List<WordEntity>>
    
    /**
     * 根据ID获取单词
     */
    @Query("SELECT * FROM words WHERE id = :id")
    suspend fun getWordById(id: String): WordEntity?
    
    /**
     * 根据语言获取单词
     */
    @Query("SELECT * FROM words WHERE language = :language ORDER BY created_at DESC")
    fun getWordsByLanguage(language: String): Flow<List<WordEntity>>
    
    /**
     * 根据分类获取单词
     */
    @Query("SELECT * FROM words WHERE category = :category ORDER BY created_at DESC")
    fun getWordsByCategory(category: String): Flow<List<WordEntity>>
    
    /**
     * 根据难度获取单词
     */
    @Query("SELECT * FROM words WHERE difficulty = :difficulty ORDER BY created_at DESC")
    fun getWordsByDifficulty(difficulty: String): Flow<List<WordEntity>>
    
    /**
     * 搜索单词
     */
    @Query("""
        SELECT * FROM words 
        WHERE text LIKE '%' || :query || '%' 
        OR translation LIKE '%' || :query || '%' 
        OR definition LIKE '%' || :query || '%'
        ORDER BY created_at DESC
    """)
    fun searchWords(query: String): Flow<List<WordEntity>>
    
    /**
     * 获取随机单词
     */
    @Query("SELECT * FROM words ORDER BY RANDOM() LIMIT :limit")
    suspend fun getRandomWords(limit: Int): List<WordEntity>
    
    /**
     * 插入单词
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWord(word: WordEntity)
    
    /**
     * 插入多个单词
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWords(words: List<WordEntity>)
    
    /**
     * 更新单词
     */
    @Update
    suspend fun updateWord(word: WordEntity)
    
    /**
     * 删除单词
     */
    @Delete
    suspend fun deleteWord(word: WordEntity)
    
    /**
     * 根据ID删除单词
     */
    @Query("DELETE FROM words WHERE id = :id")
    suspend fun deleteWordById(id: String)
    
    /**
     * 获取单词总数
     */
    @Query("SELECT COUNT(*) FROM words")
    suspend fun getWordCount(): Int
    
    /**
     * 根据分类获取单词数量
     */
    @Query("SELECT COUNT(*) FROM words WHERE category = :category")
    suspend fun getWordCountByCategory(category: String): Int
}