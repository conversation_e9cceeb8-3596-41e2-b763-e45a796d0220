package com.cadence.core.speech

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语音配置管理器
 * 管理语音识别和TTS的配置设置
 */
@Singleton
class SpeechConfigManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val json: Json
) {
    
    private val preferences: SharedPreferences = context.getSharedPreferences(
        "speech_config", Context.MODE_PRIVATE
    )
    
    private val _speechSettings = MutableStateFlow(loadSpeechSettings())
    val speechSettings: StateFlow<SpeechSettings> = _speechSettings.asStateFlow()
    
    companion object {
        private const val KEY_SPEECH_SETTINGS = "speech_settings"
        private const val KEY_TTS_SETTINGS = "tts_settings"
        private const val KEY_RECOGNITION_SETTINGS = "recognition_settings"
        private const val KEY_AUDIO_SETTINGS = "audio_settings"
    }
    
    /**
     * 加载语音设置
     */
    private fun loadSpeechSettings(): SpeechSettings {
        return try {
            val settingsJson = preferences.getString(KEY_SPEECH_SETTINGS, null)
            if (settingsJson != null) {
                json.decodeFromString<SpeechSettings>(settingsJson)
            } else {
                getDefaultSpeechSettings()
            }
        } catch (e: Exception) {
            Timber.e(e, "加载语音设置失败，使用默认设置")
            getDefaultSpeechSettings()
        }
    }
    
    /**
     * 获取默认语音设置
     */
    private fun getDefaultSpeechSettings(): SpeechSettings {
        return SpeechSettings(
            ttsSettings = TtsSettings(),
            recognitionSettings = RecognitionSettings(),
            audioSettings = AudioSettings(),
            generalSettings = GeneralSettings()
        )
    }
    
    /**
     * 保存语音设置
     */
    suspend fun saveSpeechSettings(settings: SpeechSettings) {
        try {
            val settingsJson = json.encodeToString(settings)
            preferences.edit()
                .putString(KEY_SPEECH_SETTINGS, settingsJson)
                .apply()
            
            _speechSettings.value = settings
            Timber.d("语音设置已保存")
        } catch (e: Exception) {
            Timber.e(e, "保存语音设置失败")
        }
    }
    
    /**
     * 更新TTS设置
     */
    suspend fun updateTtsSettings(ttsSettings: TtsSettings) {
        val currentSettings = _speechSettings.value
        val newSettings = currentSettings.copy(ttsSettings = ttsSettings)
        saveSpeechSettings(newSettings)
    }
    
    /**
     * 更新语音识别设置
     */
    suspend fun updateRecognitionSettings(recognitionSettings: RecognitionSettings) {
        val currentSettings = _speechSettings.value
        val newSettings = currentSettings.copy(recognitionSettings = recognitionSettings)
        saveSpeechSettings(newSettings)
    }
    
    /**
     * 更新音频设置
     */
    suspend fun updateAudioSettings(audioSettings: AudioSettings) {
        val currentSettings = _speechSettings.value
        val newSettings = currentSettings.copy(audioSettings = audioSettings)
        saveSpeechSettings(newSettings)
    }
    
    /**
     * 更新通用设置
     */
    suspend fun updateGeneralSettings(generalSettings: GeneralSettings) {
        val currentSettings = _speechSettings.value
        val newSettings = currentSettings.copy(generalSettings = generalSettings)
        saveSpeechSettings(newSettings)
    }
    
    /**
     * 获取语言的TTS配置
     */
    fun getTtsConfigForLanguage(language: String): TtsConfig {
        val settings = _speechSettings.value.ttsSettings
        
        return TtsConfig(
            speechRate = when (language.substring(0, 2)) {
                "zh" -> settings.speechRate * 0.9f // 中文稍慢
                "ja", "ko" -> settings.speechRate * 0.85f // 日韩语更慢
                "ar", "hi" -> settings.speechRate * 0.8f // 阿拉伯语和印地语最慢
                else -> settings.speechRate
            },
            pitch = settings.pitch,
            volume = settings.volume
        )
    }
    
    /**
     * 获取语言的识别配置
     */
    fun getRecognitionConfigForLanguage(language: String): SpeechConfig {
        val settings = _speechSettings.value.recognitionSettings
        val optimization = getLanguageOptimization(language)
        
        return SpeechConfig(
            language = language,
            maxResults = settings.maxResults,
            partialResults = settings.enablePartialResults,
            offlineOnly = settings.preferOffline && isLanguageSupportedOffline(language),
            confidenceThreshold = optimization.confidenceThreshold,
            timeoutMs = optimization.maxSpeechTimeoutMs
        )
    }
    
    /**
     * 获取语言优化参数（简化版本）
     */
    private fun getLanguageOptimization(language: String): LanguageOptimization {
        return when (language.substring(0, 2)) {
            "zh" -> LanguageOptimization(
                preferredModel = "zh_model",
                confidenceThreshold = 0.7f,
                maxSpeechTimeoutMs = 8000L,
                maxNoSpeechTimeoutMs = 3000L,
                enablePunctuation = true,
                enableNumberFormatting = true,
                speechRate = 1.0f,
                noiseReduction = true
            )
            "en" -> LanguageOptimization(
                preferredModel = "en_model",
                confidenceThreshold = 0.8f,
                maxSpeechTimeoutMs = 10000L,
                maxNoSpeechTimeoutMs = 4000L,
                enablePunctuation = true,
                enableNumberFormatting = true,
                speechRate = 1.1f,
                noiseReduction = true
            )
            else -> LanguageOptimization(
                preferredModel = "default_model",
                confidenceThreshold = 0.7f,
                maxSpeechTimeoutMs = 10000L,
                maxNoSpeechTimeoutMs = 4000L,
                enablePunctuation = true,
                enableNumberFormatting = true,
                speechRate = 1.0f,
                noiseReduction = true
            )
        }
    }
    
    /**
     * 检查语言是否支持离线
     */
    private fun isLanguageSupportedOffline(language: String): Boolean {
        val supportedOfflineLanguages = setOf(
            "zh-CN", "zh-TW", "en-US", "en-GB", 
            "ja-JP", "ko-KR", "es-ES", "fr-FR", "de-DE"
        )
        return supportedOfflineLanguages.contains(language)
    }
    
    /**
     * 重置为默认设置
     */
    suspend fun resetToDefaults() {
        val defaultSettings = getDefaultSpeechSettings()
        saveSpeechSettings(defaultSettings)
    }
    
    /**
     * 导出设置
     */
    fun exportSettings(): String {
        return try {
            json.encodeToString(_speechSettings.value)
        } catch (e: Exception) {
            Timber.e(e, "导出设置失败")
            ""
        }
    }
    
    /**
     * 导入设置
     */
    suspend fun importSettings(settingsJson: String): Boolean {
        return try {
            val settings = json.decodeFromString<SpeechSettings>(settingsJson)
            saveSpeechSettings(settings)
            true
        } catch (e: Exception) {
            Timber.e(e, "导入设置失败")
            false
        }
    }
    
    /**
     * 获取设置摘要
     */
    fun getSettingsSummary(): SettingsSummary {
        val settings = _speechSettings.value
        
        return SettingsSummary(
            ttsEnabled = settings.generalSettings.enableTts,
            recognitionEnabled = settings.generalSettings.enableRecognition,
            offlineMode = settings.recognitionSettings.preferOffline,
            autoLanguageDetection = settings.recognitionSettings.enableAutoLanguageDetection,
            speechRate = settings.ttsSettings.speechRate,
            recognitionLanguage = settings.recognitionSettings.defaultLanguage,
            ttsLanguage = settings.ttsSettings.defaultLanguage,
            cacheEnabled = settings.generalSettings.enableCache,
            noiseReduction = settings.audioSettings.enableNoiseReduction
        )
    }
}

/**
 * 语音设置
 */
@Serializable
data class SpeechSettings(
    val ttsSettings: TtsSettings = TtsSettings(),
    val recognitionSettings: RecognitionSettings = RecognitionSettings(),
    val audioSettings: AudioSettings = AudioSettings(),
    val generalSettings: GeneralSettings = GeneralSettings()
)

/**
 * TTS设置
 */
@Serializable
data class TtsSettings(
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f,
    val volume: Float = 1.0f,
    val defaultLanguage: String = "zh-CN",
    val enableSSML: Boolean = false,
    val voiceGender: VoiceGender = VoiceGender.NEUTRAL,
    val enableEmphasis: Boolean = true
)

/**
 * 语音识别设置
 */
@Serializable
data class RecognitionSettings(
    val defaultLanguage: String = "zh-CN",
    val maxResults: Int = 5,
    val enablePartialResults: Boolean = true,
    val enableAutoLanguageDetection: Boolean = false,
    val preferOffline: Boolean = false,
    val enableProfanityFilter: Boolean = true,
    val enablePunctuation: Boolean = true
)

/**
 * 音频设置
 */
@Serializable
data class AudioSettings(
    val enableNoiseReduction: Boolean = true,
    val enableEchoCancellation: Boolean = true,
    val enableAutomaticGainControl: Boolean = true,
    val inputGain: Float = 1.0f,
    val outputVolume: Float = 1.0f,
    val audioQuality: AudioQuality = AudioQuality.HIGH
)

/**
 * 通用设置
 */
@Serializable
data class GeneralSettings(
    val enableTts: Boolean = true,
    val enableRecognition: Boolean = true,
    val enableCache: Boolean = true,
    val enableAnalytics: Boolean = false,
    val enableHapticFeedback: Boolean = true,
    val enableVisualFeedback: Boolean = true
)

/**
 * 语音性别
 */
@Serializable
enum class VoiceGender {
    MALE, FEMALE, NEUTRAL
}

/**
 * 音频质量
 */
@Serializable
enum class AudioQuality {
    LOW, NORMAL, HIGH, VERY_HIGH
}

/**
 * 设置摘要
 */
data class SettingsSummary(
    val ttsEnabled: Boolean,
    val recognitionEnabled: Boolean,
    val offlineMode: Boolean,
    val autoLanguageDetection: Boolean,
    val speechRate: Float,
    val recognitionLanguage: String,
    val ttsLanguage: String,
    val cacheEnabled: Boolean,
    val noiseReduction: Boolean
)
