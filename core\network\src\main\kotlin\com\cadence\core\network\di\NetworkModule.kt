package com.cadence.core.network.di

import android.content.Context
import com.cadence.core.network.BuildConfig
import com.cadence.core.network.api.GeminiApiService
import com.cadence.core.network.api.SyncApiService
import com.cadence.core.network.interceptor.ApiKeyInterceptor
import com.cadence.core.network.interceptor.ErrorHandlingInterceptor
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 网络模块依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    /**
     * 提供JSON序列化配置
     */
    @Provides
    @Singleton
    fun provideJson(): Json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
        encodeDefaults = true
        isLenient = true
    }
    
    /**
     * 提供HTTP日志拦截器
     */
    @Provides
    @Singleton
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }
    
    /**
     * 提供HTTP缓存
     */
    @Provides
    @Singleton
    fun provideHttpCache(@ApplicationContext context: Context): okhttp3.Cache {
        val cacheDir = File(context.cacheDir, "http_cache")
        val cacheSize = 50L * 1024 * 1024 // 50MB
        return okhttp3.Cache(cacheDir, cacheSize)
    }

    /**
     * 提供OkHttp客户端
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(
        @ApplicationContext context: Context,
        apiKeyInterceptor: ApiKeyInterceptor,
        errorHandlingInterceptor: ErrorHandlingInterceptor,
        cacheInterceptor: com.cadence.core.network.interceptor.CacheInterceptor,
        requestPriorityInterceptor: com.cadence.core.network.interceptor.RequestPriorityInterceptor,
        loggingInterceptor: HttpLoggingInterceptor,
        cache: okhttp3.Cache
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .cache(cache)
            .addInterceptor(requestPriorityInterceptor)
            .addInterceptor(apiKeyInterceptor)
            .addInterceptor(cacheInterceptor)
            .addInterceptor(errorHandlingInterceptor)
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            // 安全配置
            .protocols(listOf(okhttp3.Protocol.HTTP_2, okhttp3.Protocol.HTTP_1_1))
            .build()
    }
    
    /**
     * 提供Gemini API的Retrofit实例
     */
    @Provides
    @Singleton
    @GeminiRetrofit
    fun provideGeminiRetrofit(
        okHttpClient: OkHttpClient,
        json: Json
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.GEMINI_API_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(
                json.asConverterFactory("application/json".toMediaType())
            )
            .build()
    }
    
    /**
     * 提供Gemini API服务
     */
    @Provides
    @Singleton
    fun provideGeminiApiService(
        @GeminiRetrofit retrofit: Retrofit
    ): GeminiApiService {
        return retrofit.create(GeminiApiService::class.java)
    }

    /**
     * 提供同步API的Retrofit实例
     */
    @Provides
    @Singleton
    @SyncRetrofit
    fun provideSyncRetrofit(
        okHttpClient: OkHttpClient,
        json: Json
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.SYNC_API_BASE_URL ?: "https://api.cadence.com/")
            .client(okHttpClient)
            .addConverterFactory(
                json.asConverterFactory("application/json".toMediaType())
            )
            .build()
    }

    /**
     * 提供同步API服务
     */
    @Provides
    @Singleton
    fun provideSyncApiService(
        @SyncRetrofit retrofit: Retrofit
    ): SyncApiService {
        return retrofit.create(SyncApiService::class.java)
    }

    /**
     * 提供网络监控器
     */
    @Provides
    @Singleton
    fun provideNetworkMonitor(
        @ApplicationContext context: Context
    ): com.cadence.core.network.monitor.NetworkMonitor {
        return com.cadence.core.network.monitor.NetworkMonitor(context)
    }

    /**
     * 提供网络性能监控器
     */
    @Provides
    @Singleton
    fun provideNetworkPerformanceMonitor(): com.cadence.core.network.monitor.NetworkPerformanceMonitor {
        return com.cadence.core.network.monitor.NetworkPerformanceMonitor()
    }

    /**
     * 提供下载管理器
     */
    @Provides
    @Singleton
    fun provideDownloadManager(
        @ApplicationContext context: Context,
        okHttpClient: OkHttpClient,
        networkMonitor: com.cadence.core.network.monitor.NetworkMonitor
    ): com.cadence.core.network.download.DownloadManager {
        return com.cadence.core.network.download.DownloadManager(context, okHttpClient, networkMonitor)
    }
}

/**
 * 同步API Retrofit限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class SyncRetrofit

/**
 * Gemini API Retrofit限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GeminiRetrofit
