package com.cadence.data.mapper

import com.cadence.core.database.entity.TagEntity
import com.cadence.domain.model.Tag

/**
 * 标签数据映射器
 * 负责标签数据库实体与领域模型之间的转换
 */

/**
 * 将数据库实体转换为领域模型
 */
fun TagEntity.toDomain(): Tag {
    return Tag(
        id = id,
        name = name,
        color = color,
        description = description,
        usageCount = usageCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 将领域模型转换为数据库实体
 */
fun Tag.toEntity(): TagEntity {
    return TagEntity(
        id = id,
        name = name,
        color = color,
        description = description,
        usageCount = usageCount,
        createdAt = createdAt,
        updatedAt = updatedAt
    )
}

/**
 * 批量转换数据库实体列表为领域模型列表
 */
fun List<TagEntity>.toDomainList(): List<Tag> {
    return map { it.toDomain() }
}

/**
 * 批量转换领域模型列表为数据库实体列表
 */
fun List<Tag>.toEntityList(): List<TagEntity> {
    return map { it.toEntity() }
}
