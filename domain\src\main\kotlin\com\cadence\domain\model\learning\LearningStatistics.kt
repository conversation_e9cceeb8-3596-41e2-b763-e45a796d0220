package com.cadence.domain.model.learning

/**
 * 学习统计领域模型
 * 提供用户学习数据的统计信息
 */
data class LearningStatistics(
    val userId: String,
    val totalWordsLearned: Int = 0,
    val totalStudySessions: Int = 0,
    val totalStudyTime: Long = 0, // 毫秒
    val currentStreak: Int = 0,
    val longestStreak: Int = 0,
    val averageAccuracy: Float = 0f,
    val wordsToReview: Int = 0,
    val masteredWords: Int = 0,
    val weeklyProgress: List<DailyProgress> = emptyList(),
    val categoryProgress: Map<WordCategory, CategoryProgress> = emptyMap(),
    val lastUpdated: Long = System.currentTimeMillis(),
    val wordProgressMap: Map<String, LearningProgress> = emptyMap()
) {
    /**
     * 获取指定单词的学习进度
     */
    fun getWordProgress(wordId: String): LearningProgress? {
        return wordProgressMap[wordId]
    }
} {
    /**
     * 计算平均每日学习时间（分钟）
     */
    val averageDailyStudyTime: Int
        get() = if (weeklyProgress.isNotEmpty()) {
            (weeklyProgress.sumOf { it.studyTimeMinutes } / weeklyProgress.size)
        } else 0
    
    /**
     * 计算学习效率（每分钟学习的单词数）
     */
    val learningEfficiency: Float
        get() = if (totalStudyTime > 0) {
            totalWordsLearned.toFloat() / (totalStudyTime / (1000 * 60))
        } else 0f
}

/**
 * 每日学习进度
 */
data class DailyProgress(
    val date: String, // YYYY-MM-DD格式
    val wordsLearned: Int = 0,
    val studyTimeMinutes: Int = 0,
    val sessionsCompleted: Int = 0,
    val accuracy: Float = 0f
)

/**
 * 分类学习进度
 */
data class CategoryProgress(
    val category: WordCategory,
    val totalWords: Int = 0,
    val masteredWords: Int = 0,
    val averageAccuracy: Float = 0f,
    val lastStudied: Long? = null
) {
    /**
     * 计算掌握率
     */
    val masteryRate: Float
        get() = if (totalWords > 0) masteredWords.toFloat() / totalWords else 0f
}