package com.cadence.core.logging

import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志格式化器
 * 负责将日志条目格式化为不同的输出格式
 */
@Singleton
class LogFormatter @Inject constructor() {
    
    companion object {
        private const val SENSITIVE_FIELD_MASK = "***"
        private val SENSITIVE_KEYS = setOf(
            "password", "token", "secret", "key", "auth", "credential",
            "pin", "ssn", "credit_card", "phone", "email", "address"
        )
    }
    
    private val json = Json { 
        prettyPrint = false
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    private val prettyJson = Json { 
        prettyPrint = true
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    private val isoDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault()).apply {
        timeZone = TimeZone.getTimeZone("UTC")
    }
    
    /**
     * 格式化日志条目为JSON格式
     */
    fun format(logEntry: LogEntry, format: LogFormat = LogFormat.JSON_COMPACT): String {
        return try {
            val sanitizedEntry = sanitizeLogEntry(logEntry)
            
            when (format) {
                LogFormat.JSON_COMPACT -> formatJsonCompact(sanitizedEntry)
                LogFormat.JSON_PRETTY -> formatJsonPretty(sanitizedEntry)
                LogFormat.PLAIN_TEXT -> formatPlainText(sanitizedEntry)
                LogFormat.CSV -> formatCsv(sanitizedEntry)
                LogFormat.LOGCAT -> formatLogcat(sanitizedEntry)
            }
        } catch (e: Exception) {
            Timber.e(e, "格式化日志条目失败")
            formatFallback(logEntry)
        }
    }
    
    /**
     * 格式化为紧凑JSON
     */
    private fun formatJsonCompact(logEntry: LogEntry): String {
        val logData = createLogData(logEntry)
        return json.encodeToString(logData)
    }
    
    /**
     * 格式化为美化JSON
     */
    private fun formatJsonPretty(logEntry: LogEntry): String {
        val logData = createLogData(logEntry)
        return prettyJson.encodeToString(logData)
    }
    
    /**
     * 格式化为纯文本
     */
    private fun formatPlainText(logEntry: LogEntry): String {
        val timestamp = dateFormat.format(Date(logEntry.timestamp))
        val level = logEntry.level.name.padEnd(7)
        val thread = "[${logEntry.threadName}]".padEnd(15)
        
        val contextStr = if (logEntry.context.isNotEmpty()) {
            " | Context: ${logEntry.context.entries.joinToString(", ") { "${it.key}=${it.value}" }}"
        } else {
            ""
        }
        
        val exceptionStr = logEntry.exception?.let { exception ->
            "\n  Exception: ${exception.type}: ${exception.message}\n  ${exception.stackTrace.replace("\n", "\n  ")}"
        } ?: ""
        
        return "$timestamp $level $thread ${logEntry.message}$contextStr$exceptionStr"
    }
    
    /**
     * 格式化为CSV
     */
    private fun formatCsv(logEntry: LogEntry): String {
        val timestamp = isoDateFormat.format(Date(logEntry.timestamp))
        val level = logEntry.level.name
        val thread = logEntry.threadName
        val message = escapeCsvField(logEntry.message)
        val context = escapeCsvField(logEntry.context.entries.joinToString(";") { "${it.key}=${it.value}" })
        val exceptionType = logEntry.exception?.type ?: ""
        val exceptionMessage = escapeCsvField(logEntry.exception?.message ?: "")
        
        return "$timestamp,$level,$thread,$message,$context,$exceptionType,$exceptionMessage"
    }
    
    /**
     * 格式化为Logcat格式
     */
    private fun formatLogcat(logEntry: LogEntry): String {
        val timestamp = dateFormat.format(Date(logEntry.timestamp))
        val level = when (logEntry.level) {
            LogLevel.VERBOSE -> "V"
            LogLevel.DEBUG -> "D"
            LogLevel.INFO -> "I"
            LogLevel.WARN -> "W"
            LogLevel.ERROR -> "E"
            LogLevel.FATAL -> "F"
        }
        val tag = "Cadence"
        
        val contextStr = if (logEntry.context.isNotEmpty()) {
            " [${logEntry.context.entries.joinToString(", ") { "${it.key}=${it.value}" }}]"
        } else {
            ""
        }
        
        return "$timestamp $level/$tag: ${logEntry.message}$contextStr"
    }
    
    /**
     * 创建日志数据对象
     */
    private fun createLogData(logEntry: LogEntry): LogData {
        return LogData(
            timestamp = isoDateFormat.format(Date(logEntry.timestamp)),
            timestampMs = logEntry.timestamp,
            level = logEntry.level.name,
            levelPriority = logEntry.level.priority,
            message = logEntry.message,
            context = logEntry.context,
            thread = ThreadInfo(
                name = logEntry.threadName,
                id = logEntry.threadId
            ),
            exception = logEntry.exception,
            session = SessionInfo(
                id = logEntry.sessionId,
                userId = logEntry.userId
            ),
            app = AppInfo(
                version = logEntry.appVersion,
                buildType = logEntry.buildType
            )
        )
    }
    
    /**
     * 清理敏感信息
     */
    private fun sanitizeLogEntry(logEntry: LogEntry): LogEntry {
        val sanitizedContext = sanitizeContext(logEntry.context)
        val sanitizedMessage = sanitizeMessage(logEntry.message)
        
        return logEntry.copy(
            message = sanitizedMessage,
            context = sanitizedContext
        )
    }
    
    /**
     * 清理上下文中的敏感信息
     */
    private fun sanitizeContext(context: Map<String, String>): Map<String, String> {
        return context.mapValues { (key, value) ->
            if (isSensitiveKey(key)) {
                SENSITIVE_FIELD_MASK
            } else {
                sanitizeValue(value)
            }
        }
    }
    
    /**
     * 清理消息中的敏感信息
     */
    private fun sanitizeMessage(message: String): String {
        var sanitized = message
        
        // 清理常见的敏感信息模式
        sanitized = sanitized.replace(Regex("password[\\s]*[:=][\\s]*\\S+", RegexOption.IGNORE_CASE), "password=***")
        sanitized = sanitized.replace(Regex("token[\\s]*[:=][\\s]*\\S+", RegexOption.IGNORE_CASE), "token=***")
        sanitized = sanitized.replace(Regex("key[\\s]*[:=][\\s]*\\S+", RegexOption.IGNORE_CASE), "key=***")
        sanitized = sanitized.replace(Regex("secret[\\s]*[:=][\\s]*\\S+", RegexOption.IGNORE_CASE), "secret=***")
        
        // 清理邮箱地址
        sanitized = sanitized.replace(Regex("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"), "***@***.***")
        
        // 清理电话号码
        sanitized = sanitized.replace(Regex("\\b\\d{3}-\\d{3}-\\d{4}\\b"), "***-***-****")
        sanitized = sanitized.replace(Regex("\\b\\d{10}\\b"), "**********")
        
        return sanitized
    }
    
    /**
     * 清理值
     */
    private fun sanitizeValue(value: String): String {
        // 如果值看起来像密码、令牌等，进行清理
        return when {
            value.length > 20 && value.matches(Regex("[a-zA-Z0-9+/=]+")) -> SENSITIVE_FIELD_MASK // Base64
            value.length > 30 && value.matches(Regex("[a-fA-F0-9]+")) -> SENSITIVE_FIELD_MASK // Hex
            value.startsWith("Bearer ") -> "Bearer ***"
            value.startsWith("Basic ") -> "Basic ***"
            else -> value
        }
    }
    
    /**
     * 检查是否为敏感键
     */
    private fun isSensitiveKey(key: String): Boolean {
        val lowerKey = key.lowercase()
        return SENSITIVE_KEYS.any { sensitiveKey ->
            lowerKey.contains(sensitiveKey)
        }
    }
    
    /**
     * 转义CSV字段
     */
    private fun escapeCsvField(field: String): String {
        return if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
            "\"${field.replace("\"", "\"\"")}\"" 
        } else {
            field
        }
    }
    
    /**
     * 后备格式化方法
     */
    private fun formatFallback(logEntry: LogEntry): String {
        val timestamp = try {
            dateFormat.format(Date(logEntry.timestamp))
        } catch (e: Exception) {
            logEntry.timestamp.toString()
        }
        
        return "$timestamp [${logEntry.level.name}] ${logEntry.threadName}: ${logEntry.message}"
    }
    
    /**
     * 获取CSV头部
     */
    fun getCsvHeader(): String {
        return "timestamp,level,thread,message,context,exception_type,exception_message"
    }
    
    /**
     * 批量格式化日志条目
     */
    fun formatBatch(logEntries: List<LogEntry>, format: LogFormat = LogFormat.JSON_COMPACT): List<String> {
        return logEntries.map { logEntry ->
            format(logEntry, format)
        }
    }
    
    /**
     * 格式化日志条目为搜索友好的格式
     */
    fun formatForSearch(logEntry: LogEntry): String {
        val sanitizedEntry = sanitizeLogEntry(logEntry)
        val timestamp = dateFormat.format(Date(sanitizedEntry.timestamp))
        
        val searchableText = buildString {
            append("$timestamp ")
            append("${sanitizedEntry.level.name} ")
            append("${sanitizedEntry.threadName} ")
            append("${sanitizedEntry.message} ")
            
            sanitizedEntry.context.forEach { (key, value) ->
                append("$key:$value ")
            }
            
            sanitizedEntry.exception?.let { exception ->
                append("${exception.type} ${exception.message} ")
            }
        }
        
        return searchableText.trim()
    }
}

/**
 * 日志格式枚举
 */
enum class LogFormat {
    JSON_COMPACT,   // 紧凑JSON格式
    JSON_PRETTY,    // 美化JSON格式
    PLAIN_TEXT,     // 纯文本格式
    CSV,            // CSV格式
    LOGCAT          // Logcat格式
}

/**
 * 日志数据类（用于JSON序列化）
 */
@kotlinx.serialization.Serializable
data class LogData(
    val timestamp: String,
    val timestampMs: Long,
    val level: String,
    val levelPriority: Int,
    val message: String,
    val context: Map<String, String>,
    val thread: ThreadInfo,
    val exception: ExceptionInfo? = null,
    val session: SessionInfo,
    val app: AppInfo
)

@kotlinx.serialization.Serializable
data class ThreadInfo(
    val name: String,
    val id: Long
)

@kotlinx.serialization.Serializable
data class SessionInfo(
    val id: String,
    val userId: String? = null
)

@kotlinx.serialization.Serializable
data class AppInfo(
    val version: String,
    val buildType: String
)
