package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 错误分析器
 * 负责分析错误模式、趋势和影响
 */
@Singleton
class ErrorAnalyzer @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val ANALYSIS_WINDOW_HOURS = 24
        private const val PATTERN_THRESHOLD = 3
        private const val HIGH_FREQUENCY_THRESHOLD = 10
        private const val CRITICAL_FREQUENCY_THRESHOLD = 20
    }
    
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    
    // 错误历史记录
    private val errorHistory = mutableListOf<ErrorRecord>()
    private val errorPatterns = ConcurrentHashMap<String, ErrorPattern>()
    private val errorTrends = ConcurrentHashMap<String, ErrorTrend>()
    
    // 分析缓存
    private val analysisCache = ConcurrentHashMap<String, ErrorAnalysis>()
    
    init {
        // 启动定期分析任务
        startPeriodicAnalysis()
        Timber.d("错误分析器已初始化")
    }
    
    /**
     * 分析错误
     */
    suspend fun analyzeError(errorReport: ErrorReport): ErrorAnalysis = withContext(Dispatchers.Default) {
        try {
            val errorSignature = generateErrorSignature(errorReport)
            
            // 检查缓存
            analysisCache[errorSignature]?.let { cachedAnalysis ->
                if (System.currentTimeMillis() - cachedAnalysis.timestamp < 300000L) { // 5分钟缓存
                    return@withContext cachedAnalysis
                }
            }
            
            // 记录错误
            recordError(errorReport)
            
            // 执行分析
            val analysis = performAnalysis(errorReport, errorSignature)
            
            // 缓存结果
            analysisCache[errorSignature] = analysis
            
            analysis
        } catch (e: Exception) {
            Timber.e(e, "错误分析失败")
            createDefaultAnalysis(errorReport)
        }
    }
    
    /**
     * 执行分析
     */
    private fun performAnalysis(errorReport: ErrorReport, errorSignature: String): ErrorAnalysis {
        val frequency = calculateErrorFrequency(errorSignature)
        val pattern = detectErrorPattern(errorReport)
        val trend = analyzeErrorTrend(errorSignature)
        val impact = assessErrorImpact(errorReport, frequency)
        val riskScore = calculateRiskScore(errorReport, frequency, impact)
        val recommendations = generateRecommendations(errorReport, pattern, trend)
        val similarErrors = findSimilarErrors(errorReport)
        val rootCause = analyzeRootCause(errorReport, pattern)
        
        return ErrorAnalysis(
            timestamp = System.currentTimeMillis(),
            errorSignature = errorSignature,
            frequency = frequency,
            pattern = pattern,
            trend = trend,
            impact = impact,
            riskScore = riskScore,
            recommendations = recommendations,
            similarErrors = similarErrors,
            rootCause = rootCause,
            confidence = calculateConfidence(frequency, pattern)
        )
    }
    
    /**
     * 生成错误签名
     */
    private fun generateErrorSignature(errorReport: ErrorReport): String {
        val exceptionType = errorReport.exception.type
        val message = errorReport.exception.message.take(100) // 限制长度
        val stackTraceHash = errorReport.exception.stackTrace.hashCode()
        
        return "${exceptionType}_${message.hashCode()}_${stackTraceHash}".replace(" ", "_")
    }
    
    /**
     * 记录错误
     */
    private fun recordError(errorReport: ErrorReport) {
        val errorRecord = ErrorRecord(
            timestamp = errorReport.timestamp,
            signature = generateErrorSignature(errorReport),
            severity = errorReport.severity,
            context = errorReport.context,
            deviceInfo = errorReport.deviceInfo,
            appVersion = errorReport.appInfo.versionName
        )
        
        synchronized(errorHistory) {
            errorHistory.add(errorRecord)
            
            // 保持历史记录在合理范围内
            val cutoffTime = System.currentTimeMillis() - (ANALYSIS_WINDOW_HOURS * 60 * 60 * 1000L)
            errorHistory.removeAll { it.timestamp < cutoffTime }
        }
    }
    
    /**
     * 计算错误频率
     */
    private fun calculateErrorFrequency(errorSignature: String): ErrorFrequency {
        val now = System.currentTimeMillis()
        val oneHourAgo = now - (60 * 60 * 1000L)
        val oneDayAgo = now - (24 * 60 * 60 * 1000L)
        val oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000L)
        
        synchronized(errorHistory) {
            val matchingErrors = errorHistory.filter { it.signature == errorSignature }
            
            val lastHour = matchingErrors.count { it.timestamp > oneHourAgo }
            val lastDay = matchingErrors.count { it.timestamp > oneDayAgo }
            val lastWeek = matchingErrors.count { it.timestamp > oneWeekAgo }
            val total = matchingErrors.size
            
            return ErrorFrequency(
                lastHour = lastHour,
                lastDay = lastDay,
                lastWeek = lastWeek,
                total = total,
                firstOccurrence = matchingErrors.minOfOrNull { it.timestamp } ?: now,
                lastOccurrence = matchingErrors.maxOfOrNull { it.timestamp } ?: now
            )
        }
    }
    
    /**
     * 检测错误模式
     */
    private fun detectErrorPattern(errorReport: ErrorReport): ErrorPattern {
        val signature = generateErrorSignature(errorReport)
        
        return errorPatterns.getOrPut(signature) {
            val relatedErrors = synchronized(errorHistory) {
                errorHistory.filter { it.signature == signature }
            }
            
            val devicePatterns = analyzeDevicePatterns(relatedErrors)
            val timePatterns = analyzeTimePatterns(relatedErrors)
            val contextPatterns = analyzeContextPatterns(relatedErrors)
            val versionPatterns = analyzeVersionPatterns(relatedErrors)
            
            ErrorPattern(
                signature = signature,
                devicePatterns = devicePatterns,
                timePatterns = timePatterns,
                contextPatterns = contextPatterns,
                versionPatterns = versionPatterns,
                isRecurring = relatedErrors.size >= PATTERN_THRESHOLD,
                confidence = calculatePatternConfidence(relatedErrors)
            )
        }
    }
    
    /**
     * 分析错误趋势
     */
    private fun analyzeErrorTrend(errorSignature: String): ErrorTrend {
        return errorTrends.getOrPut(errorSignature) {
            val relatedErrors = synchronized(errorHistory) {
                errorHistory.filter { it.signature == errorSignature }
                    .sortedBy { it.timestamp }
            }
            
            if (relatedErrors.size < 2) {
                return@getOrPut ErrorTrend(
                    direction = TrendDirection.STABLE,
                    rate = 0.0,
                    confidence = 0.0
                )
            }
            
            val direction = calculateTrendDirection(relatedErrors)
            val rate = calculateTrendRate(relatedErrors)
            val confidence = calculateTrendConfidence(relatedErrors)
            
            ErrorTrend(
                direction = direction,
                rate = rate,
                confidence = confidence
            )
        }
    }
    
    /**
     * 评估错误影响
     */
    private fun assessErrorImpact(errorReport: ErrorReport, frequency: ErrorFrequency): ErrorImpact {
        val severityWeight = when (errorReport.severity) {
            ErrorSeverity.LOW -> 1.0
            ErrorSeverity.MEDIUM -> 2.0
            ErrorSeverity.HIGH -> 4.0
            ErrorSeverity.CRITICAL -> 8.0
        }
        
        val frequencyWeight = when {
            frequency.lastHour > CRITICAL_FREQUENCY_THRESHOLD -> 4.0
            frequency.lastHour > HIGH_FREQUENCY_THRESHOLD -> 2.0
            frequency.lastDay > HIGH_FREQUENCY_THRESHOLD -> 1.5
            else -> 1.0
        }
        
        val userImpact = calculateUserImpact(errorReport)
        val businessImpact = calculateBusinessImpact(errorReport, frequency)
        val technicalImpact = calculateTechnicalImpact(errorReport)
        
        val overallScore = (severityWeight * frequencyWeight * userImpact * businessImpact * technicalImpact) / 32.0
        
        return ErrorImpact(
            userImpact = userImpact,
            businessImpact = businessImpact,
            technicalImpact = technicalImpact,
            overallScore = overallScore.coerceIn(0.0, 10.0)
        )
    }
    
    /**
     * 计算风险评分
     */
    private fun calculateRiskScore(
        errorReport: ErrorReport,
        frequency: ErrorFrequency,
        impact: ErrorImpact
    ): Double {
        val severityScore = when (errorReport.severity) {
            ErrorSeverity.LOW -> 2.0
            ErrorSeverity.MEDIUM -> 4.0
            ErrorSeverity.HIGH -> 7.0
            ErrorSeverity.CRITICAL -> 10.0
        }
        
        val frequencyScore = when {
            frequency.lastHour > CRITICAL_FREQUENCY_THRESHOLD -> 10.0
            frequency.lastHour > HIGH_FREQUENCY_THRESHOLD -> 7.0
            frequency.lastDay > HIGH_FREQUENCY_THRESHOLD -> 5.0
            frequency.total > PATTERN_THRESHOLD -> 3.0
            else -> 1.0
        }
        
        val impactScore = impact.overallScore
        
        // 加权平均
        val riskScore = (severityScore * 0.4 + frequencyScore * 0.3 + impactScore * 0.3)
        
        return riskScore.coerceIn(0.0, 10.0)
    }
    
    /**
     * 生成建议
     */
    private fun generateRecommendations(
        errorReport: ErrorReport,
        pattern: ErrorPattern,
        trend: ErrorTrend
    ): List<String> {
        val recommendations = mutableListOf<String>()
        
        // 基于严重性的建议
        when (errorReport.severity) {
            ErrorSeverity.CRITICAL -> {
                recommendations.add("立即修复：这是一个严重错误，可能导致应用崩溃")
                recommendations.add("考虑发布热修复版本")
            }
            ErrorSeverity.HIGH -> {
                recommendations.add("高优先级修复：这个错误严重影响用户体验")
                recommendations.add("在下一个版本中修复")
            }
            ErrorSeverity.MEDIUM -> {
                recommendations.add("中等优先级：安排在适当的版本中修复")
            }
            ErrorSeverity.LOW -> {
                recommendations.add("低优先级：可以在维护版本中修复")
            }
        }
        
        // 基于模式的建议
        if (pattern.isRecurring) {
            recommendations.add("这是一个重复出现的错误，需要深入调查根本原因")
        }
        
        // 基于趋势的建议
        when (trend.direction) {
            TrendDirection.INCREASING -> {
                recommendations.add("错误频率正在增加，需要紧急关注")
            }
            TrendDirection.DECREASING -> {
                recommendations.add("错误频率正在减少，但仍需监控")
            }
            TrendDirection.STABLE -> {
                recommendations.add("错误频率稳定，按计划修复")
            }
        }
        
        // 基于设备模式的建议
        if (pattern.devicePatterns.isNotEmpty()) {
            recommendations.add("错误在特定设备上更频繁，考虑设备兼容性问题")
        }
        
        return recommendations
    }
    
    /**
     * 查找相似错误
     */
    private fun findSimilarErrors(errorReport: ErrorReport): List<String> {
        val currentSignature = generateErrorSignature(errorReport)
        val exceptionType = errorReport.exception.type
        
        return synchronized(errorHistory) {
            errorHistory
                .filter { it.signature != currentSignature }
                .filter { record ->
                    // 查找相同异常类型的错误
                    errorHistory.any { it.signature.startsWith(exceptionType) }
                }
                .map { it.signature }
                .distinct()
                .take(5)
        }
    }
    
    /**
     * 分析根本原因
     */
    private fun analyzeRootCause(errorReport: ErrorReport, pattern: ErrorPattern): RootCauseAnalysis {
        val possibleCauses = mutableListOf<String>()
        val confidence = mutableListOf<Double>()
        
        // 基于异常类型分析
        when {
            errorReport.exception.type.contains("Network") -> {
                possibleCauses.add("网络连接问题")
                confidence.add(0.8)
            }
            errorReport.exception.type.contains("OutOfMemory") -> {
                possibleCauses.add("内存不足")
                confidence.add(0.9)
            }
            errorReport.exception.type.contains("NullPointer") -> {
                possibleCauses.add("空指针引用")
                confidence.add(0.7)
            }
            errorReport.exception.type.contains("IllegalState") -> {
                possibleCauses.add("状态管理错误")
                confidence.add(0.6)
            }
        }
        
        // 基于模式分析
        if (pattern.devicePatterns.isNotEmpty()) {
            possibleCauses.add("设备兼容性问题")
            confidence.add(0.6)
        }
        
        if (pattern.versionPatterns.isNotEmpty()) {
            possibleCauses.add("版本相关问题")
            confidence.add(0.7)
        }
        
        return RootCauseAnalysis(
            possibleCauses = possibleCauses.zip(confidence).toMap(),
            primaryCause = possibleCauses.firstOrNull(),
            confidence = confidence.maxOrNull() ?: 0.0
        )
    }
    
    // 辅助分析方法
    private fun analyzeDevicePatterns(errors: List<ErrorRecord>): List<String> {
        return errors.groupBy { "${it.deviceInfo.manufacturer} ${it.deviceInfo.model}" }
            .filter { it.value.size >= 2 }
            .keys.toList()
    }
    
    private fun analyzeTimePatterns(errors: List<ErrorRecord>): List<String> {
        val patterns = mutableListOf<String>()
        
        // 分析小时模式
        val hourCounts = errors.groupBy { 
            java.util.Calendar.getInstance().apply { 
                timeInMillis = it.timestamp 
            }.get(java.util.Calendar.HOUR_OF_DAY) 
        }
        
        hourCounts.filter { it.value.size >= 2 }.keys.forEach { hour ->
            patterns.add("频繁发生在${hour}点")
        }
        
        return patterns
    }
    
    private fun analyzeContextPatterns(errors: List<ErrorRecord>): List<String> {
        return errors.groupBy { it.context }
            .filter { it.value.size >= 2 }
            .keys.toList()
    }
    
    private fun analyzeVersionPatterns(errors: List<ErrorRecord>): List<String> {
        return errors.groupBy { it.appVersion }
            .filter { it.value.size >= 2 }
            .keys.toList()
    }
    
    private fun calculatePatternConfidence(errors: List<ErrorRecord>): Double {
        return when {
            errors.size >= 10 -> 0.9
            errors.size >= 5 -> 0.7
            errors.size >= 3 -> 0.5
            else -> 0.3
        }
    }
    
    private fun calculateTrendDirection(errors: List<ErrorRecord>): TrendDirection {
        if (errors.size < 3) return TrendDirection.STABLE
        
        val recent = errors.takeLast(errors.size / 2)
        val earlier = errors.take(errors.size / 2)
        
        val recentRate = recent.size.toDouble() / (recent.maxOf { it.timestamp } - recent.minOf { it.timestamp })
        val earlierRate = earlier.size.toDouble() / (earlier.maxOf { it.timestamp } - earlier.minOf { it.timestamp })
        
        return when {
            recentRate > earlierRate * 1.2 -> TrendDirection.INCREASING
            recentRate < earlierRate * 0.8 -> TrendDirection.DECREASING
            else -> TrendDirection.STABLE
        }
    }
    
    private fun calculateTrendRate(errors: List<ErrorRecord>): Double {
        if (errors.size < 2) return 0.0
        
        val timeSpan = errors.last().timestamp - errors.first().timestamp
        return if (timeSpan > 0) {
            errors.size.toDouble() / (timeSpan / (60 * 60 * 1000.0)) // 每小时错误数
        } else {
            0.0
        }
    }
    
    private fun calculateTrendConfidence(errors: List<ErrorRecord>): Double {
        return when {
            errors.size >= 20 -> 0.9
            errors.size >= 10 -> 0.7
            errors.size >= 5 -> 0.5
            else -> 0.3
        }
    }
    
    private fun calculateUserImpact(errorReport: ErrorReport): Double {
        return when (errorReport.severity) {
            ErrorSeverity.CRITICAL -> 4.0
            ErrorSeverity.HIGH -> 3.0
            ErrorSeverity.MEDIUM -> 2.0
            ErrorSeverity.LOW -> 1.0
        }
    }
    
    private fun calculateBusinessImpact(errorReport: ErrorReport, frequency: ErrorFrequency): Double {
        val baseImpact = when (errorReport.severity) {
            ErrorSeverity.CRITICAL -> 3.0
            ErrorSeverity.HIGH -> 2.5
            ErrorSeverity.MEDIUM -> 2.0
            ErrorSeverity.LOW -> 1.0
        }
        
        val frequencyMultiplier = when {
            frequency.lastHour > HIGH_FREQUENCY_THRESHOLD -> 2.0
            frequency.lastDay > HIGH_FREQUENCY_THRESHOLD -> 1.5
            else -> 1.0
        }
        
        return baseImpact * frequencyMultiplier
    }
    
    private fun calculateTechnicalImpact(errorReport: ErrorReport): Double {
        return when {
            errorReport.exception.type.contains("OutOfMemory") -> 4.0
            errorReport.exception.type.contains("Security") -> 4.0
            errorReport.exception.type.contains("Database") -> 3.0
            errorReport.exception.type.contains("Network") -> 2.0
            else -> 1.5
        }
    }
    
    private fun calculateConfidence(frequency: ErrorFrequency, pattern: ErrorPattern): Double {
        val frequencyConfidence = when {
            frequency.total >= 10 -> 0.9
            frequency.total >= 5 -> 0.7
            frequency.total >= 3 -> 0.5
            else -> 0.3
        }
        
        return (frequencyConfidence + pattern.confidence) / 2.0
    }
    
    private fun createDefaultAnalysis(errorReport: ErrorReport): ErrorAnalysis {
        return ErrorAnalysis(
            timestamp = System.currentTimeMillis(),
            errorSignature = generateErrorSignature(errorReport),
            frequency = ErrorFrequency(0, 1, 1, 1, System.currentTimeMillis(), System.currentTimeMillis()),
            pattern = ErrorPattern("", emptyList(), emptyList(), emptyList(), emptyList(), false, 0.0),
            trend = ErrorTrend(TrendDirection.STABLE, 0.0, 0.0),
            impact = ErrorImpact(1.0, 1.0, 1.0, 1.0),
            riskScore = 1.0,
            recommendations = listOf("需要更多数据进行分析"),
            similarErrors = emptyList(),
            rootCause = RootCauseAnalysis(emptyMap(), null, 0.0),
            confidence = 0.1
        )
    }
    
    /**
     * 启动定期分析任务
     */
    private fun startPeriodicAnalysis() {
        scope.launch {
            while (isActive) {
                try {
                    delay(3600000L) // 每小时执行一次
                    performPeriodicAnalysis()
                } catch (e: Exception) {
                    Timber.e(e, "定期分析任务失败")
                }
            }
        }
    }
    
    /**
     * 执行定期分析
     */
    private suspend fun performPeriodicAnalysis() = withContext(Dispatchers.Default) {
        try {
            // 清理过期的缓存
            val cutoffTime = System.currentTimeMillis() - (24 * 60 * 60 * 1000L)
            analysisCache.entries.removeIf { it.value.timestamp < cutoffTime }
            
            // 更新错误模式
            updateErrorPatterns()
            
            // 更新错误趋势
            updateErrorTrends()
            
            Timber.d("定期分析完成")
            
        } catch (e: Exception) {
            Timber.e(e, "定期分析失败")
        }
    }
    
    private fun updateErrorPatterns() {
        // 重新计算错误模式
        errorPatterns.clear()
    }
    
    private fun updateErrorTrends() {
        // 重新计算错误趋势
        errorTrends.clear()
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        errorHistory.clear()
        errorPatterns.clear()
        errorTrends.clear()
        analysisCache.clear()
        Timber.d("错误分析器已清理")
    }
}

// 数据类定义
data class ErrorRecord(
    val timestamp: Long,
    val signature: String,
    val severity: ErrorSeverity,
    val context: String,
    val deviceInfo: DeviceInfo,
    val appVersion: String
)

data class ErrorAnalysis(
    val timestamp: Long,
    val errorSignature: String,
    val frequency: ErrorFrequency,
    val pattern: ErrorPattern,
    val trend: ErrorTrend,
    val impact: ErrorImpact,
    val riskScore: Double,
    val recommendations: List<String>,
    val similarErrors: List<String>,
    val rootCause: RootCauseAnalysis,
    val confidence: Double
)

data class ErrorFrequency(
    val lastHour: Int,
    val lastDay: Int,
    val lastWeek: Int,
    val total: Int,
    val firstOccurrence: Long,
    val lastOccurrence: Long
)

data class ErrorPattern(
    val signature: String,
    val devicePatterns: List<String>,
    val timePatterns: List<String>,
    val contextPatterns: List<String>,
    val versionPatterns: List<String>,
    val isRecurring: Boolean,
    val confidence: Double
)

data class ErrorTrend(
    val direction: TrendDirection,
    val rate: Double,
    val confidence: Double
)

data class ErrorImpact(
    val userImpact: Double,
    val businessImpact: Double,
    val technicalImpact: Double,
    val overallScore: Double
)

data class RootCauseAnalysis(
    val possibleCauses: Map<String, Double>,
    val primaryCause: String?,
    val confidence: Double
)

enum class TrendDirection {
    INCREASING,
    DECREASING,
    STABLE
}
