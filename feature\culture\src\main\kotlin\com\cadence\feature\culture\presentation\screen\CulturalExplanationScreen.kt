package com.cadence.feature.culture.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.domain.model.culture.CulturalContext
import com.cadence.feature.culture.presentation.component.*
import com.cadence.feature.culture.presentation.viewmodel.CulturalExplanationViewModel

/**
 * 文化解释主界面
 * 显示单词的文化背景信息和解释
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalExplanationScreen(
    word: String,
    sourceLanguage: String,
    targetLanguage: String,
    onNavigateBack: () -> Unit,
    onNavigateToDetail: (String) -> Unit,
    onNavigateToRecommendations: () -> Unit,
    viewModel: CulturalExplanationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 初始化加载数据
    LaunchedEffect(word, sourceLanguage, targetLanguage) {
        viewModel.loadCulturalContexts(word, sourceLanguage, targetLanguage)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "文化背景解释",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = onNavigateToRecommendations) {
                        Icon(
                            imageVector = Icons.Default.Lightbulb,
                            contentDescription = "文化推荐"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.error != null -> {
                    ErrorMessage(
                        error = uiState.error,
                        onRetry = { 
                            viewModel.loadCulturalContexts(word, sourceLanguage, targetLanguage) 
                        },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.culturalContexts.isEmpty() -> {
                    EmptyStateMessage(
                        word = word,
                        onRequestExplanation = { viewModel.requestCulturalExplanation(word) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                else -> {
                    CulturalExplanationContent(
                        word = word,
                        culturalContexts = uiState.culturalContexts,
                        onContextClick = onNavigateToDetail,
                        onBookmarkClick = { contextId -> 
                            viewModel.toggleBookmark(contextId) 
                        },
                        onMarkAsLearned = { contextId -> 
                            viewModel.markAsLearned(contextId) 
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 文化解释内容区域
 */
@Composable
private fun CulturalExplanationContent(
    word: String,
    culturalContexts: List<CulturalContext>,
    onContextClick: (String) -> Unit,
    onBookmarkClick: (String) -> Unit,
    onMarkAsLearned: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 单词标题卡片
        item {
            WordHeaderCard(
                word = word,
                contextCount = culturalContexts.size
            )
        }
        
        // 文化背景概览
        item {
            CulturalOverviewCard(
                culturalContexts = culturalContexts
            )
        }
        
        // 文化背景详细列表
        items(
            items = culturalContexts,
            key = { it.id }
        ) { context ->
            CulturalContextCard(
                context = context,
                onClick = { onContextClick(context.id) },
                onBookmarkClick = { onBookmarkClick(context.id) },
                onMarkAsLearned = { onMarkAsLearned(context.id) }
            )
        }
        
        // 底部间距
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 加载指示器
 */
@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator()
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "正在加载文化背景信息...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 错误消息
 */
@Composable
private fun ErrorMessage(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}

/**
 * 空状态消息
 */
@Composable
private fun EmptyStateMessage(
    word: String,
    onRequestExplanation: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Psychology,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(64.dp)
        )
        Spacer(modifier = Modifier.height(24.dp))
        Text(
            text = "暂无文化背景信息",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "「$word」的文化背景信息正在完善中",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(24.dp))
        OutlinedButton(
            onClick = onRequestExplanation
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("申请添加文化解释")
        }
    }
}