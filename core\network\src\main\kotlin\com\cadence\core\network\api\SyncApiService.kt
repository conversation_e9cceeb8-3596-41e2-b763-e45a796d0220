package com.cadence.core.network.api

import com.cadence.core.network.dto.*
import retrofit2.Response
import retrofit2.http.*

/**
 * 同步API服务接口
 * 定义与云端同步服务的交互方法
 */
interface SyncApiService {
    
    // ========== 翻译历史同步 ==========
    
    /**
     * 上传翻译历史
     * @param request 翻译历史同步请求
     */
    @POST("sync/translations/upload")
    suspend fun uploadTranslationHistory(
        @Body request: TranslationHistorySyncRequest
    ): Response<TranslationHistorySyncResponse>
    
    /**
     * 下载翻译历史
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param lastSyncTime 上次同步时间
     */
    @GET("sync/translations/download")
    suspend fun downloadTranslationHistory(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?,
        @Query("lastSyncTime") lastSyncTime: Long
    ): Response<TranslationHistorySyncResponse>
    
    /**
     * 同步翻译历史（双向同步）
     * @param request 翻译历史同步请求
     */
    @POST("sync/translations")
    suspend fun syncTranslationHistory(
        @Body request: TranslationHistorySyncRequest
    ): Response<TranslationHistorySyncResponse>
    
    // ========== 标签同步 ==========
    
    /**
     * 上传标签
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param tags 标签列表
     */
    @POST("sync/tags/upload")
    suspend fun uploadTags(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?,
        @Body tags: List<TagDto>
    ): Response<GenericSyncResponse>
    
    /**
     * 下载标签
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param lastSyncTime 上次同步时间
     */
    @GET("sync/tags/download")
    suspend fun downloadTags(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?,
        @Query("lastSyncTime") lastSyncTime: Long
    ): Response<List<TagDto>>
    
    // ========== 用户偏好同步 ==========
    
    /**
     * 上传用户偏好
     * @param request 用户偏好同步请求
     */
    @POST("sync/preferences/upload")
    suspend fun uploadUserPreferences(
        @Body request: UserPreferencesSyncRequest
    ): Response<UserPreferencesSyncResponse>
    
    /**
     * 下载用户偏好
     * @param deviceId 设备ID
     * @param userId 用户ID
     */
    @GET("sync/preferences/download")
    suspend fun downloadUserPreferences(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?
    ): Response<UserPreferencesSyncResponse>
    
    /**
     * 同步用户偏好（双向同步）
     * @param request 用户偏好同步请求
     */
    @POST("sync/preferences")
    suspend fun syncUserPreferences(
        @Body request: UserPreferencesSyncRequest
    ): Response<UserPreferencesSyncResponse>
    
    // ========== 设备管理 ==========
    
    /**
     * 注册设备
     * @param request 设备注册请求
     */
    @POST("sync/devices/register")
    suspend fun registerDevice(
        @Body request: DeviceRegistrationRequest
    ): Response<DeviceRegistrationResponse>
    
    /**
     * 获取设备列表
     * @param userId 用户ID
     */
    @GET("sync/devices")
    suspend fun getDeviceList(
        @Query("userId") userId: String?
    ): Response<DeviceListResponse>
    
    /**
     * 注销设备
     * @param deviceId 设备ID
     * @param userId 用户ID
     */
    @DELETE("sync/devices/{deviceId}")
    suspend fun unregisterDevice(
        @Path("deviceId") deviceId: String,
        @Query("userId") userId: String?
    ): Response<GenericSyncResponse>
    
    /**
     * 更新设备活跃时间
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param timestamp 时间戳
     */
    @PUT("sync/devices/{deviceId}/heartbeat")
    suspend fun updateDeviceHeartbeat(
        @Path("deviceId") deviceId: String,
        @Query("userId") userId: String?,
        @Query("timestamp") timestamp: Long
    ): Response<GenericSyncResponse>
    
    // ========== 同步状态查询 ==========
    
    /**
     * 获取同步状态
     * @param deviceId 设备ID
     * @param userId 用户ID
     */
    @GET("sync/status")
    suspend fun getSyncStatus(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?
    ): Response<SyncStatusResponse>
    
    /**
     * 获取云端数据大小
     * @param userId 用户ID
     */
    @GET("sync/data-size")
    suspend fun getCloudDataSize(
        @Query("userId") userId: String?
    ): Response<GenericSyncResponse>
    
    // ========== 数据备份与恢复 ==========
    
    /**
     * 备份所有数据
     * @param request 数据备份请求
     */
    @POST("sync/backup")
    suspend fun backupAllData(
        @Body request: DataBackupRequest
    ): Response<GenericSyncResponse>
    
    /**
     * 恢复所有数据
     * @param deviceId 设备ID
     * @param userId 用户ID
     * @param backupTime 备份时间（可选，不指定则恢复最新备份）
     */
    @GET("sync/restore")
    suspend fun restoreAllData(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?,
        @Query("backupTime") backupTime: Long? = null
    ): Response<DataRestoreResponse>
    
    /**
     * 获取备份列表
     * @param userId 用户ID
     */
    @GET("sync/backups")
    suspend fun getBackupList(
        @Query("userId") userId: String?
    ): Response<List<BackupInfoDto>>
    
    /**
     * 删除指定备份
     * @param backupId 备份ID
     * @param userId 用户ID
     */
    @DELETE("sync/backups/{backupId}")
    suspend fun deleteBackup(
        @Path("backupId") backupId: String,
        @Query("userId") userId: String?
    ): Response<GenericSyncResponse>
    
    // ========== 冲突解决 ==========
    
    /**
     * 获取同步冲突列表
     * @param deviceId 设备ID
     * @param userId 用户ID
     */
    @GET("sync/conflicts")
    suspend fun getSyncConflicts(
        @Query("deviceId") deviceId: String,
        @Query("userId") userId: String?
    ): Response<List<SyncConflictDto>>
    
    /**
     * 解决同步冲突
     * @param conflictId 冲突ID
     * @param resolution 解决方案
     * @param userId 用户ID
     */
    @POST("sync/conflicts/{conflictId}/resolve")
    suspend fun resolveConflict(
        @Path("conflictId") conflictId: String,
        @Query("resolution") resolution: String,
        @Query("userId") userId: String?
    ): Response<GenericSyncResponse>
    
    // ========== 数据清理 ==========
    
    /**
     * 删除云端所有数据
     * @param userId 用户ID
     * @param confirmToken 确认令牌
     */
    @DELETE("sync/data")
    suspend fun deleteCloudData(
        @Query("userId") userId: String?,
        @Query("confirmToken") confirmToken: String
    ): Response<GenericSyncResponse>
    
    /**
     * 清理过期数据
     * @param userId 用户ID
     * @param olderThan 清理指定时间之前的数据
     */
    @DELETE("sync/cleanup")
    suspend fun cleanupExpiredData(
        @Query("userId") userId: String?,
        @Query("olderThan") olderThan: Long
    ): Response<GenericSyncResponse>
}

/**
 * 备份信息数据传输对象
 */
@kotlinx.serialization.Serializable
data class BackupInfoDto(
    val backupId: String,
    val backupTime: Long,
    val dataSize: Long,
    val itemCount: Int,
    val appVersion: String,
    val deviceName: String
)
