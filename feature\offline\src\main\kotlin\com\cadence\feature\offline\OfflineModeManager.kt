package com.cadence.feature.offline

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import com.cadence.core.logging.StructuredLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线模式管理器
 * 负责检测网络状态、管理离线功能和用户提示
 */
@Singleton
class OfflineModeManager @Inject constructor(
    private val context: Context,
    private val structuredLogger: StructuredLogger,
    private val translationEngine: OfflineTranslationEngine
) {
    
    companion object {
        private const val NETWORK_CHECK_INTERVAL_MS = 5000L // 5秒
        private const val OFFLINE_NOTIFICATION_DELAY_MS = 3000L // 3秒后显示离线提示
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 网络状态管理
    private val _networkStatus = MutableStateFlow(NetworkStatus.UNKNOWN)
    val networkStatus: StateFlow<NetworkStatus> = _networkStatus.asStateFlow()
    
    // 离线模式状态
    private val _offlineMode = MutableStateFlow(OfflineModeState())
    val offlineMode: StateFlow<OfflineModeState> = _offlineMode.asStateFlow()
    
    // 功能可用性状态
    private val _featureAvailability = MutableStateFlow(FeatureAvailability())
    val featureAvailability: StateFlow<FeatureAvailability> = _featureAvailability.asStateFlow()
    
    // 用户提示状态
    private val _userPrompts = MutableStateFlow<List<OfflinePrompt>>(emptyList())
    val userPrompts: StateFlow<List<OfflinePrompt>> = _userPrompts.asStateFlow()
    
    // 连接管理器
    private val connectivityManager: ConnectivityManager by lazy {
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }
    
    // 网络回调
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            updateNetworkStatus(NetworkStatus.CONNECTED)
        }
        
        override fun onLost(network: Network) {
            super.onLost(network)
            updateNetworkStatus(NetworkStatus.DISCONNECTED)
        }
        
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            
            when {
                hasInternet && isValidated -> updateNetworkStatus(NetworkStatus.CONNECTED)
                hasInternet && !isValidated -> updateNetworkStatus(NetworkStatus.LIMITED)
                else -> updateNetworkStatus(NetworkStatus.DISCONNECTED)
            }
        }
    }
    
    init {
        initializeOfflineModeManager()
    }
    
    /**
     * 初始化离线模式管理器
     */
    private fun initializeOfflineModeManager() {
        scope.launch {
            try {
                // 注册网络监听
                registerNetworkCallback()
                
                // 初始网络状态检查
                checkInitialNetworkStatus()
                
                // 启动定期网络检查
                startPeriodicNetworkCheck()
                
                // 监听翻译引擎状态
                observeTranslationEngineState()
                
                structuredLogger.logInfo(
                    message = "离线模式管理器初始化完成",
                    context = mapOf(
                        "initial_network_status" to _networkStatus.value.name
                    )
                )
                
                Timber.d("离线模式管理器初始化完成")
                
            } catch (e: Exception) {
                Timber.e(e, "离线模式管理器初始化失败")
                structuredLogger.logError(
                    message = "离线模式管理器初始化失败",
                    error = e
                )
            }
        }
    }
    
    /**
     * 注册网络回调
     */
    private fun registerNetworkCallback() {
        try {
            val networkRequest = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
            
        } catch (e: Exception) {
            Timber.e(e, "注册网络回调失败")
        }
    }
    
    /**
     * 检查初始网络状态
     */
    private fun checkInitialNetworkStatus() {
        val activeNetwork = connectivityManager.activeNetwork
        val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        
        val status = when {
            activeNetwork == null -> NetworkStatus.DISCONNECTED
            networkCapabilities == null -> NetworkStatus.DISCONNECTED
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) -> NetworkStatus.CONNECTED
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> NetworkStatus.LIMITED
            else -> NetworkStatus.DISCONNECTED
        }
        
        updateNetworkStatus(status)
    }
    
    /**
     * 启动定期网络检查
     */
    private fun startPeriodicNetworkCheck() {
        scope.launch {
            while (true) {
                delay(NETWORK_CHECK_INTERVAL_MS)
                
                try {
                    // 执行网络连通性测试
                    val isConnected = performNetworkConnectivityTest()
                    
                    val currentStatus = _networkStatus.value
                    val newStatus = if (isConnected) {
                        if (currentStatus == NetworkStatus.DISCONNECTED) {
                            NetworkStatus.CONNECTED
                        } else {
                            currentStatus
                        }
                    } else {
                        NetworkStatus.DISCONNECTED
                    }
                    
                    if (newStatus != currentStatus) {
                        updateNetworkStatus(newStatus)
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "定期网络检查失败")
                }
            }
        }
    }
    
    /**
     * 执行网络连通性测试
     */
    private suspend fun performNetworkConnectivityTest(): Boolean = withContext(Dispatchers.IO) {
        try {
            // TODO: 实现真实的网络连通性测试
            // 这里可以ping服务器或发送HTTP请求
            val activeNetwork = connectivityManager.activeNetwork
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
            
            return@withContext activeNetwork != null && 
                   networkCapabilities != null &&
                   networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                   networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                   
        } catch (e: Exception) {
            Timber.e(e, "网络连通性测试失败")
            false
        }
    }
    
    /**
     * 更新网络状态
     */
    private fun updateNetworkStatus(status: NetworkStatus) {
        val previousStatus = _networkStatus.value
        _networkStatus.value = status
        
        if (previousStatus != status) {
            scope.launch {
                handleNetworkStatusChange(previousStatus, status)
            }
        }
    }
    
    /**
     * 处理网络状态变化
     */
    private suspend fun handleNetworkStatusChange(
        previousStatus: NetworkStatus,
        newStatus: NetworkStatus
    ) {
        structuredLogger.logInfo(
            message = "网络状态变化",
            context = mapOf(
                "previous_status" to previousStatus.name,
                "new_status" to newStatus.name
            )
        )
        
        when (newStatus) {
            NetworkStatus.CONNECTED -> {
                handleNetworkConnected(previousStatus)
            }
            NetworkStatus.DISCONNECTED -> {
                handleNetworkDisconnected()
            }
            NetworkStatus.LIMITED -> {
                handleNetworkLimited()
            }
            NetworkStatus.UNKNOWN -> {
                // 保持当前状态
            }
        }
        
        // 更新功能可用性
        updateFeatureAvailability()
        
        // 更新离线模式状态
        updateOfflineModeState()
    }
    
    /**
     * 处理网络连接
     */
    private suspend fun handleNetworkConnected(previousStatus: NetworkStatus) {
        if (previousStatus == NetworkStatus.DISCONNECTED) {
            // 从离线恢复到在线
            addUserPrompt(
                OfflinePrompt(
                    id = "network_restored",
                    type = PromptType.INFO,
                    title = "网络已恢复",
                    message = "网络连接已恢复，所有功能现已可用",
                    action = PromptAction.DISMISS,
                    priority = PromptPriority.MEDIUM,
                    autoHide = true,
                    hideDelay = 3000L
                )
            )
        }
        
        // 清除离线相关提示
        clearPromptsOfType(PromptType.OFFLINE_WARNING)
    }
    
    /**
     * 处理网络断开
     */
    private suspend fun handleNetworkDisconnected() {
        // 延迟显示离线提示，避免短暂断网的误报
        delay(OFFLINE_NOTIFICATION_DELAY_MS)
        
        // 再次检查网络状态
        if (_networkStatus.value == NetworkStatus.DISCONNECTED) {
            addUserPrompt(
                OfflinePrompt(
                    id = "offline_mode",
                    type = PromptType.OFFLINE_WARNING,
                    title = "离线模式",
                    message = "网络连接不可用，部分功能受限。已安装的语言包仍可正常使用。",
                    action = PromptAction.VIEW_OFFLINE_FEATURES,
                    priority = PromptPriority.HIGH,
                    autoHide = false
                )
            )
        }
    }
    
    /**
     * 处理网络受限
     */
    private suspend fun handleNetworkLimited() {
        addUserPrompt(
            OfflinePrompt(
                id = "limited_network",
                type = PromptType.WARNING,
                title = "网络连接受限",
                message = "网络连接不稳定，建议使用离线功能",
                action = PromptAction.VIEW_OFFLINE_FEATURES,
                priority = PromptPriority.MEDIUM,
                autoHide = true,
                hideDelay = 5000L
            )
        )
    }
    
    /**
     * 监听翻译引擎状态
     */
    private fun observeTranslationEngineState() {
        scope.launch {
            translationEngine.engineState.collect { state ->
                updateOfflineModeState()
                
                if (!state.isReady && _networkStatus.value == NetworkStatus.DISCONNECTED) {
                    addUserPrompt(
                        OfflinePrompt(
                            id = "no_offline_models",
                            type = PromptType.ERROR,
                            title = "离线翻译不可用",
                            message = "未安装离线翻译模型，请在有网络时下载语言包",
                            action = PromptAction.DOWNLOAD_MODELS,
                            priority = PromptPriority.HIGH,
                            autoHide = false
                        )
                    )
                }
            }
        }
    }

    /**
     * 更新功能可用性
     */
    private fun updateFeatureAvailability() {
        val isOnline = _networkStatus.value == NetworkStatus.CONNECTED
        val hasOfflineModels = translationEngine.engineState.value.isReady
        val installedModels = translationEngine.engineState.value.installedModels

        val availability = FeatureAvailability(
            onlineTranslation = isOnline,
            offlineTranslation = hasOfflineModels,
            languagePackDownload = isOnline,
            dataSync = isOnline,
            cloudBackup = isOnline,
            realTimeCollaboration = isOnline,
            voiceInput = true, // 语音输入不依赖网络
            textToSpeech = true, // 文本转语音不依赖网络
            historyAccess = true, // 历史记录访问不依赖网络
            favoritesAccess = true, // 收藏夹访问不依赖网络
            offlineLanguagePairs = installedModels.map { "${it.sourceLanguage}-${it.targetLanguage}" },
            limitedFeatures = if (!isOnline) {
                listOf(
                    "在线翻译服务",
                    "语言包下载",
                    "数据同步",
                    "云端备份",
                    "实时协作"
                )
            } else {
                emptyList()
            }
        )

        _featureAvailability.value = availability
    }

    /**
     * 更新离线模式状态
     */
    private fun updateOfflineModeState() {
        val isOffline = _networkStatus.value == NetworkStatus.DISCONNECTED
        val hasOfflineModels = translationEngine.engineState.value.isReady
        val installedModels = translationEngine.engineState.value.installedModels

        val state = OfflineModeState(
            isOffline = isOffline,
            hasOfflineCapability = hasOfflineModels,
            availableLanguagePairs = installedModels.size,
            offlineFeatures = listOf(
                "离线翻译" to hasOfflineModels,
                "翻译历史" to true,
                "收藏夹" to true,
                "学习功能" to true,
                "语音输入" to true,
                "文本转语音" to true
            ),
            lastOnlineTime = if (isOffline) {
                _offlineMode.value.lastOnlineTime
            } else {
                System.currentTimeMillis()
            }
        )

        _offlineMode.value = state
    }

    /**
     * 添加用户提示
     */
    private fun addUserPrompt(prompt: OfflinePrompt) {
        val currentPrompts = _userPrompts.value.toMutableList()

        // 移除相同ID的提示
        currentPrompts.removeAll { it.id == prompt.id }

        // 添加新提示
        currentPrompts.add(prompt)

        // 按优先级排序
        currentPrompts.sortByDescending { it.priority.ordinal }

        _userPrompts.value = currentPrompts

        // 设置自动隐藏
        if (prompt.autoHide) {
            scope.launch {
                delay(prompt.hideDelay)
                dismissPrompt(prompt.id)
            }
        }

        structuredLogger.logInfo(
            message = "添加用户提示",
            context = mapOf(
                "prompt_id" to prompt.id,
                "prompt_type" to prompt.type.name,
                "prompt_title" to prompt.title
            )
        )
    }

    /**
     * 清除指定类型的提示
     */
    private fun clearPromptsOfType(type: PromptType) {
        val currentPrompts = _userPrompts.value.toMutableList()
        currentPrompts.removeAll { it.type == type }
        _userPrompts.value = currentPrompts
    }

    /**
     * 关闭提示
     */
    fun dismissPrompt(promptId: String) {
        val currentPrompts = _userPrompts.value.toMutableList()
        currentPrompts.removeAll { it.id == promptId }
        _userPrompts.value = currentPrompts

        structuredLogger.logInfo(
            message = "关闭用户提示",
            context = mapOf("prompt_id" to promptId)
        )
    }

    /**
     * 处理提示动作
     */
    fun handlePromptAction(promptId: String, action: PromptAction) {
        structuredLogger.logInfo(
            message = "处理提示动作",
            context = mapOf(
                "prompt_id" to promptId,
                "action" to action.name
            )
        )

        when (action) {
            PromptAction.DISMISS -> {
                dismissPrompt(promptId)
            }
            PromptAction.VIEW_OFFLINE_FEATURES -> {
                dismissPrompt(promptId)
                // TODO: 导航到离线功能页面
            }
            PromptAction.DOWNLOAD_MODELS -> {
                dismissPrompt(promptId)
                // TODO: 导航到语言包下载页面
            }
            PromptAction.RETRY_CONNECTION -> {
                dismissPrompt(promptId)
                scope.launch {
                    checkInitialNetworkStatus()
                }
            }
            PromptAction.ENABLE_OFFLINE_MODE -> {
                dismissPrompt(promptId)
                // TODO: 启用离线模式设置
            }
        }
    }

    /**
     * 检查功能是否可用
     */
    fun isFeatureAvailable(feature: OfflineFeature): Boolean {
        val availability = _featureAvailability.value

        return when (feature) {
            OfflineFeature.ONLINE_TRANSLATION -> availability.onlineTranslation
            OfflineFeature.OFFLINE_TRANSLATION -> availability.offlineTranslation
            OfflineFeature.LANGUAGE_PACK_DOWNLOAD -> availability.languagePackDownload
            OfflineFeature.DATA_SYNC -> availability.dataSync
            OfflineFeature.CLOUD_BACKUP -> availability.cloudBackup
            OfflineFeature.REAL_TIME_COLLABORATION -> availability.realTimeCollaboration
            OfflineFeature.VOICE_INPUT -> availability.voiceInput
            OfflineFeature.TEXT_TO_SPEECH -> availability.textToSpeech
            OfflineFeature.HISTORY_ACCESS -> availability.historyAccess
            OfflineFeature.FAVORITES_ACCESS -> availability.favoritesAccess
        }
    }

    /**
     * 获取功能限制说明
     */
    fun getFeatureLimitation(feature: OfflineFeature): String? {
        if (isFeatureAvailable(feature)) return null

        return when (feature) {
            OfflineFeature.ONLINE_TRANSLATION -> "需要网络连接才能使用在线翻译服务"
            OfflineFeature.OFFLINE_TRANSLATION -> "需要下载语言包才能使用离线翻译"
            OfflineFeature.LANGUAGE_PACK_DOWNLOAD -> "需要网络连接才能下载语言包"
            OfflineFeature.DATA_SYNC -> "需要网络连接才能同步数据"
            OfflineFeature.CLOUD_BACKUP -> "需要网络连接才能备份到云端"
            OfflineFeature.REAL_TIME_COLLABORATION -> "需要网络连接才能实时协作"
            else -> null
        }
    }

    /**
     * 获取离线模式建议
     */
    fun getOfflineModeSuggestions(): List<OfflineSuggestion> {
        val suggestions = mutableListOf<OfflineSuggestion>()
        val isOffline = _networkStatus.value == NetworkStatus.DISCONNECTED
        val hasOfflineModels = translationEngine.engineState.value.isReady

        if (isOffline && !hasOfflineModels) {
            suggestions.add(
                OfflineSuggestion(
                    id = "download_models",
                    title = "下载语言包",
                    description = "在有网络时下载语言包，以便离线使用翻译功能",
                    priority = SuggestionPriority.HIGH,
                    action = SuggestionAction.DOWNLOAD_LANGUAGE_PACKS
                )
            )
        }

        if (isOffline) {
            suggestions.add(
                OfflineSuggestion(
                    id = "use_history",
                    title = "查看翻译历史",
                    description = "浏览之前的翻译记录，无需网络连接",
                    priority = SuggestionPriority.MEDIUM,
                    action = SuggestionAction.VIEW_HISTORY
                )
            )

            suggestions.add(
                OfflineSuggestion(
                    id = "use_favorites",
                    title = "使用收藏夹",
                    description = "访问收藏的翻译内容，离线可用",
                    priority = SuggestionPriority.MEDIUM,
                    action = SuggestionAction.VIEW_FAVORITES
                )
            )
        }

        return suggestions
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
            scope.cancel()

            structuredLogger.logInfo(
                message = "离线模式管理器清理完成"
            )

        } catch (e: Exception) {
            Timber.e(e, "离线模式管理器清理失败")
        }
    }
}
