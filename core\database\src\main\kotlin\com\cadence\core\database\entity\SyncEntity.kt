package com.cadence.core.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.Index

/**
 * 同步配置实体
 */
@Entity(
    tableName = "sync_config"
)
data class SyncConfigEntity(
    @PrimaryKey
    val id: String = "default",
    val isEnabled: Boolean = false,
    val autoSyncEnabled: Boolean = true,
    val syncInterval: Long = 3600000L, // 1小时
    val wifiOnlySync: <PERSON>olean = true,
    val lastSyncTime: Long = 0L,
    val userId: String? = null,
    val deviceId: String,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 同步历史记录实体
 */
@Entity(
    tableName = "sync_history",
    indices = [
        Index(value = ["syncType"]),
        Index(value = ["status"]),
        Index(value = ["startTime"])
    ]
)
data class SyncHistoryEntity(
    @PrimaryKey
    val id: String,
    val syncType: String, // SyncType枚举值
    val status: String,   // SyncStatus枚举值
    val startTime: Long,
    val endTime: Long,
    val uploadedCount: Int = 0,
    val downloadedCount: Int = 0,
    val conflictCount: Int = 0,
    val errorMessage: String? = null,
    val details: String? = null, // JSON格式的SyncItemResult列表
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * 同步冲突实体
 */
@Entity(
    tableName = "sync_conflicts",
    indices = [
        Index(value = ["itemId"]),
        Index(value = ["conflictType"]),
        Index(value = ["resolved"])
    ]
)
data class SyncConflictEntity(
    @PrimaryKey
    val id: String,
    val itemId: String,
    val itemType: String,
    val conflictType: String, // ConflictType枚举值
    val localData: String,    // JSON格式的本地数据
    val remoteData: String,   // JSON格式的远程数据
    val resolved: Boolean = false,
    val resolution: String? = null, // ConflictResolution枚举值
    val createdAt: Long = System.currentTimeMillis(),
    val resolvedAt: Long? = null
)

/**
 * 设备信息实体
 */
@Entity(
    tableName = "device_info",
    indices = [
        Index(value = ["deviceId"], unique = true),
        Index(value = ["isCurrentDevice"])
    ]
)
data class DeviceInfoEntity(
    @PrimaryKey
    val id: String,
    val deviceId: String,
    val deviceName: String,
    val platform: String = "Android",
    val appVersion: String,
    val lastActiveTime: Long,
    val isCurrentDevice: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 同步队列实体
 * 用于存储待同步的数据项
 */
@Entity(
    tableName = "sync_queue",
    indices = [
        Index(value = ["itemType"]),
        Index(value = ["action"]),
        Index(value = ["priority"]),
        Index(value = ["createdAt"])
    ]
)
data class SyncQueueEntity(
    @PrimaryKey
    val id: String,
    val itemId: String,
    val itemType: String,     // 数据类型：translation, tag, preference等
    val action: String,       // SyncAction枚举值
    val priority: Int = 0,    // 优先级，数字越大优先级越高
    val data: String,         // JSON格式的数据
    val retryCount: Int = 0,
    val maxRetries: Int = 3,
    val lastError: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 同步统计实体
 */
@Entity(
    tableName = "sync_statistics"
)
data class SyncStatisticsEntity(
    @PrimaryKey
    val id: String = "default",
    val totalSyncs: Int = 0,
    val successfulSyncs: Int = 0,
    val failedSyncs: Int = 0,
    val lastSyncTime: Long = 0L,
    val totalDataSynced: Long = 0L, // 字节数
    val averageSyncDuration: Long = 0L, // 毫秒
    val syncsByType: String = "{}", // JSON格式的Map<SyncType, Int>
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 数据版本实体
 * 用于跟踪各种数据的版本信息
 */
@Entity(
    tableName = "data_versions",
    indices = [
        Index(value = ["dataType", "itemId"], unique = true)
    ]
)
data class DataVersionEntity(
    @PrimaryKey
    val id: String,
    val dataType: String,     // 数据类型
    val itemId: String,       // 数据项ID
    val version: Int = 1,     // 版本号
    val lastModified: Long,   // 最后修改时间
    val deviceId: String,     // 修改设备ID
    val checksum: String? = null, // 数据校验和
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
