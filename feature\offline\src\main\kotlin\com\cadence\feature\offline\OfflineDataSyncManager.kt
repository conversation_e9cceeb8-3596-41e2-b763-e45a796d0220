package com.cadence.feature.offline

import android.content.Context
import com.cadence.core.logging.StructuredLogger
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 离线数据同步管理器
 * 负责离线数据的同步、冲突检测和解决
 */
@Singleton
class OfflineDataSyncManager @Inject constructor(
    private val context: Context,
    private val structuredLogger: StructuredLogger
) {
    
    companion object {
        private const val SYNC_DATA_DIR = "offline_sync"
        private const val SYNC_METADATA_FILE = "sync_metadata.json"
        private const val CONFLICT_LOG_FILE = "conflict_log.json"
        private const val SYNC_INTERVAL_MS = 300000L // 5分钟
        private const val MAX_SYNC_RETRIES = 3
        private const val SYNC_TIMEOUT_MS = 30000L // 30秒
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 同步状态管理
    private val _syncState = MutableStateFlow(SyncState())
    val syncState: StateFlow<SyncState> = _syncState.asStateFlow()
    
    // 同步数据目录
    private val syncDataDir: File by lazy {
        File(context.filesDir, SYNC_DATA_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 同步元数据文件
    private val syncMetadataFile: File by lazy {
        File(syncDataDir, SYNC_METADATA_FILE)
    }
    
    // 冲突日志文件
    private val conflictLogFile: File by lazy {
        File(syncDataDir, CONFLICT_LOG_FILE)
    }
    
    // JSON序列化器
    private val json = Json {
        ignoreUnknownKeys = true
        prettyPrint = true
    }
    
    // 待同步数据队列
    private val pendingSyncItems = mutableListOf<SyncItem>()
    private val syncLock = Any()
    
    // 冲突解决策略
    private var conflictResolutionStrategy = ConflictResolutionStrategy.ASK_USER
    
    init {
        initializeSyncManager()
    }
    
    /**
     * 初始化同步管理器
     */
    private fun initializeSyncManager() {
        scope.launch {
            try {
                updateSyncState { it.copy(isInitializing = true) }
                
                // 加载同步元数据
                val metadata = loadSyncMetadata()
                
                // 加载待同步数据
                loadPendingSyncItems()
                
                // 启动定期同步
                startPeriodicSync()
                
                updateSyncState { 
                    it.copy(
                        isInitializing = false,
                        isReady = true,
                        lastSyncTime = metadata.lastSyncTime,
                        pendingItemsCount = pendingSyncItems.size
                    )
                }
                
                structuredLogger.logInfo(
                    message = "离线数据同步管理器初始化完成",
                    context = mapOf(
                        "sync_data_dir" to syncDataDir.absolutePath,
                        "pending_items" to pendingSyncItems.size.toString(),
                        "last_sync_time" to metadata.lastSyncTime.toString()
                    )
                )
                
                Timber.d("离线数据同步管理器初始化完成")
                
            } catch (e: Exception) {
                Timber.e(e, "同步管理器初始化失败")
                structuredLogger.logError(
                    message = "同步管理器初始化失败",
                    error = e
                )
                
                updateSyncState { 
                    it.copy(
                        isInitializing = false,
                        isReady = false,
                        error = e.message
                    )
                }
            }
        }
    }
    
    /**
     * 添加待同步数据
     */
    suspend fun addSyncItem(
        dataType: SyncDataType,
        itemId: String,
        data: String,
        operation: SyncOperation = SyncOperation.UPDATE
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            val syncItem = SyncItem(
                id = generateSyncItemId(),
                dataType = dataType,
                itemId = itemId,
                data = data,
                operation = operation,
                timestamp = System.currentTimeMillis(),
                status = SyncStatus.PENDING,
                retryCount = 0
            )
            
            synchronized(syncLock) {
                // 检查是否已存在相同的待同步项
                val existingIndex = pendingSyncItems.indexOfFirst { 
                    it.dataType == dataType && it.itemId == itemId 
                }
                
                if (existingIndex >= 0) {
                    // 更新现有项
                    pendingSyncItems[existingIndex] = syncItem
                } else {
                    // 添加新项
                    pendingSyncItems.add(syncItem)
                }
            }
            
            // 保存待同步数据
            savePendingSyncItems()
            
            // 更新状态
            updateSyncState { 
                it.copy(pendingItemsCount = pendingSyncItems.size)
            }
            
            structuredLogger.logInfo(
                message = "添加待同步数据",
                context = mapOf(
                    "data_type" to dataType.name,
                    "item_id" to itemId,
                    "operation" to operation.name,
                    "sync_item_id" to syncItem.id
                )
            )
            
            // 如果网络可用，立即尝试同步
            if (isNetworkAvailable()) {
                triggerSync()
            }
            
            true
            
        } catch (e: Exception) {
            Timber.e(e, "添加待同步数据失败")
            structuredLogger.logError(
                message = "添加待同步数据失败",
                error = e,
                context = mapOf(
                    "data_type" to dataType.name,
                    "item_id" to itemId
                )
            )
            false
        }
    }
    
    /**
     * 手动触发同步
     */
    suspend fun triggerSync(): SyncResult = withContext(Dispatchers.IO) {
        try {
            if (_syncState.value.isSyncing) {
                return@withContext SyncResult(
                    success = false,
                    error = "同步正在进行中"
                )
            }
            
            updateSyncState { it.copy(isSyncing = true, error = null) }
            
            val startTime = System.currentTimeMillis()
            var syncedCount = 0
            var conflictCount = 0
            var errorCount = 0
            
            structuredLogger.logInfo(
                message = "开始手动同步",
                context = mapOf(
                    "pending_items" to pendingSyncItems.size.toString()
                )
            )
            
            // 获取待同步项的副本
            val itemsToSync = synchronized(syncLock) {
                pendingSyncItems.filter { it.status == SyncStatus.PENDING }.toList()
            }
            
            if (itemsToSync.isEmpty()) {
                updateSyncState { 
                    it.copy(
                        isSyncing = false,
                        lastSyncTime = System.currentTimeMillis()
                    )
                }
                return@withContext SyncResult(
                    success = true,
                    message = "没有待同步的数据"
                )
            }
            
            // 逐个同步数据项
            for (syncItem in itemsToSync) {
                try {
                    val result = syncSingleItem(syncItem)
                    
                    when (result.status) {
                        SyncStatus.COMPLETED -> {
                            syncedCount++
                            removeSyncItem(syncItem.id)
                        }
                        SyncStatus.CONFLICT -> {
                            conflictCount++
                            handleSyncConflict(syncItem, result.conflictData)
                        }
                        SyncStatus.FAILED -> {
                            errorCount++
                            updateSyncItemStatus(syncItem.id, SyncStatus.FAILED, result.error)
                        }
                        else -> {
                            // 保持原状态
                        }
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "同步单个数据项失败: ${syncItem.id}")
                    errorCount++
                    updateSyncItemStatus(syncItem.id, SyncStatus.FAILED, e.message)
                }
            }
            
            val duration = System.currentTimeMillis() - startTime
            
            // 更新同步元数据
            updateSyncMetadata(System.currentTimeMillis())
            
            updateSyncState { 
                it.copy(
                    isSyncing = false,
                    lastSyncTime = System.currentTimeMillis(),
                    pendingItemsCount = pendingSyncItems.size,
                    error = null
                )
            }
            
            structuredLogger.logInfo(
                message = "同步完成",
                context = mapOf(
                    "duration_ms" to duration.toString(),
                    "synced_count" to syncedCount.toString(),
                    "conflict_count" to conflictCount.toString(),
                    "error_count" to errorCount.toString(),
                    "remaining_items" to pendingSyncItems.size.toString()
                )
            )
            
            SyncResult(
                success = true,
                syncedCount = syncedCount,
                conflictCount = conflictCount,
                errorCount = errorCount,
                duration = duration,
                message = "同步完成：成功${syncedCount}项，冲突${conflictCount}项，失败${errorCount}项"
            )
            
        } catch (e: Exception) {
            Timber.e(e, "同步失败")
            structuredLogger.logError(
                message = "同步失败",
                error = e
            )
            
            updateSyncState { 
                it.copy(
                    isSyncing = false,
                    error = e.message
                )
            }
            
            SyncResult(
                success = false,
                error = e.message ?: "同步失败"
            )
        }
    }

    /**
     * 同步单个数据项
     */
    private suspend fun syncSingleItem(syncItem: SyncItem): SyncItemResult = withContext(Dispatchers.IO) {
        try {
            // 模拟网络同步操作
            delay(100) // 模拟网络延迟

            // 检查服务器端数据
            val serverData = fetchServerData(syncItem.dataType, syncItem.itemId)

            if (serverData != null) {
                // 检测冲突
                val hasConflict = detectConflict(syncItem, serverData)

                if (hasConflict) {
                    return@withContext SyncItemResult(
                        status = SyncStatus.CONFLICT,
                        conflictData = ConflictData(
                            localData = syncItem.data,
                            serverData = serverData.data,
                            localTimestamp = syncItem.timestamp,
                            serverTimestamp = serverData.timestamp
                        )
                    )
                }
            }

            // 执行同步操作
            when (syncItem.operation) {
                SyncOperation.CREATE -> {
                    // 创建新数据
                    val success = createServerData(syncItem)
                    if (success) {
                        SyncItemResult(status = SyncStatus.COMPLETED)
                    } else {
                        SyncItemResult(status = SyncStatus.FAILED, error = "创建数据失败")
                    }
                }
                SyncOperation.UPDATE -> {
                    // 更新数据
                    val success = updateServerData(syncItem)
                    if (success) {
                        SyncItemResult(status = SyncStatus.COMPLETED)
                    } else {
                        SyncItemResult(status = SyncStatus.FAILED, error = "更新数据失败")
                    }
                }
                SyncOperation.DELETE -> {
                    // 删除数据
                    val success = deleteServerData(syncItem)
                    if (success) {
                        SyncItemResult(status = SyncStatus.COMPLETED)
                    } else {
                        SyncItemResult(status = SyncStatus.FAILED, error = "删除数据失败")
                    }
                }
            }

        } catch (e: Exception) {
            Timber.e(e, "同步单个数据项失败: ${syncItem.id}")
            SyncItemResult(
                status = SyncStatus.FAILED,
                error = e.message ?: "同步失败"
            )
        }
    }

    /**
     * 检测数据冲突
     */
    private fun detectConflict(syncItem: SyncItem, serverData: ServerData): Boolean {
        // 简单的时间戳比较冲突检测
        return serverData.timestamp > syncItem.timestamp && serverData.data != syncItem.data
    }

    /**
     * 处理同步冲突
     */
    private suspend fun handleSyncConflict(syncItem: SyncItem, conflictData: ConflictData?) {
        if (conflictData == null) return

        try {
            val resolution = when (conflictResolutionStrategy) {
                ConflictResolutionStrategy.LOCAL_WINS -> ConflictResolution.USE_LOCAL
                ConflictResolutionStrategy.SERVER_WINS -> ConflictResolution.USE_SERVER
                ConflictResolutionStrategy.LATEST_WINS -> {
                    if (conflictData.localTimestamp > conflictData.serverTimestamp) {
                        ConflictResolution.USE_LOCAL
                    } else {
                        ConflictResolution.USE_SERVER
                    }
                }
                ConflictResolutionStrategy.ASK_USER -> {
                    // 记录冲突，等待用户决策
                    logConflict(syncItem, conflictData)
                    updateSyncItemStatus(syncItem.id, SyncStatus.CONFLICT)
                    return
                }
            }

            // 应用冲突解决方案
            applyConflictResolution(syncItem, conflictData, resolution)

        } catch (e: Exception) {
            Timber.e(e, "处理同步冲突失败")
            structuredLogger.logError(
                message = "处理同步冲突失败",
                error = e,
                context = mapOf("sync_item_id" to syncItem.id)
            )
        }
    }

    /**
     * 应用冲突解决方案
     */
    private suspend fun applyConflictResolution(
        syncItem: SyncItem,
        conflictData: ConflictData,
        resolution: ConflictResolution
    ) {
        when (resolution) {
            ConflictResolution.USE_LOCAL -> {
                // 使用本地数据覆盖服务器
                val success = updateServerData(syncItem)
                if (success) {
                    removeSyncItem(syncItem.id)
                } else {
                    updateSyncItemStatus(syncItem.id, SyncStatus.FAILED, "应用本地数据失败")
                }
            }
            ConflictResolution.USE_SERVER -> {
                // 使用服务器数据覆盖本地
                updateLocalData(syncItem.dataType, syncItem.itemId, conflictData.serverData)
                removeSyncItem(syncItem.id)
            }
            ConflictResolution.MERGE -> {
                // 合并数据（需要具体的合并逻辑）
                val mergedData = mergeData(conflictData.localData, conflictData.serverData)
                val updatedSyncItem = syncItem.copy(data = mergedData)
                val success = updateServerData(updatedSyncItem)
                if (success) {
                    updateLocalData(syncItem.dataType, syncItem.itemId, mergedData)
                    removeSyncItem(syncItem.id)
                } else {
                    updateSyncItemStatus(syncItem.id, SyncStatus.FAILED, "合并数据失败")
                }
            }
        }
    }

    /**
     * 记录冲突
     */
    private suspend fun logConflict(syncItem: SyncItem, conflictData: ConflictData) {
        try {
            val conflict = ConflictLog(
                id = generateConflictId(),
                syncItemId = syncItem.id,
                dataType = syncItem.dataType,
                itemId = syncItem.itemId,
                localData = conflictData.localData,
                serverData = conflictData.serverData,
                localTimestamp = conflictData.localTimestamp,
                serverTimestamp = conflictData.serverTimestamp,
                detectedTime = System.currentTimeMillis(),
                status = ConflictStatus.PENDING
            )

            // 保存冲突日志
            val existingLogs = loadConflictLogs().toMutableList()
            existingLogs.add(conflict)
            saveConflictLogs(existingLogs)

            structuredLogger.logWarning(
                message = "检测到数据冲突",
                context = mapOf(
                    "conflict_id" to conflict.id,
                    "sync_item_id" to syncItem.id,
                    "data_type" to syncItem.dataType.name,
                    "item_id" to syncItem.itemId
                )
            )

        } catch (e: Exception) {
            Timber.e(e, "记录冲突失败")
        }
    }

    /**
     * 启动定期同步
     */
    private fun startPeriodicSync() {
        scope.launch {
            while (true) {
                delay(SYNC_INTERVAL_MS)

                if (isNetworkAvailable() && pendingSyncItems.isNotEmpty()) {
                    try {
                        triggerSync()
                    } catch (e: Exception) {
                        Timber.e(e, "定期同步失败")
                    }
                }
            }
        }
    }

    /**
     * 检查网络可用性
     */
    private fun isNetworkAvailable(): Boolean {
        // TODO: 实现真实的网络检查
        return true // 模拟网络可用
    }

    /**
     * 获取服务器数据
     */
    private suspend fun fetchServerData(dataType: SyncDataType, itemId: String): ServerData? {
        // TODO: 实现真实的服务器数据获取
        // 模拟服务器数据
        return null
    }

    /**
     * 创建服务器数据
     */
    private suspend fun createServerData(syncItem: SyncItem): Boolean {
        // TODO: 实现真实的服务器数据创建
        delay(50) // 模拟网络延迟
        return true // 模拟成功
    }

    /**
     * 更新服务器数据
     */
    private suspend fun updateServerData(syncItem: SyncItem): Boolean {
        // TODO: 实现真实的服务器数据更新
        delay(50) // 模拟网络延迟
        return true // 模拟成功
    }

    /**
     * 删除服务器数据
     */
    private suspend fun deleteServerData(syncItem: SyncItem): Boolean {
        // TODO: 实现真实的服务器数据删除
        delay(50) // 模拟网络延迟
        return true // 模拟成功
    }

    /**
     * 更新本地数据
     */
    private suspend fun updateLocalData(dataType: SyncDataType, itemId: String, data: String) {
        // TODO: 实现本地数据更新
        structuredLogger.logInfo(
            message = "更新本地数据",
            context = mapOf(
                "data_type" to dataType.name,
                "item_id" to itemId
            )
        )
    }

    /**
     * 合并数据
     */
    private fun mergeData(localData: String, serverData: String): String {
        // TODO: 实现具体的数据合并逻辑
        // 简单的合并策略：优先使用服务器数据
        return serverData
    }

    /**
     * 生成同步项ID
     */
    private fun generateSyncItemId(): String {
        return "sync_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    /**
     * 生成冲突ID
     */
    private fun generateConflictId(): String {
        return "conflict_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    /**
     * 更新同步状态
     */
    private fun updateSyncState(update: (SyncState) -> SyncState) {
        _syncState.value = update(_syncState.value)
    }

    /**
     * 移除同步项
     */
    private fun removeSyncItem(syncItemId: String) {
        synchronized(syncLock) {
            pendingSyncItems.removeAll { it.id == syncItemId }
        }
        scope.launch {
            savePendingSyncItems()
            updateSyncState { it.copy(pendingItemsCount = pendingSyncItems.size) }
        }
    }

    /**
     * 更新同步项状态
     */
    private fun updateSyncItemStatus(syncItemId: String, status: SyncStatus, error: String? = null) {
        synchronized(syncLock) {
            val index = pendingSyncItems.indexOfFirst { it.id == syncItemId }
            if (index >= 0) {
                pendingSyncItems[index] = pendingSyncItems[index].copy(
                    status = status,
                    error = error,
                    retryCount = if (status == SyncStatus.FAILED) pendingSyncItems[index].retryCount + 1 else pendingSyncItems[index].retryCount
                )
            }
        }
        scope.launch {
            savePendingSyncItems()
        }
    }

    /**
     * 加载同步元数据
     */
    private suspend fun loadSyncMetadata(): SyncMetadata = withContext(Dispatchers.IO) {
        try {
            if (syncMetadataFile.exists()) {
                val content = syncMetadataFile.readText()
                json.decodeFromString<SyncMetadata>(content)
            } else {
                SyncMetadata()
            }
        } catch (e: Exception) {
            Timber.e(e, "加载同步元数据失败")
            SyncMetadata()
        }
    }

    /**
     * 更新同步元数据
     */
    private suspend fun updateSyncMetadata(lastSyncTime: Long) = withContext(Dispatchers.IO) {
        try {
            val metadata = SyncMetadata(
                lastSyncTime = lastSyncTime,
                version = 1
            )
            val content = json.encodeToString(SyncMetadata.serializer(), metadata)
            syncMetadataFile.writeText(content)
        } catch (e: Exception) {
            Timber.e(e, "更新同步元数据失败")
        }
    }

    /**
     * 加载待同步数据
     */
    private suspend fun loadPendingSyncItems() = withContext(Dispatchers.IO) {
        try {
            val syncItemsFile = File(syncDataDir, "pending_sync_items.json")
            if (syncItemsFile.exists()) {
                val content = syncItemsFile.readText()
                val items = json.decodeFromString<List<SyncItem>>(content)
                synchronized(syncLock) {
                    pendingSyncItems.clear()
                    pendingSyncItems.addAll(items)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "加载待同步数据失败")
        }
    }

    /**
     * 保存待同步数据
     */
    private suspend fun savePendingSyncItems() = withContext(Dispatchers.IO) {
        try {
            val syncItemsFile = File(syncDataDir, "pending_sync_items.json")
            val items = synchronized(syncLock) { pendingSyncItems.toList() }
            val content = json.encodeToString(items)
            syncItemsFile.writeText(content)
        } catch (e: Exception) {
            Timber.e(e, "保存待同步数据失败")
        }
    }

    /**
     * 加载冲突日志
     */
    private suspend fun loadConflictLogs(): List<ConflictLog> = withContext(Dispatchers.IO) {
        try {
            if (conflictLogFile.exists()) {
                val content = conflictLogFile.readText()
                json.decodeFromString<List<ConflictLog>>(content)
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Timber.e(e, "加载冲突日志失败")
            emptyList()
        }
    }

    /**
     * 保存冲突日志
     */
    private suspend fun saveConflictLogs(logs: List<ConflictLog>) = withContext(Dispatchers.IO) {
        try {
            val content = json.encodeToString(logs)
            conflictLogFile.writeText(content)
        } catch (e: Exception) {
            Timber.e(e, "保存冲突日志失败")
        }
    }

    /**
     * 获取待同步项列表
     */
    fun getPendingSyncItems(): List<SyncItem> {
        return synchronized(syncLock) { pendingSyncItems.toList() }
    }

    /**
     * 获取冲突列表
     */
    suspend fun getConflicts(): List<ConflictLog> {
        return loadConflictLogs().filter { it.status == ConflictStatus.PENDING }
    }

    /**
     * 解决冲突
     */
    suspend fun resolveConflict(conflictId: String, resolution: ConflictResolution): Boolean {
        return try {
            val conflicts = loadConflictLogs().toMutableList()
            val conflictIndex = conflicts.indexOfFirst { it.id == conflictId }

            if (conflictIndex >= 0) {
                val conflict = conflicts[conflictIndex]
                val syncItem = pendingSyncItems.find { it.id == conflict.syncItemId }

                if (syncItem != null) {
                    val conflictData = ConflictData(
                        localData = conflict.localData,
                        serverData = conflict.serverData,
                        localTimestamp = conflict.localTimestamp,
                        serverTimestamp = conflict.serverTimestamp
                    )

                    applyConflictResolution(syncItem, conflictData, resolution)

                    // 标记冲突为已解决
                    conflicts[conflictIndex] = conflict.copy(
                        status = ConflictStatus.RESOLVED,
                        resolvedTime = System.currentTimeMillis(),
                        resolution = resolution
                    )
                    saveConflictLogs(conflicts)

                    true
                } else {
                    false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "解决冲突失败")
            false
        }
    }

    /**
     * 设置冲突解决策略
     */
    fun setConflictResolutionStrategy(strategy: ConflictResolutionStrategy) {
        conflictResolutionStrategy = strategy
        structuredLogger.logInfo(
            message = "设置冲突解决策略",
            context = mapOf("strategy" to strategy.name)
        )
    }

    /**
     * 清理已完成的同步项和已解决的冲突
     */
    suspend fun cleanup() {
        try {
            // 清理已解决的冲突（保留最近30天）
            val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
            val conflicts = loadConflictLogs().filter {
                it.status == ConflictStatus.PENDING ||
                (it.resolvedTime ?: 0) > thirtyDaysAgo
            }
            saveConflictLogs(conflicts)

            structuredLogger.logInfo(
                message = "同步数据清理完成",
                context = mapOf(
                    "remaining_conflicts" to conflicts.size.toString()
                )
            )

        } catch (e: Exception) {
            Timber.e(e, "同步数据清理失败")
        }
    }
}
