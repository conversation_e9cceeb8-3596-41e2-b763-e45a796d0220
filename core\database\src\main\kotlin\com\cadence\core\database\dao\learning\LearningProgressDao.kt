package com.cadence.core.database.dao.learning

import androidx.room.*
import com.cadence.core.database.entity.learning.LearningProgressEntity
import kotlinx.coroutines.flow.Flow

/**
 * 学习进度数据访问对象
 */
@Dao
interface LearningProgressDao {
    
    /**
     * 获取用户的所有学习进度
     */
    @Query("SELECT * FROM learning_progress WHERE user_id = :userId ORDER BY updated_at DESC")
    fun getUserLearningProgress(userId: String): Flow<List<LearningProgressEntity>>
    
    /**
     * 获取特定单词的学习进度
     */
    @Query("SELECT * FROM learning_progress WHERE user_id = :userId AND word_id = :wordId")
    suspend fun getLearningProgress(userId: String, wordId: String): LearningProgressEntity?
    
    /**
     * 获取需要复习的单词
     */
    @Query("""
        SELECT * FROM learning_progress 
        WHERE user_id = :userId 
        AND next_review_at <= :currentTime 
        ORDER BY next_review_at ASC
    """)
    suspend fun getWordsToReview(userId: String, currentTime: Long): List<LearningProgressEntity>
    
    /**
     * 获取已收藏的单词
     */
    @Query("SELECT * FROM learning_progress WHERE user_id = :userId AND is_bookmarked = 1 ORDER BY updated_at DESC")
    fun getBookmarkedWords(userId: String): Flow<List<LearningProgressEntity>>
    
    /**
     * 根据掌握程度获取单词
     */
    @Query("SELECT * FROM learning_progress WHERE user_id = :userId AND mastery_level = :masteryLevel ORDER BY updated_at DESC")
    fun getWordsByMasteryLevel(userId: String, masteryLevel: String): Flow<List<LearningProgressEntity>>
    
    /**
     * 获取学习统计数据
     */
    @Query("""
        SELECT 
            COUNT(*) as total_words,
            SUM(CASE WHEN mastery_level = 'MASTERED' THEN 1 ELSE 0 END) as mastered_words,
            AVG(CASE WHEN total_attempts > 0 THEN CAST(correct_answers AS FLOAT) / total_attempts ELSE 0 END) as avg_accuracy
        FROM learning_progress 
        WHERE user_id = :userId
    """)
    suspend fun getLearningStatistics(userId: String): LearningStatisticsResult?
    
    /**
     * 插入学习进度
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLearningProgress(progress: LearningProgressEntity)
    
    /**
     * 更新学习进度
     */
    @Update
    suspend fun updateLearningProgress(progress: LearningProgressEntity)
    
    /**
     * 更新掌握程度
     */
    @Query("""
        UPDATE learning_progress 
        SET mastery_level = :masteryLevel, 
            next_review_at = :nextReviewAt,
            updated_at = :updatedAt
        WHERE user_id = :userId AND word_id = :wordId
    """)
    suspend fun updateMasteryLevel(
        userId: String, 
        wordId: String, 
        masteryLevel: String, 
        nextReviewAt: Long?,
        updatedAt: Long
    )
    
    /**
     * 更新学习统计
     */
    @Query("""
        UPDATE learning_progress 
        SET correct_answers = :correctAnswers,
            total_attempts = :totalAttempts,
            last_studied_at = :lastStudiedAt,
            study_streak = :studyStreak,
            updated_at = :updatedAt
        WHERE user_id = :userId AND word_id = :wordId
    """)
    suspend fun updateLearningStats(
        userId: String,
        wordId: String,
        correctAnswers: Int,
        totalAttempts: Int,
        lastStudiedAt: Long,
        studyStreak: Int,
        updatedAt: Long
    )
    
    /**
     * 切换收藏状态
     */
    @Query("""
        UPDATE learning_progress 
        SET is_bookmarked = :isBookmarked, updated_at = :updatedAt
        WHERE user_id = :userId AND word_id = :wordId
    """)
    suspend fun toggleBookmark(userId: String, wordId: String, isBookmarked: Boolean, updatedAt: Long)
    
    /**
     * 删除学习进度
     */
    @Query("DELETE FROM learning_progress WHERE user_id = :userId AND word_id = :wordId")
    suspend fun deleteLearningProgress(userId: String, wordId: String)
    
    /**
     * 删除用户所有学习进度
     */
    @Query("DELETE FROM learning_progress WHERE user_id = :userId")
    suspend fun deleteAllUserProgress(userId: String)
}

/**
 * 学习统计查询结果
 */
data class LearningStatisticsResult(
    val total_words: Int,
    val mastered_words: Int,
    val avg_accuracy: Float
)