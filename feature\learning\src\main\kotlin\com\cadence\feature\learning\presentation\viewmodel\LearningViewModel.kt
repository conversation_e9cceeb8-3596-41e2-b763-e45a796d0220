package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.*
import com.cadence.domain.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 学习功能ViewModel
 * 管理学习界面的状态和业务逻辑
 */
@HiltViewModel
class LearningViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(LearningUiState())
    val uiState: StateFlow<LearningUiState> = _uiState.asStateFlow()
    
    private val _currentWords = MutableStateFlow<List<Word>>(emptyList())
    val currentWords: StateFlow<List<Word>> = _currentWords.asStateFlow()
    
    private val _learningProgress = MutableStateFlow<LearningStatistics?>(null)
    val learningProgress: StateFlow<LearningStatistics?> = _learningProgress.asStateFlow()
    
    private val _learningMode = MutableStateFlow(LearningMode.NEW_WORDS)
    val learningMode: StateFlow<LearningMode> = _learningMode.asStateFlow()
    
    init {
        observeLearningMode()
    }
    
    /**
     * 加载学习数据
     */
    fun loadLearningData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // 加载学习统计
                val statistics = learningRepository.getLearningStatistics("current_user")
                _learningProgress.value = statistics
                
                // 根据当前模式加载单词
                loadWordsForCurrentMode()
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载学习数据失败"
                )
            }
        }
    }
    
    /**
     * 设置学习模式
     */
    fun setLearningMode(mode: LearningMode) {
        _learningMode.value = mode
    }
    
    /**
     * 学习单词
     */
    fun studyWord(wordId: String) {
        viewModelScope.launch {
            try {
                learningRepository.startStudySession(
                    userId = "current_user",
                    wordIds = listOf(wordId),
                    sessionType = SessionType.NEW_WORDS
                )
                // 重新加载数据
                loadLearningData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "开始学习失败"
                )
            }
        }
    }
    
    /**
     * 切换收藏状态
     */
    fun toggleBookmark(wordId: String) {
        viewModelScope.launch {
            try {
                learningRepository.toggleWordBookmark("current_user", wordId)
                // 重新加载数据
                loadLearningData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "切换收藏状态失败"
                )
            }
        }
    }
    
    /**
     * 观察学习模式变化
     */
    private fun observeLearningMode() {
        viewModelScope.launch {
            _learningMode.collect { mode ->
                loadWordsForCurrentMode()
            }
        }
    }
    
    /**
     * 根据当前模式加载单词
     */
    private suspend fun loadWordsForCurrentMode() {
        try {
            val words = when (_learningMode.value) {
                LearningMode.NEW_WORDS -> learningRepository.getNewWords("current_user", 20)
                LearningMode.REVIEW -> learningRepository.getWordsToReview("current_user")
                LearningMode.PRACTICE -> learningRepository.getRandomWords(20)
                LearningMode.BOOKMARKED -> learningRepository.getBookmarkedWords("current_user")
            }
            _currentWords.value = words
        } catch (e: Exception) {
            _uiState.value = _uiState.value.copy(
                error = e.message ?: "加载单词失败"
            )
        }
    }
}

/**
 * 学习界面UI状态
 */
data class LearningUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 学习模式
 */
enum class LearningMode(val displayName: String) {
    NEW_WORDS("新单词"),
    REVIEW("复习"),
    PRACTICE("练习"),
    BOOKMARKED("收藏")
}