package com.cadence.domain.repository.culture

import com.cadence.domain.model.culture.*
import kotlinx.coroutines.flow.Flow

/**
 * 文化背景上下文Repository接口
 * 提供文化背景相关数据的访问和管理功能
 */
interface CulturalContextRepository {
    
    // 基础CRUD操作
    suspend fun saveCulturalContext(context: CulturalContext): Result<Unit>
    suspend fun saveCulturalContexts(contexts: List<CulturalContext>): Result<Unit>
    suspend fun updateCulturalContext(context: CulturalContext): Result<Unit>
    suspend fun deleteCulturalContext(contextId: String): Result<Unit>
    
    // 查询操作
    suspend fun getCulturalContextById(contextId: String): Result<CulturalContext?>
    suspend fun getCulturalContextsByWord(word: String): Result<List<CulturalContext>>
    suspend fun getCulturalContextsByLanguages(
        sourceLanguage: String,
        targetLanguage: String
    ): Result<List<CulturalContext>>
    suspend fun getCulturalContextsByRegion(region: String): Result<List<CulturalContext>>
    suspend fun getCulturalContextsByDifficulty(difficulty: CulturalDifficulty): Result<List<CulturalContext>>
    suspend fun searchCulturalContexts(query: String): Result<List<CulturalContext>>
    suspend fun getCulturalContextsPaged(page: Int, pageSize: Int): Result<List<CulturalContext>>
    
    // Flow查询（用于实时更新）
    fun getAllCulturalContextsFlow(): Flow<List<CulturalContext>>
    fun searchCulturalContextsFlow(query: String): Flow<List<CulturalContext>>
    fun getCulturalContextsByWordFlow(word: String): Flow<List<CulturalContext>>
    
    // 统计查询
    suspend fun getCulturalContextCount(): Result<Int>
    suspend fun getCulturalContextCountByRegion(region: String): Result<Int>
    suspend fun getCulturalContextCountByDifficulty(difficulty: CulturalDifficulty): Result<Int>
    suspend fun getCulturalContextStatsByRegions(): Result<Map<String, Int>>
    suspend fun getCulturalContextStatsByDifficulties(): Result<Map<CulturalDifficulty, Int>>
    
    // 推荐功能
    suspend fun getRecommendedCulturalContexts(
        userId: String,
        limit: Int = 10
    ): Result<List<CulturalContext>>
    
    suspend fun getRelatedCulturalContexts(
        contextId: String,
        limit: Int = 5
    ): Result<List<CulturalContext>>
    
    // 学习进度相关
    suspend fun markAsLearned(userId: String, contextId: String): Result<Unit>
    suspend fun markAsBookmarked(userId: String, contextId: String, bookmarked: Boolean): Result<Unit>
    suspend fun updateLearningNotes(userId: String, contextId: String, notes: String): Result<Unit>
    suspend fun recordAccess(userId: String, contextId: String): Result<Unit>
    
    suspend fun getUserLearningProgress(userId: String): Result<List<CulturalLearningProgress>>
    suspend fun getUserBookmarkedContexts(userId: String): Result<List<CulturalContext>>
    suspend fun getUserLearningStats(userId: String): Result<CulturalLearningStats>
    
    // 缓存和同步
    suspend fun refreshCulturalContexts(): Result<Unit>
    suspend fun syncCulturalContexts(): Result<Unit>
}

/**
 * 文化学习统计数据
 */
data class CulturalLearningStats(
    val totalContexts: Int,
    val learnedContexts: Int,
    val bookmarkedContexts: Int,
    val averageAccessCount: Double,
    val difficultyDistribution: Map<CulturalDifficulty, Int>,
    val regionDistribution: Map<String, Int>,
    val recentActivity: List<CulturalLearningProgress>
)