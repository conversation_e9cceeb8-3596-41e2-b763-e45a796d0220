package com.cadence.core.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.cadence.core.database.converter.DatabaseConverters
import com.cadence.core.database.dao.*
import com.cadence.core.database.dao.learning.*
import com.cadence.core.database.dao.culture.*
import com.cadence.core.database.entity.*
import com.cadence.core.database.entity.learning.*
import com.cadence.core.database.entity.culture.*
import com.cadence.core.database.migration.MIGRATION_4_5
import com.cadence.core.database.migration.MIGRATION_5_6
import com.cadence.core.database.migration.MIGRATION_6_7

/**
 * Cadence应用主数据库
 * 使用Room数据库框架，支持翻译记录、缓存、用户偏好等数据存储
 */
@Database(
    entities = [
        TranslationEntity::class,
        LanguageRegionEntity::class,
        UserPreferenceEntity::class,
        TranslationCacheEntity::class,
        OfflineDictionaryEntity::class,
        DictionaryStatsEntity::class,
        WordUsageHistoryEntity::class,
        TagEntity::class,
        TranslationTagEntity::class,
        SyncConfigEntity::class,
        SyncHistoryEntity::class,
        SyncConflictEntity::class,
        DeviceInfoEntity::class,
        SyncQueueEntity::class,
        SyncStatisticsEntity::class,
        DataVersionEntity::class,
        FavoriteFolderEntity::class,
        FavoriteItemEntity::class,
        FavoriteConfigEntity::class,
        FavoriteOperationEntity::class,
        FavoriteSyncInfoEntity::class,
        FavoriteUsageStatsEntity::class,
        FavoriteSearchSuggestionEntity::class,
        // 学习功能相关实体
        WordEntity::class,
        LearningProgressEntity::class,
        StudySessionEntity::class,
        // 文化背景相关实体
        CulturalContextEntity::class,
        UsageContextEntity::class,
        RegionalDifferenceEntity::class,
        CulturalExampleEntity::class,
        CulturalRecommendationEntity::class,
        CulturalLearningProgressEntity::class
    ],
    version = 7,
    exportSchema = true
)
@TypeConverters(DatabaseConverters::class)
abstract class CadenceDatabase : RoomDatabase() {
    
    // DAO接口
    abstract fun translationDao(): TranslationDao
    abstract fun languageRegionDao(): LanguageRegionDao
    abstract fun userPreferenceDao(): UserPreferenceDao
    abstract fun translationCacheDao(): TranslationCacheDao
    abstract fun offlineDictionaryDao(): OfflineDictionaryDao
    abstract fun tagDao(): TagDao
    abstract fun syncDao(): SyncDao
    abstract fun favoriteDao(): FavoriteDao

    // 学习功能相关DAO
    abstract fun wordDao(): WordDao
    abstract fun learningProgressDao(): LearningProgressDao
    abstract fun studySessionDao(): StudySessionDao

    // 文化背景相关DAO
    abstract fun culturalContextDao(): CulturalContextDao
    abstract fun culturalRecommendationDao(): CulturalRecommendationDao
    abstract fun culturalLearningProgressDao(): CulturalLearningProgressDao
    
    companion object {
        const val DATABASE_NAME = "cadence_database"
        
        @Volatile
        private var INSTANCE: CadenceDatabase? = null
        
        /**
         * 获取数据库实例（单例模式）
         */
        fun getDatabase(context: Context): CadenceDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CadenceDatabase::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(MIGRATION_4_5, MIGRATION_5_6, MIGRATION_6_7)
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 创建内存数据库（用于测试）
         */
        fun createInMemoryDatabase(context: Context): CadenceDatabase {
            return Room.inMemoryDatabaseBuilder(
                context.applicationContext,
                CadenceDatabase::class.java
            )
                .allowMainThreadQueries()
                .build()
        }
    }
}

/**
 * 数据库回调，用于初始化数据
 */
private class DatabaseCallback : RoomDatabase.Callback() {
    // 可以在这里添加数据库创建后的初始化逻辑
}
