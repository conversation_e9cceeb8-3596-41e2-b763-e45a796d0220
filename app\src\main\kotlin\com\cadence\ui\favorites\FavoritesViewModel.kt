package com.cadence.ui.favorites

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.*
import com.cadence.domain.usecase.ManageFavoritesUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 收藏功能视图模型
 * 管理收藏夹和收藏项的UI状态
 */
@HiltViewModel
class FavoritesViewModel @Inject constructor(
    private val manageFavoritesUseCase: ManageFavoritesUseCase
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(FavoritesUiState())
    val uiState: StateFlow<FavoritesUiState> = _uiState.asStateFlow()
    
    // 收藏夹列表
    private val _folders = MutableStateFlow<List<FavoriteFolder>>(emptyList())
    val folders: StateFlow<List<FavoriteFolder>> = _folders.asStateFlow()
    
    // 当前选中的收藏夹
    private val _selectedFolder = MutableStateFlow<FavoriteFolder?>(null)
    val selectedFolder: StateFlow<FavoriteFolder?> = _selectedFolder.asStateFlow()
    
    // 收藏项列表
    private val _favoriteItems = MutableStateFlow<List<Pair<FavoriteItem, Translation?>>>(emptyList())
    val favoriteItems: StateFlow<List<Pair<FavoriteItem, Translation?>>> = _favoriteItems.asStateFlow()
    
    // 搜索查询
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // 收藏统计
    private val _statistics = MutableStateFlow<FavoriteStatistics?>(null)
    val statistics: StateFlow<FavoriteStatistics?> = _statistics.asStateFlow()
    
    init {
        loadFolders()
        loadStatistics()
    }
    
    /**
     * 加载收藏夹列表
     */
    fun loadFolders() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                manageFavoritesUseCase.getAllFolders()
                    .catch { error ->
                        Timber.e(error, "加载收藏夹失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "加载收藏夹失败: ${error.message}"
                        )
                    }
                    .collect { folders ->
                        _folders.value = folders
                        _uiState.value = _uiState.value.copy(isLoading = false, error = null)
                        
                        // 如果没有选中的收藏夹，选择第一个
                        if (_selectedFolder.value == null && folders.isNotEmpty()) {
                            selectFolder(folders.first())
                        }
                    }
            } catch (e: Exception) {
                Timber.e(e, "加载收藏夹异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "加载收藏夹异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择收藏夹
     */
    fun selectFolder(folder: FavoriteFolder) {
        _selectedFolder.value = folder
        loadFolderItems(folder.id)
    }
    
    /**
     * 加载收藏夹项目
     */
    private fun loadFolderItems(folderId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoadingItems = true)
                
                manageFavoritesUseCase.getFolderItemsWithTranslations(folderId)
                    .catch { error ->
                        Timber.e(error, "加载收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            isLoadingItems = false,
                            error = "加载收藏项失败: ${error.message}"
                        )
                    }
                    .collect { items ->
                        _favoriteItems.value = items
                        _uiState.value = _uiState.value.copy(isLoadingItems = false, error = null)
                    }
            } catch (e: Exception) {
                Timber.e(e, "加载收藏项异常")
                _uiState.value = _uiState.value.copy(
                    isLoadingItems = false,
                    error = "加载收藏项异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 创建收藏夹
     */
    fun createFolder(name: String, description: String?, color: String, icon: String?) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = manageFavoritesUseCase.createFolder(name, description, color, icon)
                result.fold(
                    onSuccess = { folderId ->
                        Timber.d("创建收藏夹成功: $folderId")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "收藏夹创建成功"
                        )
                        loadFolders() // 重新加载收藏夹列表
                    },
                    onFailure = { error ->
                        Timber.e(error, "创建收藏夹失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "创建收藏夹失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "创建收藏夹异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "创建收藏夹异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 删除收藏夹
     */
    fun deleteFolder(folderId: String, moveItemsToFolderId: String? = null) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = manageFavoritesUseCase.deleteFolder(folderId, moveItemsToFolderId)
                result.fold(
                    onSuccess = {
                        Timber.d("删除收藏夹成功: $folderId")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "收藏夹删除成功"
                        )
                        
                        // 如果删除的是当前选中的收藏夹，清除选择
                        if (_selectedFolder.value?.id == folderId) {
                            _selectedFolder.value = null
                            _favoriteItems.value = emptyList()
                        }
                        
                        loadFolders() // 重新加载收藏夹列表
                    },
                    onFailure = { error ->
                        Timber.e(error, "删除收藏夹失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "删除收藏夹失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "删除收藏夹异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "删除收藏夹异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 移除收藏项
     */
    fun removeFavoriteItem(itemId: String) {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.removeFavoriteItem(itemId)
                result.fold(
                    onSuccess = {
                        Timber.d("移除收藏项成功: $itemId")
                        _uiState.value = _uiState.value.copy(message = "已移除收藏")
                        
                        // 重新加载当前收藏夹的项目
                        _selectedFolder.value?.let { folder ->
                            loadFolderItems(folder.id)
                        }
                    },
                    onFailure = { error ->
                        Timber.e(error, "移除收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            error = "移除收藏失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "移除收藏项异常")
                _uiState.value = _uiState.value.copy(
                    error = "移除收藏异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 搜索收藏项
     */
    fun searchFavoriteItems(query: String) {
        _searchQuery.value = query
        
        if (query.isBlank()) {
            // 如果搜索为空，重新加载当前收藏夹的项目
            _selectedFolder.value?.let { folder ->
                loadFolderItems(folder.id)
            }
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoadingItems = true)
                
                manageFavoritesUseCase.searchFavoriteItemsWithTranslations(
                    searchQuery = query,
                    folderId = _selectedFolder.value?.id
                )
                    .catch { error ->
                        Timber.e(error, "搜索收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            isLoadingItems = false,
                            error = "搜索失败: ${error.message}"
                        )
                    }
                    .collect { items ->
                        _favoriteItems.value = items
                        _uiState.value = _uiState.value.copy(isLoadingItems = false, error = null)
                    }
            } catch (e: Exception) {
                Timber.e(e, "搜索收藏项异常")
                _uiState.value = _uiState.value.copy(
                    isLoadingItems = false,
                    error = "搜索异常: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载统计信息
     */
    private fun loadStatistics() {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.getFavoriteStatistics()
                result.fold(
                    onSuccess = { stats ->
                        _statistics.value = stats
                    },
                    onFailure = { error ->
                        Timber.e(error, "加载统计信息失败")
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "加载统计信息异常")
            }
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }

    /**
     * 移动收藏项到其他收藏夹
     */
    fun moveFavoriteItem(itemId: String, targetFolderId: String) {
        viewModelScope.launch {
            try {
                val result = manageFavoritesUseCase.moveFavoriteItem(itemId, targetFolderId)
                result.fold(
                    onSuccess = {
                        Timber.d("移动收藏项成功: $itemId -> $targetFolderId")
                        _uiState.value = _uiState.value.copy(message = "已移动到其他收藏夹")

                        // 重新加载当前收藏夹的项目
                        _selectedFolder.value?.let { folder ->
                            loadFolderItems(folder.id)
                        }
                    },
                    onFailure = { error ->
                        Timber.e(error, "移动收藏项失败")
                        _uiState.value = _uiState.value.copy(
                            error = "移动收藏失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "移动收藏项异常")
                _uiState.value = _uiState.value.copy(
                    error = "移动收藏异常: ${e.message}"
                )
            }
        }
    }
}

/**
 * 收藏功能UI状态
 */
data class FavoritesUiState(
    val isLoading: Boolean = false,
    val isLoadingItems: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
