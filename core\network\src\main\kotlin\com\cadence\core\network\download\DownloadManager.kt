package com.cadence.core.network.download

import android.content.Context
import com.cadence.core.network.monitor.NetworkMonitor
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 下载管理器
 * 支持断点续传、并发控制和进度监控
 */
@Singleton
class DownloadManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val okHttpClient: OkHttpClient,
    private val networkMonitor: NetworkMonitor
) {
    
    private val downloadScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 活跃下载任务
    private val activeDownloads = ConcurrentHashMap<String, DownloadTask>()
    
    // 下载状态流
    private val _downloadStates = MutableStateFlow<Map<String, DownloadState>>(emptyMap())
    val downloadStates: StateFlow<Map<String, DownloadState>> = _downloadStates.asStateFlow()
    
    companion object {
        private const val BUFFER_SIZE = 8192
        private const val MAX_CONCURRENT_DOWNLOADS = 3
        private const val CHUNK_SIZE = 1024 * 1024 // 1MB chunks
    }
    
    /**
     * 开始下载
     */
    fun startDownload(
        url: String,
        fileName: String,
        directory: File? = null
    ): Flow<DownloadState> {
        val downloadId = generateDownloadId(url, fileName)
        
        // 如果已经在下载，返回现有状态
        activeDownloads[downloadId]?.let { existingTask ->
            return existingTask.stateFlow
        }
        
        val targetDir = directory ?: File(context.filesDir, "downloads")
        if (!targetDir.exists()) {
            targetDir.mkdirs()
        }
        
        val targetFile = File(targetDir, fileName)
        val tempFile = File(targetDir, "$fileName.tmp")
        
        val task = DownloadTask(
            id = downloadId,
            url = url,
            fileName = fileName,
            targetFile = targetFile,
            tempFile = tempFile
        )
        
        activeDownloads[downloadId] = task
        
        // 启动下载协程
        downloadScope.launch {
            try {
                performDownload(task)
            } catch (e: Exception) {
                Timber.e(e, "下载失败: $url")
                task.updateState(DownloadState.Error(downloadId, e.message ?: "下载失败"))
            } finally {
                activeDownloads.remove(downloadId)
            }
        }
        
        return task.stateFlow
    }
    
    /**
     * 暂停下载
     */
    fun pauseDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { task ->
            task.pause()
            Timber.d("暂停下载: $downloadId")
        }
    }
    
    /**
     * 恢复下载
     */
    fun resumeDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { task ->
            task.resume()
            Timber.d("恢复下载: $downloadId")
        }
    }
    
    /**
     * 取消下载
     */
    fun cancelDownload(downloadId: String) {
        activeDownloads[downloadId]?.let { task ->
            task.cancel()
            activeDownloads.remove(downloadId)
            
            // 删除临时文件
            if (task.tempFile.exists()) {
                task.tempFile.delete()
            }
            
            Timber.d("取消下载: $downloadId")
        }
    }
    
    /**
     * 获取下载状态
     */
    fun getDownloadState(downloadId: String): DownloadState? {
        return activeDownloads[downloadId]?.currentState
    }
    
    /**
     * 获取所有活跃下载
     */
    fun getActiveDownloads(): List<DownloadState> {
        return activeDownloads.values.map { it.currentState }
    }
    
    /**
     * 执行下载
     */
    private suspend fun performDownload(task: DownloadTask) {
        // 检查网络连接
        if (!networkMonitor.isConnected()) {
            task.updateState(DownloadState.Error(task.id, "网络不可用"))
            return
        }
        
        // 获取已下载的大小（断点续传）
        val downloadedSize = if (task.tempFile.exists()) task.tempFile.length() else 0L
        
        val request = Request.Builder()
            .url(task.url)
            .apply {
                if (downloadedSize > 0) {
                    addHeader("Range", "bytes=$downloadedSize-")
                }
            }
            .build()
        
        task.updateState(DownloadState.Downloading(task.id, 0, 0, 0f))
        
        try {
            val response = okHttpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                task.updateState(DownloadState.Error(task.id, "HTTP错误: ${response.code}"))
                return
            }
            
            val contentLength = response.header("Content-Length")?.toLongOrNull() ?: 0L
            val totalSize = if (response.code == 206) { // 部分内容
                downloadedSize + contentLength
            } else {
                contentLength
            }
            
            downloadFile(task, response, downloadedSize, totalSize)
            
        } catch (e: IOException) {
            if (task.isCancelled) {
                task.updateState(DownloadState.Cancelled(task.id))
            } else {
                task.updateState(DownloadState.Error(task.id, e.message ?: "下载失败"))
            }
        }
    }
    
    /**
     * 下载文件内容
     */
    private suspend fun downloadFile(
        task: DownloadTask,
        response: Response,
        initialDownloadedSize: Long,
        totalSize: Long
    ) {
        val inputStream = response.body?.byteStream() ?: return
        val outputStream = FileOutputStream(task.tempFile, initialDownloadedSize > 0)
        
        val buffer = ByteArray(BUFFER_SIZE)
        var downloadedBytes = initialDownloadedSize
        var lastUpdateTime = System.currentTimeMillis()
        
        try {
            while (!task.isCancelled) {
                // 检查是否暂停
                while (task.isPaused && !task.isCancelled) {
                    delay(100)
                }
                
                if (task.isCancelled) break
                
                val bytesRead = inputStream.read(buffer)
                if (bytesRead == -1) break
                
                outputStream.write(buffer, 0, bytesRead)
                downloadedBytes += bytesRead
                
                // 更新进度（限制更新频率）
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastUpdateTime > 500) { // 每500ms更新一次
                    val progress = if (totalSize > 0) {
                        (downloadedBytes.toFloat() / totalSize * 100)
                    } else {
                        0f
                    }
                    
                    task.updateState(
                        DownloadState.Downloading(
                            id = task.id,
                            downloadedBytes = downloadedBytes,
                            totalBytes = totalSize,
                            progress = progress
                        )
                    )
                    
                    lastUpdateTime = currentTime
                }
            }
            
            if (task.isCancelled) {
                task.updateState(DownloadState.Cancelled(task.id))
            } else {
                // 下载完成，移动临时文件到目标位置
                if (task.tempFile.renameTo(task.targetFile)) {
                    task.updateState(DownloadState.Completed(task.id, task.targetFile.absolutePath))
                    Timber.d("下载完成: ${task.fileName}")
                } else {
                    task.updateState(DownloadState.Error(task.id, "文件移动失败"))
                }
            }
            
        } finally {
            inputStream.close()
            outputStream.close()
        }
    }
    
    /**
     * 生成下载ID
     */
    private fun generateDownloadId(url: String, fileName: String): String {
        return "${url.hashCode()}_${fileName.hashCode()}"
    }
    
    /**
     * 清理已完成的下载记录
     */
    fun clearCompletedDownloads() {
        val completedTasks = activeDownloads.values.filter { 
            it.currentState is DownloadState.Completed || it.currentState is DownloadState.Error 
        }
        
        completedTasks.forEach { task ->
            activeDownloads.remove(task.id)
        }
        
        Timber.d("清理了 ${completedTasks.size} 个已完成的下载任务")
    }
}

/**
 * 下载任务
 */
private class DownloadTask(
    val id: String,
    val url: String,
    val fileName: String,
    val targetFile: File,
    val tempFile: File
) {
    private val _stateFlow = MutableStateFlow<DownloadState>(DownloadState.Pending(id))
    val stateFlow: StateFlow<DownloadState> = _stateFlow.asStateFlow()
    
    var currentState: DownloadState = DownloadState.Pending(id)
        private set
    
    @Volatile
    var isPaused = false
        private set
    
    @Volatile
    var isCancelled = false
        private set
    
    fun updateState(newState: DownloadState) {
        currentState = newState
        _stateFlow.value = newState
    }
    
    fun pause() {
        isPaused = true
        updateState(DownloadState.Paused(id))
    }
    
    fun resume() {
        isPaused = false
        updateState(DownloadState.Downloading(id, 0, 0, 0f))
    }
    
    fun cancel() {
        isCancelled = true
        updateState(DownloadState.Cancelled(id))
    }
}

/**
 * 下载状态密封类
 */
sealed class DownloadState(val id: String) {
    data class Pending(val downloadId: String) : DownloadState(downloadId)
    data class Downloading(
        val downloadId: String,
        val downloadedBytes: Long,
        val totalBytes: Long,
        val progress: Float
    ) : DownloadState(downloadId)
    data class Paused(val downloadId: String) : DownloadState(downloadId)
    data class Completed(val downloadId: String, val filePath: String) : DownloadState(downloadId)
    data class Cancelled(val downloadId: String) : DownloadState(downloadId)
    data class Error(val downloadId: String, val message: String) : DownloadState(downloadId)
}

/**
 * 下载状态扩展函数
 */
fun DownloadState.getDisplayName(): String = when (this) {
    is DownloadState.Pending -> "等待中"
    is DownloadState.Downloading -> "下载中"
    is DownloadState.Paused -> "已暂停"
    is DownloadState.Completed -> "已完成"
    is DownloadState.Cancelled -> "已取消"
    is DownloadState.Error -> "下载失败"
}

fun DownloadState.isActive(): Boolean = when (this) {
    is DownloadState.Pending, is DownloadState.Downloading, is DownloadState.Paused -> true
    else -> false
}
