package com.cadence.core.database.entity.learning

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 单词数据库实体
 */
@Entity(tableName = "words")
data class WordEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "text")
    val text: String,
    
    @ColumnInfo(name = "language")
    val language: String,
    
    @ColumnInfo(name = "pronunciation")
    val pronunciation: String? = null,
    
    @ColumnInfo(name = "definition")
    val definition: String,
    
    @ColumnInfo(name = "example")
    val example: String? = null,
    
    @ColumnInfo(name = "translation")
    val translation: String,
    
    @ColumnInfo(name = "translation_language")
    val translationLanguage: String,
    
    @ColumnInfo(name = "difficulty")
    val difficulty: String,
    
    @ColumnInfo(name = "category")
    val category: String,
    
    @ColumnInfo(name = "tags")
    val tags: String, // JSON字符串存储标签列表
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)