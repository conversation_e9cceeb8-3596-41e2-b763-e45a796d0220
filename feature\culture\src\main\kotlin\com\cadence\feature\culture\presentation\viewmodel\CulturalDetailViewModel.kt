package com.cadence.feature.culture.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.culture.*
import com.cadence.domain.repository.culture.CulturalContextRepository
import com.cadence.feature.culture.presentation.state.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 文化详情界面ViewModel
 * 负责管理单个文化背景的详细信息、学习笔记、相关推荐等功能
 */
@HiltViewModel
class CulturalDetailViewModel @Inject constructor(
    private val culturalContextRepository: CulturalContextRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CulturalDetailUiState())
    val uiState: StateFlow<CulturalDetailUiState> = _uiState.asStateFlow()

    private val _userInteractionState = MutableStateFlow(UserInteractionState())
    val userInteractionState: StateFlow<UserInteractionState> = _userInteractionState.asStateFlow()

    /**
     * 加载文化背景详情
     */
    fun loadCulturalDetail(contextId: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            culturalContextRepository.getCulturalContextById(contextId)
                .onSuccess { context ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            culturalContext = context,
                            error = null
                        )
                    }
                    
                    // 加载用户状态和相关推荐
                    loadUserStatus(contextId)
                    loadRelatedContexts(context)
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = error.message ?: "加载文化背景详情失败"
                        )
                    }
                }
        }
    }

    /**
     * 收藏/取消收藏
     */
    fun toggleBookmark() {
        val contextId = _uiState.value.culturalContext?.id ?: return
        val isCurrentlyBookmarked = _uiState.value.isBookmarked
        
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isBookmarking = true,
                    lastAction = UserAction.Bookmark
                )
            }
            
            val result = if (isCurrentlyBookmarked) {
                culturalContextRepository.unbookmarkCulturalContext(contextId)
            } else {
                culturalContextRepository.bookmarkCulturalContext(contextId)
            }
            
            result
                .onSuccess {
                    _uiState.update { 
                        it.copy(isBookmarked = !isCurrentlyBookmarked)
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "收藏操作失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isBookmarking = false, lastAction = null)
            }
        }
    }

    /**
     * 标记/取消标记为已学习
     */
    fun toggleLearned() {
        val contextId = _uiState.value.culturalContext?.id ?: return
        val isCurrentlyLearned = _uiState.value.isLearned
        
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isMarkingAsLearned = true,
                    lastAction = UserAction.MarkAsLearned
                )
            }
            
            val result = if (isCurrentlyLearned) {
                culturalContextRepository.unmarkCulturalContextAsLearned(contextId)
            } else {
                culturalContextRepository.markCulturalContextAsLearned(contextId)
            }
            
            result
                .onSuccess {
                    _uiState.update { 
                        it.copy(isLearned = !isCurrentlyLearned)
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "学习状态更新失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isMarkingAsLearned = false, lastAction = null)
            }
        }
    }

    /**
     * 保存学习笔记
     */
    fun saveLearningNotes(notes: String) {
        val contextId = _uiState.value.culturalContext?.id ?: return
        
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isSaving = true,
                    lastAction = UserAction.SaveNotes
                )
            }
            
            culturalContextRepository.saveLearningNotes(contextId, notes)
                .onSuccess {
                    _uiState.update { 
                        it.copy(
                            learningNotes = notes,
                            isNotesEditing = false
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "保存笔记失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isSaving = false, lastAction = null)
            }
        }
    }

    /**
     * 开始编辑笔记
     */
    fun startEditingNotes() {
        _uiState.update { it.copy(isNotesEditing = true) }
    }

    /**
     * 取消编辑笔记
     */
    fun cancelEditingNotes() {
        _uiState.update { it.copy(isNotesEditing = false) }
    }

    /**
     * 分享文化背景
     */
    fun shareCulturalContext() {
        val context = _uiState.value.culturalContext ?: return
        
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isSharing = true,
                    lastAction = UserAction.Share
                )
            }
            
            // 生成分享内容
            val shareContent = generateShareContent(context)
            
            // 这里可以调用系统分享功能或复制到剪贴板
            // 实际实现需要在UI层处理
            
            _userInteractionState.update { 
                it.copy(isSharing = false, lastAction = null)
            }
        }
    }

    /**
     * 加载更多相关文化背景
     */
    fun loadMoreRelatedContexts() {
        val currentContext = _uiState.value.culturalContext ?: return
        
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingRelated = true) }
            
            culturalContextRepository.getRelatedCulturalContexts(
                contextId = currentContext.id,
                limit = 10
            )
                .onSuccess { relatedContexts ->
                    _uiState.update { 
                        it.copy(
                            relatedContexts = relatedContexts,
                            isLoadingRelated = false
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isLoadingRelated = false,
                            error = error.message ?: "加载相关内容失败"
                        )
                    }
                }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * 重试加载
     */
    fun retry(contextId: String) {
        loadCulturalDetail(contextId)
    }

    /**
     * 获取文化背景的学习统计
     */
    fun getCulturalStatistics(): StateFlow<CulturalStatistics?> {
        return _uiState.map { state ->
            state.culturalContext?.let { context ->
                CulturalStatistics(
                    totalUsageContexts = context.usageContext.size,
                    totalRegionalDifferences = context.regionalDifferences.size,
                    totalExamples = context.examples.size,
                    difficultyLevel = context.difficulty,
                    appropriatenessDistribution = context.usageContext
                        .groupBy { it.appropriateness }
                        .mapValues { it.value.size },
                    knowledgeType = context.knowledgeType,
                    hasLearningNotes = state.learningNotes.isNotEmpty(),
                    isBookmarked = state.isBookmarked,
                    isLearned = state.isLearned
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    }

    // 私有辅助方法

    /**
     * 加载用户状态（收藏、学习、笔记）
     */
    private suspend fun loadUserStatus(contextId: String) {
        // 加载收藏状态
        culturalContextRepository.isContextBookmarked(contextId)
            .onSuccess { isBookmarked ->
                _uiState.update { it.copy(isBookmarked = isBookmarked) }
            }
        
        // 加载学习状态
        culturalContextRepository.isContextLearned(contextId)
            .onSuccess { isLearned ->
                _uiState.update { it.copy(isLearned = isLearned) }
            }
        
        // 加载学习笔记
        culturalContextRepository.getLearningNotes(contextId)
            .onSuccess { notes ->
                _uiState.update { it.copy(learningNotes = notes) }
            }
    }

    /**
     * 加载相关文化背景
     */
    private suspend fun loadRelatedContexts(context: CulturalContext) {
        _uiState.update { it.copy(isLoadingRelated = true) }
        
        culturalContextRepository.getRelatedCulturalContexts(
            contextId = context.id,
            limit = 5
        )
            .onSuccess { relatedContexts ->
                _uiState.update { 
                    it.copy(
                        relatedContexts = relatedContexts,
                        isLoadingRelated = false
                    )
                }
            }
            .onFailure { 
                _uiState.update { it.copy(isLoadingRelated = false) }
            }
    }

    /**
     * 生成分享内容
     */
    private fun generateShareContent(context: CulturalContext): String {
        return buildString {
            appendLine("📚 文化背景分享")
            appendLine()
            appendLine("🔤 单词：${context.word}")
            appendLine("🌍 地区：${context.region}")
            appendLine("📖 文化含义：")
            appendLine(context.culturalMeaning)
            appendLine()
            appendLine("💡 使用场景：")
            context.usageContext.take(2).forEach { usage ->
                appendLine("• ${usage.context}: ${usage.description}")
            }
            appendLine()
            appendLine("来自 Cadence 翻译应用")
        }
    }
}

/**
 * 文化背景统计数据
 */
data class CulturalStatistics(
    val totalUsageContexts: Int,
    val totalRegionalDifferences: Int,
    val totalExamples: Int,
    val difficultyLevel: CulturalDifficulty,
    val appropriatenessDistribution: Map<AppropriatenessLevel, Int>,
    val knowledgeType: CulturalKnowledgeType,
    val hasLearningNotes: Boolean,
    val isBookmarked: Boolean,
    val isLearned: Boolean
)