package com.cadence.data.di

import com.cadence.data.repository.LanguageRepositoryImpl
import com.cadence.data.repository.TranslationRepositoryImpl
import com.cadence.data.repository.UserPreferenceRepositoryImpl
import com.cadence.data.repository.TagRepositoryImpl
import com.cadence.data.repository.SyncRepositoryImpl
import com.cadence.domain.repository.LanguageRepository
import com.cadence.domain.repository.TranslationRepository
import com.cadence.domain.repository.UserPreferenceRepository
import com.cadence.domain.repository.TagRepository
import com.cadence.domain.repository.SyncRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Repository依赖注入模块
 * 将Repository实现绑定到对应的接口
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {
    
    /**
     * 绑定翻译Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindTranslationRepository(
        translationRepositoryImpl: TranslationRepositoryImpl
    ): TranslationRepository
    
    /**
     * 绑定语言Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindLanguageRepository(
        languageRepositoryImpl: LanguageRepositoryImpl
    ): LanguageRepository
    
    /**
     * 绑定用户偏好Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindUserPreferenceRepository(
        userPreferenceRepositoryImpl: UserPreferenceRepositoryImpl
    ): UserPreferenceRepository

    /**
     * 绑定标签Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindTagRepository(
        tagRepositoryImpl: TagRepositoryImpl
    ): TagRepository

    /**
     * 绑定同步Repository实现
     */
    @Binds
    @Singleton
    abstract fun bindSyncRepository(
        syncRepositoryImpl: SyncRepositoryImpl
    ): SyncRepository
}
