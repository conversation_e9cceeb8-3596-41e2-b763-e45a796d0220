package com.cadence.feature.learning.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.learning.presentation.viewmodel.LearningViewModel
import com.cadence.feature.learning.presentation.component.WordCard
import com.cadence.feature.learning.presentation.component.LearningModeSelector
import com.cadence.feature.learning.presentation.component.ProgressIndicator

/**
 * 学习主界面
 * 提供单词学习、复习、测验等功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LearningScreen(
    onNavigateToWordDetail: (String) -> Unit,
    onNavigateToProgress: () -> Unit,
    onNavigateToStatistics: () -> Unit,
    viewModel: LearningViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val currentWords by viewModel.currentWords.collectAsStateWithLifecycle()
    val learningProgress by viewModel.learningProgress.collectAsStateWithLifecycle()
    
    LaunchedEffect(Unit) {
        viewModel.loadLearningData()
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 顶部标题和操作栏
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "单词学习",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                IconButton(onClick = onNavigateToProgress) {
                    Icon(
                        imageVector = Icons.Default.TrendingUp,
                        contentDescription = "学习进度"
                    )
                }
                IconButton(onClick = onNavigateToStatistics) {
                    Icon(
                        imageVector = Icons.Default.Analytics,
                        contentDescription = "学习统计"
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 学习进度指示器
        ProgressIndicator(
            totalWords = learningProgress?.totalWords ?: 0,
            masteredWords = learningProgress?.masteredWords ?: 0,
            wordsToReview = learningProgress?.wordsToReview ?: 0,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 学习模式选择器
        LearningModeSelector(
            onModeSelected = { mode ->
                viewModel.setLearningMode(mode)
            },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 单词列表
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (currentWords.isEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Default.School,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "暂无学习内容",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "开始翻译单词来建立您的学习词库",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(currentWords) { word ->
                    WordCard(
                        word = word,
                        progress = learningProgress?.getWordProgress(word.id),
                        onClick = { onNavigateToWordDetail(word.id) },
                        onBookmarkToggle = { viewModel.toggleBookmark(word.id) },
                        onStudy = { viewModel.studyWord(word.id) }
                    )
                }
            }
        }
        
        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误提示
            }
        }
    }
}