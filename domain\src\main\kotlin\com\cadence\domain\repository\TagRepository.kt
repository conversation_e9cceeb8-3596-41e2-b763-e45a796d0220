package com.cadence.domain.repository

import com.cadence.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 标签数据仓库接口
 * 定义标签相关的数据操作契约
 */
interface TagRepository {
    
    /**
     * 获取所有标签
     * @return 标签列表流
     */
    fun getAllTags(): Flow<List<Tag>>
    
    /**
     * 根据ID获取标签
     * @param tagId 标签ID
     * @return 标签对象
     */
    suspend fun getTagById(tagId: String): Result<Tag?>
    
    /**
     * 根据名称获取标签
     * @param name 标签名称
     * @return 标签对象
     */
    suspend fun getTagByName(name: String): Result<Tag?>
    
    /**
     * 搜索标签
     * @param query 搜索关键词
     * @return 匹配的标签列表流
     */
    fun searchTags(query: String): Flow<List<Tag>>
    
    /**
     * 获取热门标签
     * @param limit 限制数量
     * @return 热门标签列表流
     */
    fun getPopularTags(limit: Int = 10): Flow<List<Tag>>
    
    /**
     * 创建标签
     * @param request 创建请求
     * @return 创建的标签
     */
    suspend fun createTag(request: CreateTagRequest): Result<Tag>
    
    /**
     * 更新标签
     * @param tagId 标签ID
     * @param request 更新请求
     * @return 更新的标签
     */
    suspend fun updateTag(tagId: String, request: UpdateTagRequest): Result<Tag>
    
    /**
     * 删除标签
     * @param tagId 标签ID
     */
    suspend fun deleteTag(tagId: String): Result<Unit>
    
    /**
     * 获取翻译的标签
     * @param translationId 翻译ID
     * @return 标签列表流
     */
    fun getTagsForTranslation(translationId: String): Flow<List<Tag>>
    
    /**
     * 获取使用指定标签的翻译ID列表
     * @param tagId 标签ID
     * @return 翻译ID列表流
     */
    fun getTranslationIdsForTag(tagId: String): Flow<List<String>>
    
    /**
     * 为翻译添加标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     */
    suspend fun addTagToTranslation(translationId: String, tagId: String): Result<Unit>
    
    /**
     * 批量为翻译添加标签
     * @param translationId 翻译ID
     * @param tagIds 标签ID列表
     */
    suspend fun addTagsToTranslation(translationId: String, tagIds: List<String>): Result<Unit>
    
    /**
     * 从翻译中移除标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     */
    suspend fun removeTagFromTranslation(translationId: String, tagId: String): Result<Unit>
    
    /**
     * 移除翻译的所有标签
     * @param translationId 翻译ID
     */
    suspend fun removeAllTagsFromTranslation(translationId: String): Result<Unit>
    
    /**
     * 检查翻译是否有指定标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     * @return 是否存在关联
     */
    suspend fun hasTag(translationId: String, tagId: String): Result<Boolean>
    
    /**
     * 获取标签统计信息
     * @return 统计信息
     */
    suspend fun getTagStatistics(): Result<TagStatistics>
    
    /**
     * 清理未使用的标签
     */
    suspend fun cleanupUnusedTags(): Result<Unit>
}
