package com.cadence.feature.ocr.camera

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import timber.log.Timber
import java.io.ByteArrayOutputStream
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 相机管理器
 * 负责CameraX的初始化、配置和图片捕获
 */
@Singleton
class CameraManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var imageCapture: ImageCapture? = null
    private var preview: Preview? = null
    
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()
    
    private val _cameraState = MutableStateFlow(CameraState.IDLE)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()
    
    private val _flashMode = MutableStateFlow(ImageCapture.FLASH_MODE_AUTO)
    val flashMode: StateFlow<Int> = _flashMode.asStateFlow()
    
    companion object {
        private const val RATIO_4_3_VALUE = 4.0 / 3.0
        private const val RATIO_16_9_VALUE = 16.0 / 9.0
    }
    
    /**
     * 相机状态
     */
    enum class CameraState {
        IDLE,           // 空闲
        INITIALIZING,   // 初始化中
        READY,          // 准备就绪
        CAPTURING,      // 拍照中
        ERROR           // 错误状态
    }
    
    /**
     * 拍照结果
     */
    sealed class CaptureResult {
        data class Success(val bitmap: Bitmap, val rotationDegrees: Int) : CaptureResult()
        data class Error(val exception: Throwable) : CaptureResult()
    }
    
    /**
     * 初始化相机
     */
    suspend fun initializeCamera(
        lifecycleOwner: LifecycleOwner,
        previewView: PreviewView,
        lensFacing: Int = CameraSelector.LENS_FACING_BACK
    ): Boolean {
        return try {
            _cameraState.value = CameraState.INITIALIZING
            
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            cameraProvider = cameraProviderFuture.get()
            
            // 配置预览
            preview = Preview.Builder()
                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }
            
            // 配置图片捕获
            imageCapture = ImageCapture.Builder()
                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MAXIMIZE_QUALITY)
                .setFlashMode(_flashMode.value)
                .build()
            
            // 选择相机
            val cameraSelector = CameraSelector.Builder()
                .requireLensFacing(lensFacing)
                .build()
            
            // 绑定用例到生命周期
            cameraProvider?.unbindAll()
            camera = cameraProvider?.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture
            )
            
            _cameraState.value = CameraState.READY
            Timber.d("相机初始化成功")
            true
        } catch (e: Exception) {
            Timber.e(e, "相机初始化失败")
            _cameraState.value = CameraState.ERROR
            false
        }
    }
    
    /**
     * 拍照
     */
    suspend fun capturePhoto(): Flow<CaptureResult> = callbackFlow {
        if (_cameraState.value != CameraState.READY) {
            trySend(CaptureResult.Error(IllegalStateException("相机未准备就绪")))
            close()
            return@callbackFlow
        }
        
        _cameraState.value = CameraState.CAPTURING
        
        val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
            createTempImageFile()
        ).build()
        
        imageCapture?.takePicture(
            outputFileOptions,
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageSavedCallback {
                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    try {
                        val bitmap = BitmapFactory.decodeFile(output.savedUri?.path)
                        val rotatedBitmap = rotateBitmapIfNeeded(bitmap, 0) // 根据EXIF信息旋转
                        trySend(CaptureResult.Success(rotatedBitmap, 0))
                        _cameraState.value = CameraState.READY
                    } catch (e: Exception) {
                        trySend(CaptureResult.Error(e))
                        _cameraState.value = CameraState.READY
                    }
                    close()
                }
                
                override fun onError(exception: ImageCaptureException) {
                    trySend(CaptureResult.Error(exception))
                    _cameraState.value = CameraState.READY
                    close()
                }
            }
        )
        
        awaitClose {
            _cameraState.value = CameraState.READY
        }
    }
    
    /**
     * 内存拍照（直接返回Bitmap）
     */
    suspend fun capturePhotoInMemory(): Flow<CaptureResult> = callbackFlow {
        if (_cameraState.value != CameraState.READY) {
            trySend(CaptureResult.Error(IllegalStateException("相机未准备就绪")))
            close()
            return@callbackFlow
        }
        
        _cameraState.value = CameraState.CAPTURING
        
        imageCapture?.takePicture(
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageCapturedCallback() {
                override fun onCaptureSuccess(image: ImageProxy) {
                    try {
                        val bitmap = imageProxyToBitmap(image)
                        val rotatedBitmap = rotateBitmapIfNeeded(bitmap, image.imageInfo.rotationDegrees)
                        trySend(CaptureResult.Success(rotatedBitmap, image.imageInfo.rotationDegrees))
                        _cameraState.value = CameraState.READY
                    } catch (e: Exception) {
                        trySend(CaptureResult.Error(e))
                        _cameraState.value = CameraState.READY
                    } finally {
                        image.close()
                        close()
                    }
                }
                
                override fun onError(exception: ImageCaptureException) {
                    trySend(CaptureResult.Error(exception))
                    _cameraState.value = CameraState.READY
                    close()
                }
            }
        )
        
        awaitClose {
            _cameraState.value = CameraState.READY
        }
    }
    
    /**
     * 切换闪光灯模式
     */
    fun toggleFlashMode() {
        val newMode = when (_flashMode.value) {
            ImageCapture.FLASH_MODE_OFF -> ImageCapture.FLASH_MODE_AUTO
            ImageCapture.FLASH_MODE_AUTO -> ImageCapture.FLASH_MODE_ON
            ImageCapture.FLASH_MODE_ON -> ImageCapture.FLASH_MODE_OFF
            else -> ImageCapture.FLASH_MODE_AUTO
        }
        
        _flashMode.value = newMode
        imageCapture?.flashMode = newMode
        Timber.d("闪光灯模式切换为: $newMode")
    }
    
    /**
     * 获取闪光灯模式名称
     */
    fun getFlashModeName(mode: Int): String {
        return when (mode) {
            ImageCapture.FLASH_MODE_OFF -> "关闭"
            ImageCapture.FLASH_MODE_AUTO -> "自动"
            ImageCapture.FLASH_MODE_ON -> "开启"
            else -> "未知"
        }
    }
    
    /**
     * 检查是否有闪光灯
     */
    fun hasFlashUnit(): Boolean {
        return camera?.cameraInfo?.hasFlashUnit() ?: false
    }
    
    /**
     * 对焦到指定点
     */
    fun focusOnPoint(x: Float, y: Float, previewView: PreviewView) {
        try {
            val factory = previewView.meteringPointFactory
            val point = factory.createPoint(x, y)
            val action = FocusMeteringAction.Builder(point).build()
            
            camera?.cameraControl?.startFocusAndMetering(action)
            Timber.d("对焦到点: ($x, $y)")
        } catch (e: Exception) {
            Timber.e(e, "对焦失败")
        }
    }
    
    /**
     * 缩放控制
     */
    fun setZoomRatio(ratio: Float) {
        try {
            camera?.cameraControl?.setZoomRatio(ratio)
        } catch (e: Exception) {
            Timber.e(e, "设置缩放比例失败")
        }
    }
    
    /**
     * 获取缩放状态
     */
    fun getZoomState(): LiveData<ZoomState>? {
        return camera?.cameraInfo?.zoomState
    }
    
    /**
     * ImageProxy转Bitmap
     */
    private fun imageProxyToBitmap(image: ImageProxy): Bitmap {
        val buffer = image.planes[0].buffer
        val bytes = ByteArray(buffer.remaining())
        buffer.get(bytes)
        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }
    
    /**
     * 根据旋转角度旋转Bitmap
     */
    private fun rotateBitmapIfNeeded(bitmap: Bitmap, rotationDegrees: Int): Bitmap {
        return if (rotationDegrees != 0) {
            val matrix = Matrix().apply {
                postRotate(rotationDegrees.toFloat())
            }
            Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        } else {
            bitmap
        }
    }
    
    /**
     * 创建临时图片文件
     */
    private fun createTempImageFile(): java.io.File {
        val timeStamp = System.currentTimeMillis()
        return java.io.File(context.cacheDir, "temp_image_$timeStamp.jpg")
    }
    
    /**
     * 释放相机资源
     */
    fun release() {
        try {
            cameraProvider?.unbindAll()
            cameraExecutor.shutdown()
            _cameraState.value = CameraState.IDLE
            Timber.d("相机资源已释放")
        } catch (e: Exception) {
            Timber.e(e, "释放相机资源失败")
        }
    }
}
