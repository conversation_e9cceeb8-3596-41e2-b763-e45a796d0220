package com.cadence.feature.offline

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle

/**
 * 语言包管理主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguagePackScreen(
    onNavigateBack: () -> Unit,
    viewModel: LanguagePackViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val availableLanguagePacks by viewModel.availableLanguagePacks.collectAsStateWithLifecycle()
    val installedLanguagePacks by viewModel.installedLanguagePacks.collectAsStateWithLifecycle()
    val downloadProgress by viewModel.downloadProgress.collectAsStateWithLifecycle()
    val storageState by viewModel.storageState.collectAsStateWithLifecycle()
    
    var selectedTab by remember { mutableIntStateOf(0) }
    val tabs = listOf("可下载", "已安装", "存储管理")
    
    // 显示错误消息
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // TODO: 显示错误Snackbar
        }
    }
    
    // 显示成功消息
    LaunchedEffect(uiState.successMessage) {
        uiState.successMessage?.let {
            // TODO: 显示成功Snackbar
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("语言包管理") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(onClick = { viewModel.refresh() }) {
                    Icon(Icons.Default.Refresh, contentDescription = "刷新")
                }
            }
        )
        
        // 存储状态卡片
        StorageStatusCard(
            storageState = storageState,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
        
        // 标签页
        TabRow(selectedTabIndex = selectedTab) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }
        
        // 标签页内容
        when (selectedTab) {
            0 -> AvailableLanguagePacksTab(
                languagePacks = availableLanguagePacks,
                downloadProgress = downloadProgress,
                isLoading = uiState.isLoading,
                onDownload = viewModel::downloadLanguagePack,
                onCancelDownload = viewModel::cancelDownload,
                onPauseDownload = viewModel::pauseDownload,
                onResumeDownload = viewModel::resumeDownload,
                modifier = Modifier.fillMaxSize()
            )
            1 -> InstalledLanguagePacksTab(
                languagePacks = installedLanguagePacks,
                isLoading = uiState.isLoading,
                onUninstall = viewModel::uninstallLanguagePack,
                modifier = Modifier.fillMaxSize()
            )
            2 -> StorageManagementTab(
                storageState = storageState,
                cleanupSuggestions = viewModel.getCleanupSuggestions(),
                storageDetails = viewModel.getStorageDetails(),
                isPerformingCleanup = uiState.isPerformingCleanup,
                onPerformCleanup = viewModel::performStorageCleanup,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * 存储状态卡片
 */
@Composable
private fun StorageStatusCard(
    storageState: StorageState,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = when {
                storageState.usagePercentage >= 95 -> MaterialTheme.colorScheme.errorContainer
                storageState.usagePercentage >= 85 -> MaterialTheme.colorScheme.tertiaryContainer
                else -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "存储空间",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "${storageState.usagePercentage}%",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = when {
                        storageState.usagePercentage >= 95 -> MaterialTheme.colorScheme.error
                        storageState.usagePercentage >= 85 -> MaterialTheme.colorScheme.tertiary
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = { storageState.usagePercentage / 100f },
                modifier = Modifier.fillMaxWidth(),
                color = when {
                    storageState.usagePercentage >= 95 -> MaterialTheme.colorScheme.error
                    storageState.usagePercentage >= 85 -> MaterialTheme.colorScheme.tertiary
                    else -> MaterialTheme.colorScheme.primary
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "已用: ${storageState.usedSpaceMB}MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "可用: ${storageState.availableSpaceMB}MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 可下载语言包标签页
 */
@Composable
private fun AvailableLanguagePacksTab(
    languagePacks: List<LanguagePackItem>,
    downloadProgress: Map<String, DownloadProgress>,
    isLoading: Boolean,
    onDownload: (LanguagePackItem) -> Unit,
    onCancelDownload: (String) -> Unit,
    onPauseDownload: (String) -> Unit,
    onResumeDownload: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    if (isLoading) {
        Box(
            modifier = modifier,
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else {
        LazyColumn(
            modifier = modifier,
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(languagePacks) { languagePack ->
                AvailableLanguagePackItem(
                    languagePack = languagePack,
                    downloadProgress = downloadProgress[languagePack.id],
                    onDownload = { onDownload(languagePack) },
                    onCancelDownload = { onCancelDownload(languagePack.id) },
                    onPauseDownload = { onPauseDownload(languagePack.id) },
                    onResumeDownload = { onResumeDownload(languagePack.id) }
                )
            }
        }
    }
}

/**
 * 已安装语言包标签页
 */
@Composable
private fun InstalledLanguagePacksTab(
    languagePacks: List<LanguagePackItem>,
    isLoading: Boolean,
    onUninstall: (LanguagePackItem) -> Unit,
    modifier: Modifier = Modifier
) {
    if (isLoading) {
        Box(
            modifier = modifier,
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else if (languagePacks.isEmpty()) {
        Box(
            modifier = modifier,
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    Icons.Default.CloudDownload,
                    contentDescription = null,
                    modifier = Modifier.size(64.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "暂无已安装的语言包",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "前往"可下载"标签页下载语言包",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    } else {
        LazyColumn(
            modifier = modifier,
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(languagePacks) { languagePack ->
                InstalledLanguagePackItem(
                    languagePack = languagePack,
                    onUninstall = { onUninstall(languagePack) }
                )
            }
        }
    }
}

/**
 * 可下载语言包项目
 */
@Composable
private fun AvailableLanguagePackItem(
    languagePack: LanguagePackItem,
    downloadProgress: DownloadProgress?,
    onDownload: () -> Unit,
    onCancelDownload: () -> Unit,
    onPauseDownload: () -> Unit,
    onResumeDownload: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = languagePack.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = languagePack.languagePair,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = languagePack.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = languagePack.sizeText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "准确率: ${languagePack.accuracyText}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // 下载进度或操作按钮
            if (downloadProgress != null) {
                DownloadProgressSection(
                    progress = downloadProgress,
                    onCancel = onCancelDownload,
                    onPause = onPauseDownload,
                    onResume = onResumeDownload
                )
            } else {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    Button(
                        onClick = onDownload,
                        modifier = Modifier.height(36.dp)
                    ) {
                        Icon(
                            Icons.Default.Download,
                            contentDescription = null,
                            modifier = Modifier.size(18.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("下载")
                    }
                }
            }
        }
    }
}

/**
 * 下载进度区域
 */
@Composable
private fun DownloadProgressSection(
    progress: DownloadProgress,
    onCancel: () -> Unit,
    onPause: () -> Unit,
    onResume: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = when (progress.status) {
                    DownloadStatus.DOWNLOADING -> "下载中..."
                    DownloadStatus.PAUSED -> "已暂停"
                    DownloadStatus.COMPLETED -> "下载完成"
                    DownloadStatus.FAILED -> "下载失败"
                    DownloadStatus.CANCELLED -> "已取消"
                    else -> "准备中..."
                },
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = "${(progress.progress * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        LinearProgressIndicator(
            progress = { progress.progress },
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${progress.downloadedSize / (1024 * 1024)}MB / ${progress.totalSize / (1024 * 1024)}MB",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Row {
                when (progress.status) {
                    DownloadStatus.DOWNLOADING -> {
                        IconButton(
                            onClick = onPause,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                Icons.Default.Pause,
                                contentDescription = "暂停",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                    DownloadStatus.PAUSED -> {
                        IconButton(
                            onClick = onResume,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                Icons.Default.PlayArrow,
                                contentDescription = "恢复",
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                    else -> {}
                }

                IconButton(
                    onClick = onCancel,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "取消",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
    }
}

/**
 * 已安装语言包项目
 */
@Composable
private fun InstalledLanguagePackItem(
    languagePack: LanguagePackItem,
    onUninstall: () -> Unit
) {
    var showUninstallDialog by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = languagePack.name,
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Icon(
                            Icons.Default.CheckCircle,
                            contentDescription = "已安装",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    Text(
                        text = languagePack.languagePair,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = languagePack.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = languagePack.sizeText,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "v${languagePack.version}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                OutlinedButton(
                    onClick = { showUninstallDialog = true },
                    modifier = Modifier.height(36.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        Icons.Default.Delete,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("卸载")
                }
            }
        }
    }

    // 卸载确认对话框
    if (showUninstallDialog) {
        AlertDialog(
            onDismissRequest = { showUninstallDialog = false },
            title = { Text("确认卸载") },
            text = { Text("确定要卸载语言包"${languagePack.name}"吗？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onUninstall()
                        showUninstallDialog = false
                    }
                ) {
                    Text("卸载", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showUninstallDialog = false }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 存储管理标签页
 */
@Composable
private fun StorageManagementTab(
    storageState: StorageState,
    cleanupSuggestions: List<CleanupSuggestion>,
    storageDetails: StorageDetails,
    isPerformingCleanup: Boolean,
    onPerformCleanup: (CleanupStrategy) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 存储详情卡片
        item {
            StorageDetailsCard(storageDetails = storageDetails)
        }

        // 清理建议
        if (cleanupSuggestions.isNotEmpty()) {
            item {
                CleanupSuggestionsCard(
                    suggestions = cleanupSuggestions,
                    isPerformingCleanup = isPerformingCleanup,
                    onPerformCleanup = onPerformCleanup
                )
            }
        }

        // 手动清理选项
        item {
            ManualCleanupCard(
                isPerformingCleanup = isPerformingCleanup,
                onPerformCleanup = onPerformCleanup
            )
        }
    }
}

/**
 * 存储详情卡片
 */
@Composable
private fun StorageDetailsCard(
    storageDetails: StorageDetails
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "存储详情",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            StorageDetailRow("总空间", "${storageDetails.totalSpaceMB}MB")
            StorageDetailRow("已用空间", "${storageDetails.usedSpaceMB}MB")
            StorageDetailRow("可用空间", "${storageDetails.availableSpaceMB}MB")
            StorageDetailRow("使用率", "${storageDetails.usagePercentage}%")

            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(8.dp))

            StorageDetailRow("语言包数量", "${storageDetails.modelCount}个")
            StorageDetailRow("语言包大小", "${storageDetails.modelsSizeMB}MB")
            StorageDetailRow("缓存大小", "${storageDetails.cacheSize / (1024 * 1024)}MB")
            StorageDetailRow("临时文件", "${storageDetails.tempFilesSize / (1024 * 1024)}MB")
        }
    }
}

/**
 * 存储详情行
 */
@Composable
private fun StorageDetailRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * 清理建议卡片
 */
@Composable
private fun CleanupSuggestionsCard(
    suggestions: List<CleanupSuggestion>,
    isPerformingCleanup: Boolean,
    onPerformCleanup: (CleanupStrategy) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "清理建议",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            suggestions.forEach { suggestion ->
                CleanupSuggestionItem(
                    suggestion = suggestion,
                    isPerformingCleanup = isPerformingCleanup,
                    onPerformCleanup = onPerformCleanup
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 清理建议项目
 */
@Composable
private fun CleanupSuggestionItem(
    suggestion: CleanupSuggestion,
    isPerformingCleanup: Boolean,
    onPerformCleanup: (CleanupStrategy) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    when (suggestion.priority) {
                        CleanupPriority.HIGH -> Icons.Default.Warning
                        CleanupPriority.MEDIUM -> Icons.Default.Info
                        CleanupPriority.LOW -> Icons.Default.Lightbulb
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = when (suggestion.priority) {
                        CleanupPriority.HIGH -> MaterialTheme.colorScheme.error
                        CleanupPriority.MEDIUM -> MaterialTheme.colorScheme.tertiary
                        CleanupPriority.LOW -> MaterialTheme.colorScheme.primary
                    }
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = suggestion.description,
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            if (suggestion.estimatedSavingMB > 0) {
                Text(
                    text = "预计释放: ${suggestion.estimatedSavingMB}MB",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        if (suggestion.type != CleanupType.OLD_MODELS) {
            TextButton(
                onClick = {
                    val strategy = when (suggestion.type) {
                        CleanupType.CACHE -> CleanupStrategy.CACHE_ONLY
                        CleanupType.TEMP_FILES -> CleanupStrategy.SMART
                        else -> CleanupStrategy.SMART
                    }
                    onPerformCleanup(strategy)
                },
                enabled = !isPerformingCleanup
            ) {
                Text("清理")
            }
        }
    }
}

/**
 * 手动清理卡片
 */
@Composable
private fun ManualCleanupCard(
    isPerformingCleanup: Boolean,
    onPerformCleanup: (CleanupStrategy) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "手动清理",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = "选择清理策略来释放存储空间",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(16.dp))

            if (isPerformingCleanup) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    CircularProgressIndicator(modifier = Modifier.size(24.dp))
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "正在清理...",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            } else {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    CleanupStrategyButton(
                        title = "智能清理",
                        description = "自动选择最佳清理策略",
                        strategy = CleanupStrategy.SMART,
                        onPerformCleanup = onPerformCleanup
                    )

                    CleanupStrategyButton(
                        title = "仅清理缓存",
                        description = "只清理临时缓存文件",
                        strategy = CleanupStrategy.CACHE_ONLY,
                        onPerformCleanup = onPerformCleanup
                    )

                    CleanupStrategyButton(
                        title = "激进清理",
                        description = "清理所有可清理的内容",
                        strategy = CleanupStrategy.AGGRESSIVE,
                        onPerformCleanup = onPerformCleanup
                    )
                }
            }
        }
    }
}

/**
 * 清理策略按钮
 */
@Composable
private fun CleanupStrategyButton(
    title: String,
    description: String,
    strategy: CleanupStrategy,
    onPerformCleanup: (CleanupStrategy) -> Unit
) {
    OutlinedButton(
        onClick = { onPerformCleanup(strategy) },
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(vertical = 4.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
