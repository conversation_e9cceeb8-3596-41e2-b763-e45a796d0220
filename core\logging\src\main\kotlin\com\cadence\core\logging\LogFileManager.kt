package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.*
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.zip.GZIPOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志文件管理器
 * 负责日志文件的写入、轮转、压缩和清理
 */
@Singleton
class LogFileManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val LOG_DIR = "logs"
        private const val MAX_FILE_SIZE_MB = 10
        private const val MAX_FILES_PER_LEVEL = 5
        private const val LOG_RETENTION_DAYS = 7
        private const val BUFFER_SIZE = 8192
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timestampFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    // 日志目录
    private val logDir: File by lazy {
        File(context.filesDir, LOG_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    // 文件写入器缓存
    private val fileWriters = ConcurrentHashMap<LogLevel, BufferedWriter>()
    
    // 当前文件大小跟踪
    private val fileSizes = ConcurrentHashMap<LogLevel, Long>()
    
    // 文件锁
    private val fileLocks = ConcurrentHashMap<LogLevel, Any>()
    
    init {
        // 为每个日志级别创建锁
        LogLevel.values().forEach { level ->
            fileLocks[level] = Any()
        }
        
        // 启动定期清理任务
        startPeriodicCleanup()
        
        Timber.d("日志文件管理器已初始化，日志目录: ${logDir.absolutePath}")
    }
    
    /**
     * 写入日志
     */
    suspend fun writeLog(logContent: String, level: LogLevel) = withContext(Dispatchers.IO) {
        synchronized(fileLocks[level]!!) {
            try {
                val writer = getOrCreateWriter(level)
                writer.write(logContent)
                writer.newLine()
                
                // 更新文件大小
                val currentSize = fileSizes.getOrDefault(level, 0L)
                val newSize = currentSize + logContent.length + 1 // +1 for newline
                fileSizes[level] = newSize
                
                // 检查是否需要轮转
                if (newSize > MAX_FILE_SIZE_MB * 1024 * 1024) {
                    rotateLogFile(level)
                }
                
            } catch (e: Exception) {
                Timber.e(e, "写入日志文件失败: level=$level")
                // 尝试重新创建写入器
                recreateWriter(level)
            }
        }
    }
    
    /**
     * 获取或创建文件写入器
     */
    private fun getOrCreateWriter(level: LogLevel): BufferedWriter {
        return fileWriters.getOrPut(level) {
            createWriter(level)
        }
    }
    
    /**
     * 创建文件写入器
     */
    private fun createWriter(level: LogLevel): BufferedWriter {
        val logFile = getCurrentLogFile(level)
        val fileWriter = FileWriter(logFile, true) // append mode
        val bufferedWriter = BufferedWriter(fileWriter, BUFFER_SIZE)
        
        // 记录当前文件大小
        fileSizes[level] = logFile.length()
        
        Timber.d("创建日志写入器: ${logFile.name}, 当前大小: ${logFile.length()} bytes")
        
        return bufferedWriter
    }
    
    /**
     * 重新创建写入器
     */
    private fun recreateWriter(level: LogLevel) {
        try {
            fileWriters[level]?.close()
        } catch (e: Exception) {
            Timber.w(e, "关闭旧写入器失败")
        }
        
        fileWriters.remove(level)
        fileSizes.remove(level)
    }
    
    /**
     * 获取当前日志文件
     */
    private fun getCurrentLogFile(level: LogLevel): File {
        val today = dateFormat.format(Date())
        val fileName = "${level.name.lowercase()}_$today.log"
        return File(logDir, fileName)
    }
    
    /**
     * 轮转日志文件
     */
    private suspend fun rotateLogFile(level: LogLevel) = withContext(Dispatchers.IO) {
        try {
            Timber.d("开始轮转日志文件: $level")
            
            // 关闭当前写入器
            fileWriters[level]?.close()
            fileWriters.remove(level)
            
            // 重命名当前文件
            val currentFile = getCurrentLogFile(level)
            if (currentFile.exists()) {
                val timestamp = timestampFormat.format(Date())
                val rotatedFileName = "${level.name.lowercase()}_$timestamp.log"
                val rotatedFile = File(logDir, rotatedFileName)
                
                if (currentFile.renameTo(rotatedFile)) {
                    Timber.d("日志文件已轮转: ${currentFile.name} -> ${rotatedFile.name}")
                    
                    // 异步压缩旧文件
                    scope.launch {
                        compressLogFile(rotatedFile)
                    }
                } else {
                    Timber.w("日志文件轮转失败: ${currentFile.name}")
                }
            }
            
            // 重置文件大小
            fileSizes[level] = 0L
            
            // 清理旧文件
            cleanupOldFiles(level)
            
        } catch (e: Exception) {
            Timber.e(e, "轮转日志文件失败: $level")
        }
    }
    
    /**
     * 压缩日志文件
     */
    private suspend fun compressLogFile(logFile: File) = withContext(Dispatchers.IO) {
        try {
            val compressedFile = File(logFile.parent, "${logFile.nameWithoutExtension}.log.gz")
            
            FileInputStream(logFile).use { fis ->
                FileOutputStream(compressedFile).use { fos ->
                    GZIPOutputStream(fos).use { gzos ->
                        fis.copyTo(gzos, BUFFER_SIZE)
                    }
                }
            }
            
            // 删除原文件
            if (logFile.delete()) {
                Timber.d("日志文件已压缩: ${logFile.name} -> ${compressedFile.name}")
            } else {
                Timber.w("删除原日志文件失败: ${logFile.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "压缩日志文件失败: ${logFile.name}")
        }
    }
    
    /**
     * 清理旧文件
     */
    private suspend fun cleanupOldFiles(level: LogLevel) = withContext(Dispatchers.IO) {
        try {
            val levelPrefix = level.name.lowercase()
            val logFiles = logDir.listFiles { _, name ->
                name.startsWith(levelPrefix) && (name.endsWith(".log") || name.endsWith(".log.gz"))
            }?.sortedByDescending { it.lastModified() }
            
            // 保留最新的文件，删除超出数量限制的文件
            logFiles?.drop(MAX_FILES_PER_LEVEL)?.forEach { file ->
                if (file.delete()) {
                    Timber.d("删除旧日志文件: ${file.name}")
                } else {
                    Timber.w("删除旧日志文件失败: ${file.name}")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "清理旧日志文件失败: $level")
        }
    }
    
    /**
     * 启动定期清理任务
     */
    private fun startPeriodicCleanup() {
        scope.launch {
            while (isActive) {
                try {
                    delay(24 * 60 * 60 * 1000L) // 每24小时执行一次
                    performPeriodicCleanup()
                } catch (e: Exception) {
                    Timber.e(e, "定期清理任务失败")
                }
            }
        }
    }
    
    /**
     * 执行定期清理
     */
    private suspend fun performPeriodicCleanup() = withContext(Dispatchers.IO) {
        try {
            val retentionTime = System.currentTimeMillis() - (LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000L)
            
            logDir.listFiles()?.filter { file ->
                file.lastModified() < retentionTime
            }?.forEach { file ->
                if (file.delete()) {
                    Timber.d("删除过期日志文件: ${file.name}")
                } else {
                    Timber.w("删除过期日志文件失败: ${file.name}")
                }
            }
            
            Timber.d("定期清理完成")
            
        } catch (e: Exception) {
            Timber.e(e, "定期清理失败")
        }
    }
    
    /**
     * 刷新所有写入器
     */
    suspend fun flush() = withContext(Dispatchers.IO) {
        fileWriters.values.forEach { writer ->
            try {
                writer.flush()
            } catch (e: Exception) {
                Timber.e(e, "刷新日志写入器失败")
            }
        }
    }
    
    /**
     * 获取日志文件列表
     */
    suspend fun getLogFiles(): List<LogFileInfo> = withContext(Dispatchers.IO) {
        try {
            logDir.listFiles()
                ?.filter { it.isFile && (it.name.endsWith(".log") || it.name.endsWith(".log.gz")) }
                ?.map { file ->
                    LogFileInfo(
                        name = file.name,
                        path = file.absolutePath,
                        size = file.length(),
                        lastModified = file.lastModified(),
                        isCompressed = file.name.endsWith(".gz"),
                        level = extractLogLevel(file.name)
                    )
                }
                ?.sortedByDescending { it.lastModified }
                ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "获取日志文件列表失败")
            emptyList()
        }
    }
    
    /**
     * 从文件名提取日志级别
     */
    private fun extractLogLevel(fileName: String): LogLevel? {
        return LogLevel.values().find { level ->
            fileName.startsWith(level.name.lowercase())
        }
    }
    
    /**
     * 读取日志文件内容
     */
    suspend fun readLogFile(fileName: String, maxLines: Int = 1000): List<String> = withContext(Dispatchers.IO) {
        try {
            val file = File(logDir, fileName)
            if (!file.exists()) {
                return@withContext emptyList()
            }
            
            if (fileName.endsWith(".gz")) {
                // 读取压缩文件
                readCompressedLogFile(file, maxLines)
            } else {
                // 读取普通文件
                file.readLines().takeLast(maxLines)
            }
        } catch (e: Exception) {
            Timber.e(e, "读取日志文件失败: $fileName")
            emptyList()
        }
    }
    
    /**
     * 读取压缩日志文件
     */
    private fun readCompressedLogFile(file: File, maxLines: Int): List<String> {
        return try {
            val lines = mutableListOf<String>()
            FileInputStream(file).use { fis ->
                java.util.zip.GZIPInputStream(fis).use { gzis ->
                    BufferedReader(InputStreamReader(gzis)).use { reader ->
                        reader.lineSequence().takeLast(maxLines).toList()
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "读取压缩日志文件失败: ${file.name}")
            emptyList()
        }
    }
    
    /**
     * 获取日志目录大小
     */
    suspend fun getLogDirectorySize(): Long = withContext(Dispatchers.IO) {
        try {
            logDir.walkTopDown()
                .filter { it.isFile }
                .map { it.length() }
                .sum()
        } catch (e: Exception) {
            Timber.e(e, "计算日志目录大小失败")
            0L
        }
    }
    
    /**
     * 清理所有日志文件
     */
    suspend fun clearAllLogs() = withContext(Dispatchers.IO) {
        try {
            // 关闭所有写入器
            fileWriters.values.forEach { writer ->
                try {
                    writer.close()
                } catch (e: Exception) {
                    Timber.w(e, "关闭写入器失败")
                }
            }
            fileWriters.clear()
            fileSizes.clear()
            
            // 删除所有日志文件
            logDir.listFiles()?.forEach { file ->
                if (file.delete()) {
                    Timber.d("删除日志文件: ${file.name}")
                } else {
                    Timber.w("删除日志文件失败: ${file.name}")
                }
            }
            
            Timber.i("所有日志文件已清理")
            
        } catch (e: Exception) {
            Timber.e(e, "清理所有日志文件失败")
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        
        fileWriters.values.forEach { writer ->
            try {
                writer.close()
            } catch (e: Exception) {
                Timber.w(e, "关闭日志写入器失败")
            }
        }
        
        fileWriters.clear()
        fileSizes.clear()
        
        Timber.d("日志文件管理器已清理")
    }
}

/**
 * 日志文件信息数据类
 */
data class LogFileInfo(
    val name: String,
    val path: String,
    val size: Long,
    val lastModified: Long,
    val isCompressed: Boolean,
    val level: LogLevel?
)
