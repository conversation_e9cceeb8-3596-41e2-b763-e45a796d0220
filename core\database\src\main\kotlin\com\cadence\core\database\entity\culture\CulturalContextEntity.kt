package com.cadence.core.database.entity.culture

import androidx.room.*
import com.cadence.domain.model.culture.*

/**
 * 文化背景上下文数据库实体
 */
@Entity(
    tableName = "cultural_contexts",
    indices = [
        Index(value = ["word"]),
        Index(value = ["source_language", "target_language"]),
        Index(value = ["region"]),
        Index(value = ["difficulty"]),
        Index(value = ["created_at"])
    ]
)
data class CulturalContextEntity(
    @PrimaryKey
    val id: String,
    val word: String,
    @ColumnInfo(name = "source_language")
    val sourceLanguage: String,
    @ColumnInfo(name = "target_language")
    val targetLanguage: String,
    val region: String,
    @ColumnInfo(name = "cultural_meaning")
    val culturalMeaning: String,
    @ColumnInfo(name = "historical_background")
    val historicalBackground: String,
    @ColumnInfo(name = "related_concepts")
    val relatedConcepts: String, // JSON字符串
    val tags: String, // JSON字符串
    val difficulty: String,
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 使用场景实体
 */
@Entity(
    tableName = "usage_contexts",
    foreignKeys = [
        ForeignKey(
            entity = CulturalContextEntity::class,
            parentColumns = ["id"],
            childColumns = ["context_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["context_id"]),
        Index(value = ["appropriateness"])
    ]
)
data class UsageContextEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "context_id")
    val contextId: String,
    val context: String,
    val description: String,
    val appropriateness: String,
    val examples: String // JSON字符串
)

/**
 * 地域差异实体
 */
@Entity(
    tableName = "regional_differences",
    foreignKeys = [
        ForeignKey(
            entity = CulturalContextEntity::class,
            parentColumns = ["id"],
            childColumns = ["context_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["context_id"]),
        Index(value = ["region"])
    ]
)
data class RegionalDifferenceEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "context_id")
    val contextId: String,
    val region: String,
    val difference: String,
    val explanation: String,
    @ColumnInfo(name = "alternative_expressions")
    val alternativeExpressions: String // JSON字符串
)

/**
 * 文化示例实体
 */
@Entity(
    tableName = "cultural_examples",
    foreignKeys = [
        ForeignKey(
            entity = CulturalContextEntity::class,
            parentColumns = ["id"],
            childColumns = ["context_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["context_id"]),
        Index(value = ["appropriate_usage"])
    ]
)
data class CulturalExampleEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "context_id")
    val contextId: String,
    val scenario: String,
    @ColumnInfo(name = "original_text")
    val originalText: String,
    @ColumnInfo(name = "translated_text")
    val translatedText: String,
    @ColumnInfo(name = "cultural_note")
    val culturalNote: String,
    @ColumnInfo(name = "appropriate_usage")
    val appropriateUsage: Boolean
)

/**
 * 文化推荐实体
 */
@Entity(
    tableName = "cultural_recommendations",
    indices = [
        Index(value = ["type"]),
        Index(value = ["difficulty"]),
        Index(value = ["region"]),
        Index(value = ["language"]),
        Index(value = ["popularity"]),
        Index(value = ["created_at"])
    ]
)
data class CulturalRecommendationEntity(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val type: String,
    @ColumnInfo(name = "related_words")
    val relatedWords: String, // JSON字符串
    val content: String,
    val difficulty: String,
    val region: String,
    val language: String,
    val tags: String, // JSON字符串
    val popularity: Int,
    @ColumnInfo(name = "created_at")
    val createdAt: Long
)

/**
 * 文化学习进度实体
 */
@Entity(
    tableName = "cultural_learning_progress",
    foreignKeys = [
        ForeignKey(
            entity = CulturalContextEntity::class,
            parentColumns = ["id"],
            childColumns = ["context_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["context_id"]),
        Index(value = ["is_learned"]),
        Index(value = ["bookmarked"]),
        Index(value = ["last_accessed_at"])
    ]
)
data class CulturalLearningProgressEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo(name = "user_id")
    val userId: String,
    @ColumnInfo(name = "context_id")
    val contextId: String,
    @ColumnInfo(name = "is_learned")
    val isLearned: Boolean,
    val difficulty: String,
    @ColumnInfo(name = "last_accessed_at")
    val lastAccessedAt: Long,
    @ColumnInfo(name = "access_count")
    val accessCount: Int,
    val bookmarked: Boolean,
    val notes: String
)

/**
 * 完整的文化背景信息（包含关联数据）
 */
data class CulturalContextWithDetails(
    @Embedded
    val context: CulturalContextEntity,
    
    @Relation(
        parentColumn = "id",
        entityColumn = "context_id"
    )
    val usageContexts: List<UsageContextEntity>,
    
    @Relation(
        parentColumn = "id",
        entityColumn = "context_id"
    )
    val regionalDifferences: List<RegionalDifferenceEntity>,
    
    @Relation(
        parentColumn = "id",
        entityColumn = "context_id"
    )
    val examples: List<CulturalExampleEntity>,
    
    @Relation(
        parentColumn = "id",
        entityColumn = "context_id"
    )
    val learningProgress: List<CulturalLearningProgressEntity>
)