package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 同步状态枚举
 */
enum class SyncStatus {
    IDLE,           // 空闲状态
    SYNCING,        // 同步中
    SUCCESS,        // 同步成功
    FAILED,         // 同步失败
    CONFLICT        // 同步冲突
}

/**
 * 同步类型枚举
 */
enum class SyncType {
    TRANSLATION_HISTORY,    // 翻译历史
    FAVORITES,             // 收藏内容
    TAGS,                  // 标签
    USER_PREFERENCES,      // 用户偏好
    ALL                    // 全部数据
}

/**
 * 同步配置
 */
@Serializable
data class SyncConfig(
    val isEnabled: Boolean = false,
    val autoSyncEnabled: Boolean = true,
    val syncInterval: Long = 3600000L, // 1小时，毫秒
    val wifiOnlySync: Boolean = true,
    val lastSyncTime: Long = 0L,
    val userId: String? = null,
    val deviceId: String
)

/**
 * 同步结果
 */
@Serializable
data class SyncResult(
    val syncType: SyncType,
    val status: SyncStatus,
    val startTime: Long,
    val endTime: Long,
    val uploadedCount: Int = 0,
    val downloadedCount: Int = 0,
    val conflictCount: Int = 0,
    val errorMessage: String? = null,
    val details: List<SyncItemResult> = emptyList()
)

/**
 * 同步项目结果
 */
@Serializable
data class SyncItemResult(
    val itemId: String,
    val itemType: String,
    val action: SyncAction,
    val status: SyncStatus,
    val errorMessage: String? = null
)

/**
 * 同步操作类型
 */
enum class SyncAction {
    UPLOAD,     // 上传到云端
    DOWNLOAD,   // 从云端下载
    UPDATE,     // 更新
    DELETE,     // 删除
    CONFLICT    // 冲突处理
}

/**
 * 同步数据包装器
 */
@Serializable
data class SyncData<T>(
    val id: String,
    val data: T,
    val lastModified: Long,
    val deviceId: String,
    val version: Int = 1,
    val checksum: String? = null
)

/**
 * 翻译历史同步数据
 */
@Serializable
data class TranslationSyncData(
    val translations: List<Translation>,
    val tags: List<Tag>,
    val translationTags: List<TranslationTagRelation>,
    val lastSyncTime: Long
)

/**
 * 翻译标签关联关系
 */
@Serializable
data class TranslationTagRelation(
    val translationId: String,
    val tagId: String,
    val createdAt: Long
)

/**
 * 同步冲突
 */
@Serializable
data class SyncConflict<T>(
    val itemId: String,
    val localData: SyncData<T>,
    val remoteData: SyncData<T>,
    val conflictType: ConflictType
)

/**
 * 冲突类型
 */
enum class ConflictType {
    BOTH_MODIFIED,      // 本地和远程都有修改
    LOCAL_DELETED,      // 本地删除，远程修改
    REMOTE_DELETED,     // 远程删除，本地修改
    VERSION_MISMATCH    // 版本不匹配
}

/**
 * 冲突解决策略
 */
enum class ConflictResolution {
    USE_LOCAL,          // 使用本地数据
    USE_REMOTE,         // 使用远程数据
    MERGE,              // 合并数据
    SKIP,               // 跳过冲突项
    ASK_USER            // 询问用户
}

/**
 * 同步统计信息
 */
@Serializable
data class SyncStatistics(
    val totalSyncs: Int,
    val successfulSyncs: Int,
    val failedSyncs: Int,
    val lastSyncTime: Long,
    val totalDataSynced: Long, // 字节数
    val averageSyncDuration: Long, // 毫秒
    val syncsByType: Map<SyncType, Int>
)

/**
 * 设备信息
 */
@Serializable
data class DeviceInfo(
    val deviceId: String,
    val deviceName: String,
    val platform: String = "Android",
    val appVersion: String,
    val lastActiveTime: Long,
    val isCurrentDevice: Boolean = false
)

/**
 * 云端同步响应
 */
@Serializable
data class CloudSyncResponse<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val timestamp: Long,
    val conflicts: List<SyncConflict<*>> = emptyList()
)
