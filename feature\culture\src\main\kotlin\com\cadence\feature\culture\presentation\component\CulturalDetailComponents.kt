package com.cadence.feature.culture.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.culture.*

/**
 * 文化基本信息卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalBasicInfoCard(
    context: CulturalContext,
    isLearned: Boolean,
    onMarkAsLearned: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = context.word,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                if (isLearned) {
                    Surface(
                        shape = RoundedCornerShape(12.dp),
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.CheckCircle,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = "已学习",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 语言和地区信息
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                InfoItem(
                    icon = Icons.Default.Language,
                    label = "语言",
                    value = "${context.sourceLanguage} → ${context.targetLanguage}"
                )
                
                InfoItem(
                    icon = Icons.Default.LocationOn,
                    label = "地区",
                    value = context.region
                )
                
                InfoItem(
                    icon = Icons.Default.TrendingUp,
                    label = "难度",
                    value = getDifficultyText(context.difficulty)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 操作按钮
            if (!isLearned) {
                Button(
                    onClick = onMarkAsLearned,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("标记为已学习")
                }
            }
        }
    }
}

/**
 * 文化含义卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalMeaningCard(
    culturalMeaning: String,
    historicalBackground: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Psychology,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "文化含义",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 文化含义
            Text(
                text = culturalMeaning,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            if (historicalBackground.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // 历史背景标题
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.History,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.secondary,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "历史背景",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // 历史背景内容
                Text(
                    text = historicalBackground,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 使用场景卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UsageContextCard(
    usageContexts: List<UsageContext>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lightbulb,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "使用场景",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 使用场景列表
            usageContexts.forEach { usage ->
                UsageContextItem(
                    usageContext = usage,
                    modifier = Modifier.fillMaxWidth()
                )
                if (usage != usageContexts.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * 地域差异卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RegionalDifferencesCard(
    regionalDifferences: List<RegionalDifference>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Public,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "地域差异",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 地域差异列表
            regionalDifferences.forEach { difference ->
                RegionalDifferenceItem(
                    regionalDifference = difference,
                    modifier = Modifier.fillMaxWidth()
                )
                if (difference != regionalDifferences.last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * 文化示例卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalExamplesCard(
    examples: List<CulturalExample>,
    onViewAllExamples: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.FormatQuote,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "文化示例",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                if (examples.size > 2) {
                    TextButton(onClick = onViewAllExamples) {
                        Text("查看全部")
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 示例列表（最多显示2个）
            examples.take(2).forEach { example ->
                CulturalExampleItem(
                    example = example,
                    modifier = Modifier.fillMaxWidth()
                )
                if (example != examples.take(2).last()) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
            }
        }
    }
}

/**
 * 学习笔记卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LearningNotesCard(
    notes: String,
    onNotesChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    var isEditing by remember { mutableStateOf(false) }
    var editingNotes by remember { mutableStateOf(notes) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Note,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "学习笔记",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                IconButton(
                    onClick = { 
                        if (isEditing) {
                            onNotesChange(editingNotes)
                        }
                        isEditing = !isEditing 
                    }
                ) {
                    Icon(
                        imageVector = if (isEditing) Icons.Default.Save else Icons.Default.Edit,
                        contentDescription = if (isEditing) "保存" else "编辑",
                        modifier = Modifier.size(18.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 笔记内容
            if (isEditing) {
                OutlinedTextField(
                    value = editingNotes,
                    onValueChange = { editingNotes = it },
                    placeholder = { Text("添加你的学习笔记...") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3
                )
            } else {
                if (notes.isNotEmpty()) {
                    Text(
                        text = notes,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                } else {
                    Text(
                        text = "点击编辑按钮添加学习笔记",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

/**
 * 相关推荐卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RelatedRecommendationsCard(
    contextId: String,
    onViewRelated: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Recommend,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "相关推荐",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 推荐按钮
            OutlinedButton(
                onClick = onViewRelated,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Explore,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("查看相关文化知识")
            }
        }
    }
}

// 辅助组件

/**
 * 信息项组件
 */
@Composable
private fun InfoItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(20.dp)
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 使用场景项组件
 */
@Composable
private fun UsageContextItem(
    usageContext: UsageContext,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 场景标题
        Text(
            text = usageContext.context,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        Spacer(modifier = Modifier.height(4.dp))
        
        // 场景描述
        Text(
            text = usageContext.description,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        // 适用性标签
        Spacer(modifier = Modifier.height(8.dp))
        Surface(
            shape = RoundedCornerShape(8.dp),
            color = getAppropriatenessColor(usageContext.appropriateness).copy(alpha = 0.1f)
        ) {
            Text(
                text = getAppropriatenessText(usageContext.appropriateness),
                style = MaterialTheme.typography.labelSmall,
                color = getAppropriatenessColor(usageContext.appropriateness),
                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
            )
        }
    }
}

/**
 * 地域差异项组件
 */
@Composable
private fun RegionalDifferenceItem(
    regionalDifference: RegionalDifference,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 地区标题
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Place,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = regionalDifference.region,
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        // 差异说明
        Text(
            text = regionalDifference.difference,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        // 解释
        if (regionalDifference.explanation.isNotEmpty()) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = regionalDifference.explanation,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 文化示例项组件
 */
@Composable
private fun CulturalExampleItem(
    example: CulturalExample,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 场景
        Text(
            text = example.scenario,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 原文和译文
        Surface(
            shape = RoundedCornerShape(8.dp),
            color = MaterialTheme.colorScheme.surfaceVariant
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = example.originalText,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = example.translatedText,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
        
        // 文化注释
        if (example.culturalNote.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "💡 ${example.culturalNote}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

// 辅助函数

private fun getDifficultyText(difficulty: CulturalDifficulty): String {
    return when (difficulty) {
        CulturalDifficulty.BEGINNER -> "初级"
        CulturalDifficulty.INTERMEDIATE -> "中级"
        CulturalDifficulty.ADVANCED -> "高级"
        CulturalDifficulty.EXPERT -> "专家"
    }
}

@Composable
private fun getAppropriatenessColor(appropriateness: AppropriatenessLevel): androidx.compose.ui.graphics.Color {
    return when (appropriateness) {
        AppropriatenessLevel.HIGHLY_APPROPRIATE -> MaterialTheme.colorScheme.primary
        AppropriatenessLevel.APPROPRIATE -> MaterialTheme.colorScheme.secondary
        AppropriatenessLevel.NEUTRAL -> MaterialTheme.colorScheme.outline
        AppropriatenessLevel.INAPPROPRIATE -> MaterialTheme.colorScheme.error
        AppropriatenessLevel.HIGHLY_INAPPROPRIATE -> MaterialTheme.colorScheme.error
    }
}

private fun getAppropriatenessText(appropriateness: AppropriatenessLevel): String {
    return when (appropriateness) {
        AppropriatenessLevel.HIGHLY_APPROPRIATE -> "非常合适"
        AppropriatenessLevel.APPROPRIATE -> "合适"
        AppropriatenessLevel.NEUTRAL -> "中性"
        AppropriatenessLevel.INAPPROPRIATE -> "不合适"
        AppropriatenessLevel.HIGHLY_INAPPROPRIATE -> "非常不合适"
    }
}