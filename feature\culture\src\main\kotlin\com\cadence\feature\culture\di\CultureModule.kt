package com.cadence.feature.culture.di

import com.cadence.core.database.dao.culture.CulturalContextDao
import com.cadence.domain.repository.culture.CulturalContextRepository
import com.cadence.feature.culture.data.repository.CulturalContextRepositoryImpl
import com.cadence.feature.culture.data.repository.RegionalDifferenceService
import com.cadence.feature.culture.data.repository.CulturalRecommendationService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 文化功能模块的依赖注入配置
 * 提供Repository实现和其他依赖项
 */
@Module
@InstallIn(SingletonComponent::class)
object CultureModule {

    /**
     * 提供文化背景Repository实现
     */
    @Provides
    @Singleton
    fun provideCulturalContextRepository(
        culturalContextDao: CulturalContextDao
    ): CulturalContextRepository {
        return CulturalContextRepositoryImpl(culturalContextDao)
    }

    /**
     * 提供地域文化差异服务
     */
    @Provides
    @Singleton
    fun provideRegionalDifferenceService(
        culturalContextDao: CulturalContextDao
    ): RegionalDifferenceService {
        return RegionalDifferenceService(culturalContextDao)
    }

    /**
     * 提供文化知识推荐服务
     */
    @Provides
    @Singleton
    fun provideCulturalRecommendationService(
        culturalContextDao: CulturalContextDao
    ): CulturalRecommendationService {
        return CulturalRecommendationService(culturalContextDao)
    }
}