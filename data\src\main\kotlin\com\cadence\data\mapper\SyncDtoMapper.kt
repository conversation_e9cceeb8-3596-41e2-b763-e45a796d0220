package com.cadence.data.mapper

import com.cadence.core.network.dto.*
import com.cadence.domain.model.*

/**
 * 同步数据传输对象映射器
 * 负责领域模型与网络DTO之间的转换
 */
object SyncDtoMapper {
    
    // ========== Translation 映射 ==========
    
    /**
     * 将Translation领域模型转换为TranslationDto
     */
    fun Translation.toDto(deviceId: String): TranslationDto {
        return TranslationDto(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = sourceLanguage.code,
            targetLanguage = targetLanguage.code,
            sourceRegion = sourceLanguage.region,
            targetRegion = targetLanguage.region,
            translationType = translationType.name,
            confidence = confidence,
            isFavorite = isFavorite,
            timestamp = timestamp,
            lastModified = timestamp, // 使用timestamp作为lastModified
            deviceId = deviceId,
            version = 1
        )
    }
    
    /**
     * 将TranslationDto转换为Translation领域模型
     */
    fun TranslationDto.toDomain(): Translation {
        return Translation(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = Language(
                code = sourceLanguage,
                name = sourceLanguage, // 简化处理，实际应该从语言映射表获取
                region = sourceRegion
            ),
            targetLanguage = Language(
                code = targetLanguage,
                name = targetLanguage, // 简化处理，实际应该从语言映射表获取
                region = targetRegion
            ),
            translationType = TranslationType.valueOf(translationType),
            confidence = confidence,
            isFavorite = isFavorite,
            timestamp = timestamp
        )
    }
    
    // ========== Tag 映射 ==========
    
    /**
     * 将Tag领域模型转换为TagDto
     */
    fun Tag.toDto(deviceId: String): TagDto {
        return TagDto(
            id = id,
            name = name,
            color = color,
            description = description,
            usageCount = usageCount,
            createdAt = createdAt,
            updatedAt = updatedAt,
            lastModified = updatedAt,
            deviceId = deviceId,
            version = 1
        )
    }
    
    /**
     * 将TagDto转换为Tag领域模型
     */
    fun TagDto.toDomain(): Tag {
        return Tag(
            id = id,
            name = name,
            color = color,
            description = description,
            usageCount = usageCount,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    // ========== TranslationTag 映射 ==========
    
    /**
     * 将TranslationTag领域模型转换为TranslationTagDto
     */
    fun TranslationTag.toDto(deviceId: String): TranslationTagDto {
        return TranslationTagDto(
            translationId = translationId,
            tagId = tagId,
            createdAt = createdAt,
            lastModified = createdAt,
            deviceId = deviceId
        )
    }
    
    /**
     * 将TranslationTagDto转换为TranslationTag领域模型
     */
    fun TranslationTagDto.toDomain(): TranslationTag {
        return TranslationTag(
            translationId = translationId,
            tagId = tagId,
            createdAt = createdAt
        )
    }
    
    // ========== UserPreference 映射 ==========
    
    /**
     * 将UserPreference领域模型转换为UserPreferencesDto
     */
    fun UserPreference.toDto(deviceId: String): UserPreferencesDto {
        return UserPreferencesDto(
            userId = userId,
            language = language.toDto(),
            theme = theme.name,
            translationSettings = translationSettings.toDto(),
            privacySettings = privacySettings.toDto(),
            notificationSettings = notificationSettings.toDto(),
            lastModified = System.currentTimeMillis(),
            deviceId = deviceId,
            version = 1
        )
    }
    
    /**
     * 将UserPreferencesDto转换为UserPreference领域模型
     */
    fun UserPreferencesDto.toDomain(): UserPreference {
        return UserPreference(
            userId = userId,
            language = language.toDomain(),
            theme = AppTheme.valueOf(theme),
            translationSettings = translationSettings.toDomain(),
            privacySettings = privacySettings.toDomain(),
            notificationSettings = notificationSettings.toDomain()
        )
    }
    
    // ========== Language 映射 ==========
    
    /**
     * 将Language领域模型转换为LanguageDto
     */
    fun Language.toDto(): LanguageDto {
        return LanguageDto(
            code = code,
            name = name,
            region = region
        )
    }
    
    /**
     * 将LanguageDto转换为Language领域模型
     */
    fun LanguageDto.toDomain(): Language {
        return Language(
            code = code,
            name = name,
            region = region
        )
    }
    
    // ========== TranslationSettings 映射 ==========
    
    /**
     * 将TranslationSettings领域模型转换为TranslationSettingsDto
     */
    fun TranslationSettings.toDto(): TranslationSettingsDto {
        return TranslationSettingsDto(
            autoDetectLanguage = autoDetectLanguage,
            saveHistory = saveHistory,
            enableOfflineMode = enableOfflineMode,
            preferredTranslationEngine = preferredTranslationEngine,
            maxHistoryItems = maxHistoryItems,
            enableRegionalTranslation = enableRegionalTranslation,
            showConfidenceScore = showConfidenceScore,
            enableVoiceInput = enableVoiceInput,
            enableImageTranslation = enableImageTranslation
        )
    }
    
    /**
     * 将TranslationSettingsDto转换为TranslationSettings领域模型
     */
    fun TranslationSettingsDto.toDomain(): TranslationSettings {
        return TranslationSettings(
            autoDetectLanguage = autoDetectLanguage,
            saveHistory = saveHistory,
            enableOfflineMode = enableOfflineMode,
            preferredTranslationEngine = preferredTranslationEngine,
            maxHistoryItems = maxHistoryItems,
            enableRegionalTranslation = enableRegionalTranslation,
            showConfidenceScore = showConfidenceScore,
            enableVoiceInput = enableVoiceInput,
            enableImageTranslation = enableImageTranslation
        )
    }
    
    // ========== PrivacySettings 映射 ==========
    
    /**
     * 将PrivacySettings领域模型转换为PrivacySettingsDto
     */
    fun PrivacySettings.toDto(): PrivacySettingsDto {
        return PrivacySettingsDto(
            enableDataCollection = enableDataCollection,
            enableCrashReporting = enableCrashReporting,
            enableAnalytics = enableAnalytics,
            autoDeleteHistoryDays = autoDeleteHistoryDays
        )
    }
    
    /**
     * 将PrivacySettingsDto转换为PrivacySettings领域模型
     */
    fun PrivacySettingsDto.toDomain(): PrivacySettings {
        return PrivacySettings(
            enableDataCollection = enableDataCollection,
            enableCrashReporting = enableCrashReporting,
            enableAnalytics = enableAnalytics,
            autoDeleteHistoryDays = autoDeleteHistoryDays
        )
    }
    
    // ========== NotificationSettings 映射 ==========
    
    /**
     * 将NotificationSettings领域模型转换为NotificationSettingsDto
     */
    fun NotificationSettings.toDto(): NotificationSettingsDto {
        return NotificationSettingsDto(
            enableTranslationComplete = enableTranslationComplete,
            enableDailyReminder = enableDailyReminder,
            enableWeeklyStats = enableWeeklyStats,
            reminderTime = reminderTime
        )
    }
    
    /**
     * 将NotificationSettingsDto转换为NotificationSettings领域模型
     */
    fun NotificationSettingsDto.toDomain(): NotificationSettings {
        return NotificationSettings(
            enableTranslationComplete = enableTranslationComplete,
            enableDailyReminder = enableDailyReminder,
            enableWeeklyStats = enableWeeklyStats,
            reminderTime = reminderTime
        )
    }
    
    // ========== DeviceInfo 映射 ==========
    
    /**
     * 将DeviceInfo领域模型转换为DeviceInfoDto
     */
    fun DeviceInfo.toDto(): DeviceInfoDto {
        return DeviceInfoDto(
            deviceId = deviceId,
            deviceName = deviceName,
            platform = platform,
            appVersion = appVersion,
            lastActiveTime = lastActiveTime,
            isCurrentDevice = isCurrentDevice
        )
    }
    
    /**
     * 将DeviceInfoDto转换为DeviceInfo领域模型
     */
    fun DeviceInfoDto.toDomain(): DeviceInfo {
        return DeviceInfo(
            deviceId = deviceId,
            deviceName = deviceName,
            platform = platform,
            appVersion = appVersion,
            lastActiveTime = lastActiveTime,
            isCurrentDevice = isCurrentDevice
        )
    }
    
    // ========== 批量转换方法 ==========
    
    /**
     * 批量转换Translation列表为DTO
     */
    fun List<Translation>.toDtoList(deviceId: String): List<TranslationDto> {
        return map { it.toDto(deviceId) }
    }
    
    /**
     * 批量转换TranslationDto列表为领域模型
     */
    fun List<TranslationDto>.toDomainList(): List<Translation> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换Tag列表为DTO
     */
    fun List<Tag>.toDtoList(deviceId: String): List<TagDto> {
        return map { it.toDto(deviceId) }
    }
    
    /**
     * 批量转换TagDto列表为领域模型
     */
    fun List<TagDto>.toDomainList(): List<Tag> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换TranslationTag列表为DTO
     */
    fun List<TranslationTag>.toDtoList(deviceId: String): List<TranslationTagDto> {
        return map { it.toDto(deviceId) }
    }
    
    /**
     * 批量转换TranslationTagDto列表为领域模型
     */
    fun List<TranslationTagDto>.toDomainList(): List<TranslationTag> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换DeviceInfo列表为DTO
     */
    fun List<DeviceInfo>.toDtoList(): List<DeviceInfoDto> {
        return map { it.toDto() }
    }
    
    /**
     * 批量转换DeviceInfoDto列表为领域模型
     */
    fun List<DeviceInfoDto>.toDomainList(): List<DeviceInfo> {
        return map { it.toDomain() }
    }
}
