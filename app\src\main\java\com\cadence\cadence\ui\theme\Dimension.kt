package com.cadence.cadence.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Cadence应用程序尺寸系统
 * 
 * 基于Material Design 3尺寸规范，
 * 支持响应式布局和多屏幕适配
 */

/**
 * 屏幕尺寸类型
 */
enum class ScreenSize {
    COMPACT,    // 紧凑型 (< 600dp)
    MEDIUM,     // 中等型 (600dp - 840dp)
    EXPANDED    // 扩展型 (> 840dp)
}

/**
 * 屏幕方向
 */
enum class ScreenOrientation {
    PORTRAIT,   // 竖屏
    LANDSCAPE   // 横屏
}

/**
 * 响应式尺寸配置
 */
data class ResponsiveDimensions(
    val screenSize: ScreenSize,
    val orientation: ScreenOrientation,
    
    // 间距尺寸
    val spacingXSmall: Dp,
    val spacingSmall: Dp,
    val spacingMedium: Dp,
    val spacingLarge: Dp,
    val spacingXLarge: Dp,
    
    // 内容区域尺寸
    val contentPadding: Dp,
    val cardPadding: Dp,
    val listItemPadding: Dp,
    
    // 组件尺寸
    val buttonHeight: Dp,
    val inputFieldHeight: Dp,
    val iconSize: Dp,
    val avatarSize: Dp,
    
    // 布局尺寸
    val maxContentWidth: Dp,
    val sidebarWidth: Dp,
    val bottomNavHeight: Dp,
    val topAppBarHeight: Dp
)

/**
 * 获取当前屏幕的响应式尺寸配置
 */
@Composable
fun getResponsiveDimensions(): ResponsiveDimensions {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    val screenHeight = configuration.screenHeightDp.dp
    
    val screenSize = when {
        screenWidth < 600.dp -> ScreenSize.COMPACT
        screenWidth < 840.dp -> ScreenSize.MEDIUM
        else -> ScreenSize.EXPANDED
    }
    
    val orientation = if (screenWidth > screenHeight) {
        ScreenOrientation.LANDSCAPE
    } else {
        ScreenOrientation.PORTRAIT
    }
    
    return when (screenSize) {
        ScreenSize.COMPACT -> ResponsiveDimensions(
            screenSize = screenSize,
            orientation = orientation,
            
            spacingXSmall = 4.dp,
            spacingSmall = 8.dp,
            spacingMedium = 16.dp,
            spacingLarge = 24.dp,
            spacingXLarge = 32.dp,
            
            contentPadding = 16.dp,
            cardPadding = 16.dp,
            listItemPadding = 16.dp,
            
            buttonHeight = 48.dp,
            inputFieldHeight = 56.dp,
            iconSize = 24.dp,
            avatarSize = 40.dp,
            
            maxContentWidth = screenWidth,
            sidebarWidth = 0.dp, // 紧凑型不显示侧边栏
            bottomNavHeight = 80.dp,
            topAppBarHeight = 64.dp
        )
        
        ScreenSize.MEDIUM -> ResponsiveDimensions(
            screenSize = screenSize,
            orientation = orientation,
            
            spacingXSmall = 6.dp,
            spacingSmall = 12.dp,
            spacingMedium = 20.dp,
            spacingLarge = 28.dp,
            spacingXLarge = 36.dp,
            
            contentPadding = 20.dp,
            cardPadding = 20.dp,
            listItemPadding = 20.dp,
            
            buttonHeight = 52.dp,
            inputFieldHeight = 60.dp,
            iconSize = 28.dp,
            avatarSize = 48.dp,
            
            maxContentWidth = 720.dp,
            sidebarWidth = if (orientation == ScreenOrientation.LANDSCAPE) 280.dp else 0.dp,
            bottomNavHeight = 88.dp,
            topAppBarHeight = 72.dp
        )
        
        ScreenSize.EXPANDED -> ResponsiveDimensions(
            screenSize = screenSize,
            orientation = orientation,
            
            spacingXSmall = 8.dp,
            spacingSmall = 16.dp,
            spacingMedium = 24.dp,
            spacingLarge = 32.dp,
            spacingXLarge = 40.dp,
            
            contentPadding = 24.dp,
            cardPadding = 24.dp,
            listItemPadding = 24.dp,
            
            buttonHeight = 56.dp,
            inputFieldHeight = 64.dp,
            iconSize = 32.dp,
            avatarSize = 56.dp,
            
            maxContentWidth = 840.dp,
            sidebarWidth = 320.dp,
            bottomNavHeight = 96.dp,
            topAppBarHeight = 80.dp
        )
    }
}

/**
 * 固定尺寸定义（不随屏幕尺寸变化）
 */
object CadenceDimensions {
    
    // 基础间距
    val spacing0 = 0.dp
    val spacing2 = 2.dp
    val spacing4 = 4.dp
    val spacing8 = 8.dp
    val spacing12 = 12.dp
    val spacing16 = 16.dp
    val spacing20 = 20.dp
    val spacing24 = 24.dp
    val spacing32 = 32.dp
    val spacing40 = 40.dp
    val spacing48 = 48.dp
    val spacing56 = 56.dp
    val spacing64 = 64.dp
    
    // 圆角半径
    val cornerRadiusSmall = 8.dp
    val cornerRadiusMedium = 12.dp
    val cornerRadiusLarge = 16.dp
    val cornerRadiusXLarge = 20.dp
    
    // 边框宽度
    val borderWidthThin = 1.dp
    val borderWidthMedium = 2.dp
    val borderWidthThick = 4.dp
    
    // 阴影高度
    val elevationSmall = 2.dp
    val elevationMedium = 4.dp
    val elevationLarge = 8.dp
    val elevationXLarge = 12.dp
    
    // 最小触摸目标尺寸
    val minTouchTarget = 48.dp
    
    // 分割线尺寸
    val dividerThickness = 1.dp
    
    // 进度条尺寸
    val progressBarHeight = 4.dp
    val progressBarHeightLarge = 8.dp
}

/**
 * 屏幕断点定义
 */
object ScreenBreakpoints {
    val compact = 600.dp
    val medium = 840.dp
    val expanded = 1200.dp
}
