package com.cadence.domain.model

import kotlinx.serialization.Serializable

/**
 * 收藏夹领域模型
 * 用于组织和管理收藏的翻译记录
 */
@Serializable
data class FavoriteFolder(
    val id: String,
    val name: String,
    val description: String? = null,
    val color: String = "#2196F3", // 默认蓝色
    val icon: String? = null,
    val isDefault: Boolean = false, // 是否为默认收藏夹
    val sortOrder: Int = 0,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 收藏项领域模型
 * 表示收藏夹中的具体翻译记录
 */
@Serializable
data class FavoriteItem(
    val id: String,
    val translationId: String,
    val folderId: String,
    val note: String? = null, // 用户备注
    val tags: List<String> = emptyList(), // 标签列表
    val priority: FavoritePriority = FavoritePriority.NORMAL,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 收藏优先级枚举
 */
@Serializable
enum class FavoritePriority {
    LOW,      // 低优先级
    NORMAL,   // 普通优先级
    HIGH,     // 高优先级
    URGENT    // 紧急优先级
}

/**
 * 收藏夹统计信息
 */
@Serializable
data class FavoriteStatistics(
    val totalFolders: Int,
    val totalItems: Int,
    val itemsByFolder: Map<String, Int>, // 每个收藏夹的项目数量
    val itemsByPriority: Map<FavoritePriority, Int>, // 按优先级分组的项目数量
    val itemsByTag: Map<String, Int>, // 按标签分组的项目数量
    val recentlyAddedCount: Int, // 最近添加的项目数量（7天内）
    val lastUpdated: Long
)

/**
 * 收藏夹查询条件
 */
@Serializable
data class FavoriteQuery(
    val folderId: String? = null,
    val searchQuery: String? = null,
    val tags: List<String> = emptyList(),
    val priority: FavoritePriority? = null,
    val languagePair: Pair<Language, Language>? = null,
    val translationType: TranslationType? = null,
    val sortBy: FavoriteSortBy = FavoriteSortBy.CREATED_AT,
    val sortOrder: SortOrder = SortOrder.DESC,
    val limit: Int = 50,
    val offset: Int = 0
)

/**
 * 收藏排序方式
 */
@Serializable
enum class FavoriteSortBy {
    CREATED_AT,     // 按创建时间排序
    UPDATED_AT,     // 按更新时间排序
    PRIORITY,       // 按优先级排序
    NAME,           // 按名称排序
    USAGE_COUNT     // 按使用次数排序
}

/**
 * 排序顺序
 */
@Serializable
enum class SortOrder {
    ASC,  // 升序
    DESC  // 降序
}

/**
 * 收藏夹导出数据
 */
@Serializable
data class FavoriteExportData(
    val folders: List<FavoriteFolder>,
    val items: List<FavoriteItem>,
    val translations: List<Translation>, // 关联的翻译记录
    val exportTime: Long,
    val appVersion: String
)

/**
 * 收藏夹导入结果
 */
@Serializable
data class FavoriteImportResult(
    val importedFolders: Int,
    val importedItems: Int,
    val skippedItems: Int, // 跳过的重复项
    val errors: List<String> = emptyList(),
    val importTime: Long
)

/**
 * 收藏夹操作类型
 */
@Serializable
enum class FavoriteOperationType {
    CREATE_FOLDER,    // 创建收藏夹
    UPDATE_FOLDER,    // 更新收藏夹
    DELETE_FOLDER,    // 删除收藏夹
    ADD_ITEM,         // 添加收藏项
    REMOVE_ITEM,      // 移除收藏项
    MOVE_ITEM,        // 移动收藏项
    UPDATE_ITEM,      // 更新收藏项
    EXPORT_DATA,      // 导出数据
    IMPORT_DATA       // 导入数据
}

/**
 * 收藏夹操作记录
 */
@Serializable
data class FavoriteOperation(
    val id: String,
    val operationType: FavoriteOperationType,
    val targetId: String, // 操作目标的ID（收藏夹ID或收藏项ID）
    val details: String? = null, // 操作详情
    val timestamp: Long
)

/**
 * 收藏夹同步状态
 */
@Serializable
enum class FavoriteSyncStatus {
    SYNCED,      // 已同步
    PENDING,     // 待同步
    SYNCING,     // 同步中
    FAILED,      // 同步失败
    CONFLICT     // 同步冲突
}

/**
 * 收藏夹同步信息
 */
@Serializable
data class FavoriteSyncInfo(
    val itemId: String,
    val itemType: String, // "folder" 或 "item"
    val status: FavoriteSyncStatus,
    val lastSyncTime: Long? = null,
    val syncError: String? = null,
    val version: Int = 1
)

/**
 * 收藏夹配置
 */
@Serializable
data class FavoriteConfig(
    val autoSync: Boolean = true,
    val defaultFolderId: String? = null,
    val maxItemsPerFolder: Int = 1000,
    val enableNotifications: Boolean = true,
    val showItemCount: Boolean = true,
    val compactView: Boolean = false,
    val enableTags: Boolean = true,
    val enablePriority: Boolean = true
)

/**
 * 收藏夹视图模式
 */
@Serializable
enum class FavoriteViewMode {
    LIST,     // 列表视图
    GRID,     // 网格视图
    COMPACT   // 紧凑视图
}

/**
 * 收藏夹主题
 */
@Serializable
data class FavoriteTheme(
    val primaryColor: String,
    val secondaryColor: String,
    val backgroundColor: String,
    val textColor: String,
    val iconStyle: String = "outlined"
)

/**
 * 收藏夹快捷操作
 */
@Serializable
enum class FavoriteQuickAction {
    ADD_TO_DEFAULT,    // 添加到默认收藏夹
    ADD_TO_RECENT,     // 添加到最近使用的收藏夹
    CREATE_NEW_FOLDER, // 创建新收藏夹并添加
    SHARE,             // 分享
    EXPORT,            // 导出
    DUPLICATE          // 复制
}

/**
 * 收藏夹搜索建议
 */
@Serializable
data class FavoriteSearchSuggestion(
    val type: String, // "folder", "tag", "translation"
    val value: String,
    val count: Int,
    val lastUsed: Long
)

/**
 * 收藏夹使用统计
 */
@Serializable
data class FavoriteUsageStats(
    val folderId: String,
    val accessCount: Int,
    val lastAccessTime: Long,
    val averageSessionDuration: Long,
    val mostUsedTags: List<String>,
    val peakUsageHour: Int // 0-23
)
