package com.cadence.feature.learning.di

import com.cadence.core.database.dao.learning.*
import com.cadence.core.data.repository.LearningRepositoryImpl
import com.cadence.domain.repository.LearningRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 学习功能模块的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object LearningModule {
    
    @Provides
    @Singleton
    fun provideLearningRepository(
        wordDao: WordDao,
        learningProgressDao: LearningProgressDao,
        studySessionDao: StudySessionDao
    ): LearningRepository {
        return LearningRepositoryImpl(
            wordDao = wordDao,
            learningProgressDao = learningProgressDao,
            studySessionDao = studySessionDao
        )
    }
}