package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 错误报告器
 * 负责收集、分析和上报错误信息
 */
@Singleton
class ErrorReporter @Inject constructor(
    private val context: Context,
    private val errorAnalyzer: ErrorAnalyzer,
    private val structuredLogger: StructuredLogger
) {
    
    companion object {
        private const val ERROR_REPORTS_DIR = "error_reports"
        private const val MAX_PENDING_REPORTS = 100
        private const val REPORT_BATCH_SIZE = 10
        private const val UPLOAD_RETRY_DELAY_MS = 30000L // 30 seconds
        private const val MAX_UPLOAD_RETRIES = 3
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val json = Json { 
        prettyPrint = false
        ignoreUnknownKeys = true
    }
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
    
    // 错误报告目录
    private val errorReportsDir: File by lazy {
        File(context.filesDir, ERROR_REPORTS_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    // 报告状态管理
    private val _reportingState = MutableStateFlow(ErrorReportingState())
    val reportingState: StateFlow<ErrorReportingState> = _reportingState.asStateFlow()
    
    // 待上传报告队列
    private val pendingReports = mutableListOf<ErrorReport>()
    private val uploadLock = Any()
    
    init {
        // 启动报告处理器
        startReportProcessor()
        
        // 加载待上传的报告
        loadPendingReports()
        
        Timber.d("错误报告器已初始化")
    }
    
    /**
     * 报告错误
     */
    suspend fun reportError(
        exception: Throwable,
        context: String = "unknown",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        additionalData: Map<String, Any> = emptyMap(),
        userDescription: String? = null
    ) = withContext(Dispatchers.IO) {
        try {
            val errorReport = createErrorReport(
                exception = exception,
                context = context,
                severity = severity,
                additionalData = additionalData,
                userDescription = userDescription
            )
            
            // 分析错误
            val analysis = errorAnalyzer.analyzeError(errorReport)
            val enhancedReport = errorReport.copy(analysis = analysis)
            
            // 保存报告
            saveErrorReport(enhancedReport)
            
            // 添加到上传队列
            addToPendingReports(enhancedReport)
            
            // 记录日志
            structuredLogger.error(
                message = "Error reported",
                context = mapOf(
                    "error_id" to enhancedReport.id,
                    "severity" to severity.name,
                    "context" to context,
                    "analysis_score" to analysis.riskScore.toString()
                ),
                exception = exception
            )
            
            // 更新状态
            updateReportingState(enhancedReport)
            
            // 如果是高严重性错误，立即尝试上传
            if (severity == ErrorSeverity.HIGH || severity == ErrorSeverity.CRITICAL) {
                triggerImmediateUpload()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "报告错误失败")
        }
    }
    
    /**
     * 创建错误报告
     */
    private fun createErrorReport(
        exception: Throwable,
        context: String,
        severity: ErrorSeverity,
        additionalData: Map<String, Any>,
        userDescription: String?
    ): ErrorReport {
        return ErrorReport(
            id = generateReportId(),
            timestamp = System.currentTimeMillis(),
            exception = ExceptionInfo(
                type = exception.javaClass.name,
                message = exception.message ?: "No message",
                stackTrace = getStackTrace(exception),
                cause = exception.cause?.let { cause ->
                    ExceptionInfo(
                        type = cause.javaClass.name,
                        message = cause.message ?: "No message",
                        stackTrace = getStackTrace(cause)
                    )
                }
            ),
            context = context,
            severity = severity,
            additionalData = additionalData,
            userDescription = userDescription,
            deviceInfo = collectDeviceInfo(),
            appInfo = collectAppInfo(),
            systemInfo = collectSystemInfo(),
            userInfo = collectUserInfo(),
            sessionInfo = collectSessionInfo(),
            networkInfo = collectNetworkInfo(),
            performanceInfo = collectPerformanceInfo()
        )
    }
    
    /**
     * 保存错误报告
     */
    private suspend fun saveErrorReport(errorReport: ErrorReport) = withContext(Dispatchers.IO) {
        try {
            val fileName = "error_${errorReport.id}_${dateFormat.format(Date(errorReport.timestamp))}.json"
            val reportFile = File(errorReportsDir, fileName)
            
            val reportJson = json.encodeToString(errorReport)
            reportFile.writeText(reportJson)
            
            Timber.d("错误报告已保存: ${reportFile.name}")
            
        } catch (e: Exception) {
            Timber.e(e, "保存错误报告失败")
        }
    }
    
    /**
     * 添加到待上传报告队列
     */
    private fun addToPendingReports(errorReport: ErrorReport) {
        synchronized(uploadLock) {
            pendingReports.add(errorReport)
            
            // 限制队列大小
            if (pendingReports.size > MAX_PENDING_REPORTS) {
                pendingReports.removeAt(0)
            }
        }
    }
    
    /**
     * 启动报告处理器
     */
    private fun startReportProcessor() {
        scope.launch {
            while (isActive) {
                try {
                    delay(60000L) // 每分钟检查一次
                    processReports()
                } catch (e: Exception) {
                    Timber.e(e, "报告处理器错误")
                }
            }
        }
    }
    
    /**
     * 处理报告
     */
    private suspend fun processReports() = withContext(Dispatchers.IO) {
        synchronized(uploadLock) {
            if (pendingReports.isEmpty()) return@withContext
            
            val reportsToUpload = pendingReports.take(REPORT_BATCH_SIZE).toList()
            uploadReports(reportsToUpload)
        }
    }
    
    /**
     * 上传报告
     */
    private suspend fun uploadReports(reports: List<ErrorReport>) = withContext(Dispatchers.IO) {
        try {
            // 这里应该实现实际的上传逻辑
            // 例如：发送到服务器、Firebase、Bugsnag等
            
            val uploadResult = simulateUpload(reports)
            
            if (uploadResult.isSuccess) {
                synchronized(uploadLock) {
                    pendingReports.removeAll(reports)
                }
                
                structuredLogger.info(
                    message = "Error reports uploaded successfully",
                    context = mapOf(
                        "report_count" to reports.size.toString(),
                        "upload_timestamp" to System.currentTimeMillis().toString()
                    )
                )
                
                // 删除已上传的报告文件
                deleteUploadedReports(reports)
                
            } else {
                Timber.w("错误报告上传失败: ${uploadResult.error}")
                
                // 重试逻辑
                retryUpload(reports)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "上传错误报告时发生异常")
        }
    }
    
    /**
     * 模拟上传（实际实现时应替换为真实的上传逻辑）
     */
    private suspend fun simulateUpload(reports: List<ErrorReport>): UploadResult {
        delay(1000) // 模拟网络延迟
        
        // 模拟成功率（实际实现时应移除）
        return if (Math.random() > 0.2) { // 80% 成功率
            UploadResult(isSuccess = true)
        } else {
            UploadResult(isSuccess = false, error = "Network error")
        }
    }
    
    /**
     * 重试上传
     */
    private suspend fun retryUpload(reports: List<ErrorReport>) {
        scope.launch {
            repeat(MAX_UPLOAD_RETRIES) { attempt ->
                delay(UPLOAD_RETRY_DELAY_MS * (attempt + 1))
                
                val retryResult = simulateUpload(reports)
                if (retryResult.isSuccess) {
                    synchronized(uploadLock) {
                        pendingReports.removeAll(reports)
                    }
                    deleteUploadedReports(reports)
                    return@launch
                }
            }
            
            Timber.w("错误报告重试上传失败，将保留在本地")
        }
    }
    
    /**
     * 删除已上传的报告文件
     */
    private suspend fun deleteUploadedReports(reports: List<ErrorReport>) = withContext(Dispatchers.IO) {
        reports.forEach { report ->
            try {
                val fileName = "error_${report.id}_${dateFormat.format(Date(report.timestamp))}.json"
                val reportFile = File(errorReportsDir, fileName)
                
                if (reportFile.exists() && reportFile.delete()) {
                    Timber.d("已删除上传的报告文件: ${reportFile.name}")
                }
            } catch (e: Exception) {
                Timber.w(e, "删除报告文件失败: ${report.id}")
            }
        }
    }
    
    /**
     * 触发立即上传
     */
    private fun triggerImmediateUpload() {
        scope.launch {
            processReports()
        }
    }
    
    /**
     * 加载待上传的报告
     */
    private fun loadPendingReports() {
        scope.launch {
            try {
                val reportFiles = errorReportsDir.listFiles()
                    ?.filter { it.name.endsWith(".json") }
                    ?.sortedBy { it.lastModified() }
                
                reportFiles?.forEach { file ->
                    try {
                        val reportJson = file.readText()
                        val errorReport = json.decodeFromString<ErrorReport>(reportJson)
                        addToPendingReports(errorReport)
                    } catch (e: Exception) {
                        Timber.w(e, "加载错误报告失败: ${file.name}")
                    }
                }
                
                Timber.d("已加载 ${pendingReports.size} 个待上传的错误报告")
                
            } catch (e: Exception) {
                Timber.e(e, "加载待上传报告失败")
            }
        }
    }
    
    /**
     * 生成报告ID
     */
    private fun generateReportId(): String {
        return "ERR_${System.currentTimeMillis()}_${UUID.randomUUID().toString().take(8)}"
    }
    
    /**
     * 收集设备信息
     */
    private fun collectDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = android.os.Build.MANUFACTURER,
            model = android.os.Build.MODEL,
            androidVersion = android.os.Build.VERSION.RELEASE,
            apiLevel = android.os.Build.VERSION.SDK_INT,
            brand = android.os.Build.BRAND,
            device = android.os.Build.DEVICE,
            hardware = android.os.Build.HARDWARE,
            totalMemory = getTotalMemory(),
            availableMemory = getAvailableMemory(),
            storageSpace = getAvailableStorage()
        )
    }
    
    /**
     * 收集应用信息
     */
    private fun collectAppInfo(): AppInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            AppInfo(
                packageName = context.packageName,
                versionName = packageInfo.versionName ?: "unknown",
                versionCode = packageInfo.longVersionCode,
                buildType = if (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE != 0) "debug" else "release",
                installTime = packageInfo.firstInstallTime,
                updateTime = packageInfo.lastUpdateTime
            )
        } catch (e: Exception) {
            AppInfo("unknown", "unknown", 0, "unknown", 0, 0)
        }
    }
    
    /**
     * 收集系统信息
     */
    private fun collectSystemInfo(): SystemInfo {
        return SystemInfo(
            osVersion = android.os.Build.VERSION.RELEASE,
            apiLevel = android.os.Build.VERSION.SDK_INT,
            kernelVersion = System.getProperty("os.version") ?: "unknown",
            locale = Locale.getDefault().toString(),
            timezone = TimeZone.getDefault().id,
            uptime = android.os.SystemClock.elapsedRealtime(),
            bootTime = System.currentTimeMillis() - android.os.SystemClock.elapsedRealtime()
        )
    }
    
    /**
     * 收集用户信息
     */
    private fun collectUserInfo(): UserInfo {
        // 这里应该从用户管理器获取信息，注意隐私保护
        return UserInfo(
            userId = null, // 不收集用户ID以保护隐私
            userType = "anonymous",
            sessionDuration = getSessionDuration()
        )
    }
    
    /**
     * 收集会话信息
     */
    private fun collectSessionInfo(): SessionInfo {
        return SessionInfo(
            sessionId = getSessionId(),
            startTime = getSessionStartTime(),
            duration = getSessionDuration(),
            screenCount = getScreenCount(),
            interactionCount = getInteractionCount()
        )
    }
    
    /**
     * 收集网络信息
     */
    private fun collectNetworkInfo(): NetworkInfo {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            
            NetworkInfo(
                isConnected = activeNetwork?.isConnected ?: false,
                networkType = activeNetwork?.typeName ?: "unknown",
                networkSubtype = activeNetwork?.subtypeName ?: "unknown",
                isRoaming = activeNetwork?.isRoaming ?: false
            )
        } catch (e: Exception) {
            NetworkInfo(false, "unknown", "unknown", false)
        }
    }
    
    /**
     * 收集性能信息
     */
    private fun collectPerformanceInfo(): PerformanceInfo {
        return try {
            val runtime = Runtime.getRuntime()
            PerformanceInfo(
                heapUsed = runtime.totalMemory() - runtime.freeMemory(),
                heapMax = runtime.maxMemory(),
                cpuUsage = getCpuUsage(),
                batteryLevel = getBatteryLevel(),
                thermalState = getThermalState()
            )
        } catch (e: Exception) {
            PerformanceInfo(0, 0, 0.0, 0, "unknown")
        }
    }
    
    // 辅助方法
    private fun getTotalMemory(): Long = try {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val memInfo = android.app.ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)
        memInfo.totalMem
    } catch (e: Exception) { -1L }
    
    private fun getAvailableMemory(): Long = try {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val memInfo = android.app.ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)
        memInfo.availMem
    } catch (e: Exception) { -1L }
    
    private fun getAvailableStorage(): Long = try {
        val stat = android.os.StatFs(context.filesDir.path)
        stat.availableBytes
    } catch (e: Exception) { -1L }
    
    private fun getStackTrace(exception: Throwable): String = try {
        val stringWriter = java.io.StringWriter()
        val printWriter = java.io.PrintWriter(stringWriter)
        exception.printStackTrace(printWriter)
        stringWriter.toString()
    } catch (e: Exception) { "Unable to get stack trace" }
    
    private fun getSessionId(): String = "session_${System.currentTimeMillis()}"
    private fun getSessionStartTime(): Long = System.currentTimeMillis() - 3600000L // 假设1小时前开始
    private fun getSessionDuration(): Long = 3600000L // 假设1小时
    private fun getScreenCount(): Int = 5 // 假设访问了5个屏幕
    private fun getInteractionCount(): Int = 20 // 假设20次交互
    private fun getCpuUsage(): Double = Math.random() * 100 // 模拟CPU使用率
    private fun getBatteryLevel(): Int = (Math.random() * 100).toInt() // 模拟电池电量
    private fun getThermalState(): String = "normal" // 模拟热状态
    
    /**
     * 更新报告状态
     */
    private fun updateReportingState(errorReport: ErrorReport) {
        val currentState = _reportingState.value
        _reportingState.value = currentState.copy(
            totalReports = currentState.totalReports + 1,
            lastReportTime = errorReport.timestamp,
            lastReportId = errorReport.id,
            pendingUploads = pendingReports.size,
            reportsBySeverity = currentState.reportsBySeverity.toMutableMap().apply {
                this[errorReport.severity] = (this[errorReport.severity] ?: 0) + 1
            }
        )
    }
    
    /**
     * 获取错误报告统计
     */
    fun getReportingStatistics(): ErrorReportingStatistics {
        val currentState = _reportingState.value
        return ErrorReportingStatistics(
            totalReports = currentState.totalReports,
            pendingUploads = currentState.pendingUploads,
            reportsBySeverity = currentState.reportsBySeverity,
            lastReportTime = currentState.lastReportTime
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        Timber.d("错误报告器已清理")
    }
}

// 数据类定义
data class ErrorReport(
    val id: String,
    val timestamp: Long,
    val exception: ExceptionInfo,
    val context: String,
    val severity: ErrorSeverity,
    val additionalData: Map<String, Any>,
    val userDescription: String?,
    val deviceInfo: DeviceInfo,
    val appInfo: AppInfo,
    val systemInfo: SystemInfo,
    val userInfo: UserInfo,
    val sessionInfo: SessionInfo,
    val networkInfo: NetworkInfo,
    val performanceInfo: PerformanceInfo,
    val analysis: ErrorAnalysis? = null
)

data class UserInfo(
    val userId: String?,
    val userType: String,
    val sessionDuration: Long
)

data class SessionInfo(
    val sessionId: String,
    val startTime: Long,
    val duration: Long,
    val screenCount: Int,
    val interactionCount: Int
)

data class PerformanceInfo(
    val heapUsed: Long,
    val heapMax: Long,
    val cpuUsage: Double,
    val batteryLevel: Int,
    val thermalState: String
)

data class UploadResult(
    val isSuccess: Boolean,
    val error: String? = null
)

data class ErrorReportingState(
    val totalReports: Long = 0,
    val lastReportTime: Long = 0,
    val lastReportId: String = "",
    val pendingUploads: Int = 0,
    val reportsBySeverity: Map<ErrorSeverity, Int> = emptyMap()
)

data class ErrorReportingStatistics(
    val totalReports: Long,
    val pendingUploads: Int,
    val reportsBySeverity: Map<ErrorSeverity, Int>,
    val lastReportTime: Long
)
