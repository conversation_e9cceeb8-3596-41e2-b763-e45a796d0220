package com.cadence.core.database.entity

import androidx.room.*
import kotlinx.serialization.Serializable

/**
 * 收藏夹数据实体
 */
@Entity(
    tableName = "favorite_folders",
    indices = [
        Index(value = ["name"], unique = true),
        Index(value = ["is_default"]),
        Index(value = ["sort_order"]),
        Index(value = ["created_at"]),
        Index(value = ["updated_at"])
    ]
)
@Serializable
data class FavoriteFolderEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "description")
    val description: String? = null,
    
    @ColumnInfo(name = "color")
    val color: String = "#2196F3",
    
    @ColumnInfo(name = "icon")
    val icon: String? = null,
    
    @ColumnInfo(name = "is_default")
    val isDefault: Boolean = false,
    
    @ColumnInfo(name = "sort_order")
    val sortOrder: Int = 0,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 收藏项数据实体
 */
@Entity(
    tableName = "favorite_items",
    indices = [
        Index(value = ["translation_id"], unique = true),
        Index(value = ["folder_id"]),
        Index(value = ["priority"]),
        Index(value = ["created_at"]),
        Index(value = ["updated_at"])
    ],
    foreignKeys = [
        ForeignKey(
            entity = FavoriteFolderEntity::class,
            parentColumns = ["id"],
            childColumns = ["folder_id"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = TranslationEntity::class,
            parentColumns = ["id"],
            childColumns = ["translation_id"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
@Serializable
data class FavoriteItemEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "translation_id")
    val translationId: String,
    
    @ColumnInfo(name = "folder_id")
    val folderId: String,
    
    @ColumnInfo(name = "note")
    val note: String? = null,
    
    @ColumnInfo(name = "tags")
    val tags: String = "[]", // JSON格式存储标签列表
    
    @ColumnInfo(name = "priority")
    val priority: String = "NORMAL", // FavoritePriority枚举值
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 收藏夹配置数据实体
 */
@Entity(
    tableName = "favorite_config",
    indices = [
        Index(value = ["updated_at"])
    ]
)
@Serializable
data class FavoriteConfigEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String = "default_config",
    
    @ColumnInfo(name = "auto_sync")
    val autoSync: Boolean = true,
    
    @ColumnInfo(name = "default_folder_id")
    val defaultFolderId: String? = null,
    
    @ColumnInfo(name = "max_items_per_folder")
    val maxItemsPerFolder: Int = 1000,
    
    @ColumnInfo(name = "enable_notifications")
    val enableNotifications: Boolean = true,
    
    @ColumnInfo(name = "show_item_count")
    val showItemCount: Boolean = true,
    
    @ColumnInfo(name = "compact_view")
    val compactView: Boolean = false,
    
    @ColumnInfo(name = "enable_tags")
    val enableTags: Boolean = true,
    
    @ColumnInfo(name = "enable_priority")
    val enablePriority: Boolean = true,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 收藏夹操作记录数据实体
 */
@Entity(
    tableName = "favorite_operations",
    indices = [
        Index(value = ["operation_type"]),
        Index(value = ["target_id"]),
        Index(value = ["timestamp"])
    ]
)
@Serializable
data class FavoriteOperationEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "operation_type")
    val operationType: String, // FavoriteOperationType枚举值
    
    @ColumnInfo(name = "target_id")
    val targetId: String,
    
    @ColumnInfo(name = "details")
    val details: String? = null,
    
    @ColumnInfo(name = "timestamp")
    val timestamp: Long
)

/**
 * 收藏夹同步信息数据实体
 */
@Entity(
    tableName = "favorite_sync_info",
    indices = [
        Index(value = ["item_id", "item_type"], unique = true),
        Index(value = ["status"]),
        Index(value = ["last_sync_time"])
    ]
)
@Serializable
data class FavoriteSyncInfoEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "item_id")
    val itemId: String,
    
    @ColumnInfo(name = "item_type")
    val itemType: String, // "folder" 或 "item"
    
    @ColumnInfo(name = "status")
    val status: String, // FavoriteSyncStatus枚举值
    
    @ColumnInfo(name = "last_sync_time")
    val lastSyncTime: Long? = null,
    
    @ColumnInfo(name = "sync_error")
    val syncError: String? = null,
    
    @ColumnInfo(name = "version")
    val version: Int = 1,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 收藏夹使用统计数据实体
 */
@Entity(
    tableName = "favorite_usage_stats",
    indices = [
        Index(value = ["folder_id"], unique = true),
        Index(value = ["access_count"]),
        Index(value = ["last_access_time"])
    ],
    foreignKeys = [
        ForeignKey(
            entity = FavoriteFolderEntity::class,
            parentColumns = ["id"],
            childColumns = ["folder_id"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
@Serializable
data class FavoriteUsageStatsEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "folder_id")
    val folderId: String,
    
    @ColumnInfo(name = "access_count")
    val accessCount: Int = 0,
    
    @ColumnInfo(name = "last_access_time")
    val lastAccessTime: Long,
    
    @ColumnInfo(name = "average_session_duration")
    val averageSessionDuration: Long = 0,
    
    @ColumnInfo(name = "most_used_tags")
    val mostUsedTags: String = "[]", // JSON格式存储标签列表
    
    @ColumnInfo(name = "peak_usage_hour")
    val peakUsageHour: Int = 0, // 0-23
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 收藏夹搜索建议数据实体
 */
@Entity(
    tableName = "favorite_search_suggestions",
    indices = [
        Index(value = ["type", "value"], unique = true),
        Index(value = ["count"]),
        Index(value = ["last_used"])
    ]
)
@Serializable
data class FavoriteSearchSuggestionEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "type")
    val type: String, // "folder", "tag", "translation"
    
    @ColumnInfo(name = "value")
    val value: String,
    
    @ColumnInfo(name = "count")
    val count: Int = 1,
    
    @ColumnInfo(name = "last_used")
    val lastUsed: Long,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long
)

/**
 * 收藏夹与翻译记录的关联视图
 * 用于查询收藏项及其对应的翻译详情
 */
data class FavoriteItemWithTranslation(
    @Embedded val favoriteItem: FavoriteItemEntity,
    @Embedded(prefix = "translation_") val translation: TranslationEntity?
)

/**
 * 收藏夹与项目数量的关联视图
 * 用于显示收藏夹列表及其包含的项目数量
 */
data class FavoriteFolderWithItemCount(
    @Embedded val folder: FavoriteFolderEntity,
    @ColumnInfo(name = "item_count") val itemCount: Int
)
