package com.cadence.feature.settings.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.*
import com.cadence.domain.repository.UserPreferenceRepository
import com.cadence.core.util.LocaleManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 设置界面ViewModel
 * 管理用户偏好设置的状态和业务逻辑
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userPreferenceRepository: UserPreferenceRepository,
    private val localeManager: LocaleManager
) : ViewModel() {

    // 用户偏好设置
    val userPreference = userPreferenceRepository.getUserPreference()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    // 用户完整设置
    val userSettings = userPreferenceRepository.getUserSettings()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )

    // UI状态
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState = _uiState.asStateFlow()

    /**
     * 更新主题模式
     */
    fun updateThemeMode(themeMode: ThemeMode) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = userPreferenceRepository.updateThemeMode(themeMode)
                result.fold(
                    onSuccess = {
                        Timber.d("主题模式更新成功: $themeMode")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "主题已更新"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "主题模式更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "主题更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新主题模式时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新字体大小
     */
    fun updateFontSize(fontSize: FontSize) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = userPreferenceRepository.updateFontSize(fontSize)
                result.fold(
                    onSuccess = {
                        Timber.d("字体大小更新成功: $fontSize")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "字体大小已更新"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "字体大小更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "字体大小更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新字体大小时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新默认源语言
     */
    fun updateDefaultSourceLanguage(language: Language) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = userPreferenceRepository.updateDefaultSourceLanguage(language)
                result.fold(
                    onSuccess = {
                        Timber.d("默认源语言更新成功: ${language.name}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "默认源语言已更新"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "默认源语言更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "语言更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新默认源语言时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新默认目标语言
     */
    fun updateDefaultTargetLanguage(language: Language) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = userPreferenceRepository.updateDefaultTargetLanguage(language)
                result.fold(
                    onSuccess = {
                        Timber.d("默认目标语言更新成功: ${language.name}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "默认目标语言已更新"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "默认目标语言更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "语言更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新默认目标语言时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 切换自动检测语言
     */
    fun toggleAutoDetectLanguage() {
        viewModelScope.launch {
            try {
                val currentValue = userPreference.value?.autoDetectLanguage ?: true
                val result = userPreferenceRepository.updateAutoDetectLanguage(!currentValue)
                result.fold(
                    onSuccess = {
                        Timber.d("自动检测语言设置更新成功: ${!currentValue}")
                    },
                    onFailure = { error ->
                        Timber.e(error, "自动检测语言设置更新失败")
                        _uiState.value = _uiState.value.copy(
                            error = "设置更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "切换自动检测语言时发生异常")
                _uiState.value = _uiState.value.copy(
                    error = "设置更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 切换保存翻译历史
     */
    fun toggleSaveTranslationHistory() {
        viewModelScope.launch {
            try {
                val currentValue = userPreference.value?.saveTranslationHistory ?: true
                val result = userPreferenceRepository.updateSaveTranslationHistory(!currentValue)
                result.fold(
                    onSuccess = {
                        Timber.d("保存翻译历史设置更新成功: ${!currentValue}")
                    },
                    onFailure = { error ->
                        Timber.e(error, "保存翻译历史设置更新失败")
                        _uiState.value = _uiState.value.copy(
                            error = "设置更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "切换保存翻译历史时发生异常")
                _uiState.value = _uiState.value.copy(
                    error = "设置更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 切换文化背景解释
     */
    fun toggleEnableCulturalContext() {
        viewModelScope.launch {
            try {
                val currentValue = userPreference.value?.enableCulturalContext ?: true
                val result = userPreferenceRepository.updateEnableCulturalContext(!currentValue)
                result.fold(
                    onSuccess = {
                        Timber.d("文化背景解释设置更新成功: ${!currentValue}")
                    },
                    onFailure = { error ->
                        Timber.e(error, "文化背景解释设置更新失败")
                        _uiState.value = _uiState.value.copy(
                            error = "设置更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "切换文化背景解释时发生异常")
                _uiState.value = _uiState.value.copy(
                    error = "设置更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 重置设置为默认值
     */
    fun resetToDefault() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val result = userPreferenceRepository.resetToDefault()
                result.fold(
                    onSuccess = {
                        Timber.d("设置重置为默认值成功")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "设置已重置为默认值"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "设置重置失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "重置失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "重置设置时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "重置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }

    /**
     * 更新应用语言
     */
    fun updateAppLanguage(appLanguage: AppLanguage) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val result = userPreferenceRepository.updateAppLanguage(appLanguage)
                result.fold(
                    onSuccess = {
                        Timber.d("应用语言更新成功: ${appLanguage.displayName}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "语言设置已更新"
                        )
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "应用语言更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "语言设置更新失败: ${exception.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "应用语言更新异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "语言设置更新异常: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取支持的语言列表
     */
    fun getSupportedLanguages(): List<AppLanguage> {
        return localeManager.getSupportedLanguages()
    }

    /**
     * 获取语言显示名称
     */
    fun getLanguageDisplayName(appLanguage: AppLanguage): String {
        // 这里需要Context，在实际使用时通过Compose的LocalContext获取
        return appLanguage.displayName
    }

    /**
     * 更新主题色彩
     */
    fun updateThemeColor(themeColor: ThemeColor) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                val result = userPreferenceRepository.updateThemeColor(themeColor)
                result.fold(
                    onSuccess = {
                        Timber.d("主题色彩更新成功: ${themeColor.displayName}")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            message = "主题色彩已更新"
                        )
                    },
                    onFailure = { error ->
                        Timber.e(error, "主题色彩更新失败")
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "主题色彩更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "更新主题色彩时发生异常")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "主题色彩更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 切换动态颜色设置
     */
    fun toggleDynamicColor() {
        viewModelScope.launch {
            try {
                val currentValue = userPreference.value?.enableDynamicColor ?: true
                val result = userPreferenceRepository.updateEnableDynamicColor(!currentValue)
                result.fold(
                    onSuccess = {
                        Timber.d("动态颜色设置更新成功: ${!currentValue}")
                    },
                    onFailure = { error ->
                        Timber.e(error, "动态颜色设置更新失败")
                        _uiState.value = _uiState.value.copy(
                            error = "设置更新失败: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "切换动态颜色设置时发生异常")
                _uiState.value = _uiState.value.copy(
                    error = "设置更新失败: ${e.message}"
                )
            }
        }
    }
}

/**
 * 设置界面UI状态
 */
data class SettingsUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
