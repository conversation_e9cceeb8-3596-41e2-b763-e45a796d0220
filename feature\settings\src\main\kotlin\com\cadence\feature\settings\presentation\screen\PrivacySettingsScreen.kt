package com.cadence.feature.settings.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.settings.presentation.component.*
import com.cadence.feature.settings.presentation.viewmodel.SettingsViewModel
import timber.log.Timber

/**
 * 隐私设置界面
 * 管理用户隐私和数据清理功能
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacySettingsScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val userSettings by viewModel.userSettings.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var showClearHistoryDialog by remember { mutableStateOf(false) }
    var showClearCacheDialog by remember { mutableStateOf(false) }
    var showClearAllDataDialog by remember { mutableStateOf(false) }
    
    val privacySettings = userSettings?.privacySettings

    // 显示错误提示
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            Timber.e("隐私设置错误: $error")
            viewModel.clearError()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "隐私与安全",
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(vertical = 16.dp)
        ) {
            // 隐私设置说明
            item {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Security,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "隐私保护",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.SemiBold,
                                color = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "管理您的数据隐私设置，控制信息的收集和使用方式。",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(24.dp))
            }

            // 数据收集设置
            item {
                SettingsGroupTitle(title = "数据收集")
            }

            item {
                SettingsSwitch(
                    title = "使用统计",
                    subtitle = "允许收集匿名使用数据以改进应用",
                    icon = Icons.Default.Analytics,
                    checked = privacySettings?.allowUsageAnalytics ?: true,
                    onCheckedChange = { 
                        // TODO: 实现使用统计开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "崩溃报告",
                    subtitle = "自动发送崩溃报告帮助修复问题",
                    icon = Icons.Default.BugReport,
                    checked = privacySettings?.allowCrashReports ?: true,
                    onCheckedChange = { 
                        // TODO: 实现崩溃报告开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "个性化建议",
                    subtitle = "基于使用习惯提供个性化功能建议",
                    icon = Icons.Default.Recommend,
                    checked = privacySettings?.allowPersonalization ?: true,
                    onCheckedChange = { 
                        // TODO: 实现个性化建议开关
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 翻译隐私设置
            item {
                SettingsGroupTitle(title = "翻译隐私")
            }

            item {
                SettingsSwitch(
                    title = "保存翻译历史",
                    subtitle = "在本地保存翻译记录",
                    icon = Icons.Default.History,
                    checked = privacySettings?.saveTranslationHistory ?: true,
                    onCheckedChange = { 
                        // TODO: 实现翻译历史保存开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "云端同步",
                    subtitle = "将翻译历史同步到云端",
                    icon = Icons.Default.CloudSync,
                    checked = privacySettings?.enableCloudSync ?: false,
                    enabled = privacySettings?.saveTranslationHistory ?: true,
                    onCheckedChange = { 
                        // TODO: 实现云端同步开关
                    }
                )
            }

            item {
                SettingsSwitch(
                    title = "敏感内容过滤",
                    subtitle = "自动检测并保护敏感信息",
                    icon = Icons.Default.Shield,
                    checked = privacySettings?.enableSensitiveContentFilter ?: true,
                    onCheckedChange = { 
                        // TODO: 实现敏感内容过滤开关
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 数据清理
            item {
                SettingsGroupTitle(title = "数据清理")
            }

            item {
                SettingsItem(
                    title = "清理翻译历史",
                    subtitle = "删除所有本地翻译记录",
                    icon = Icons.Default.DeleteSweep,
                    onClick = { showClearHistoryDialog = true }
                )
            }

            item {
                SettingsItem(
                    title = "清理缓存数据",
                    subtitle = "删除临时文件和缓存",
                    icon = Icons.Default.CleaningServices,
                    onClick = { showClearCacheDialog = true }
                )
            }

            item {
                SettingsItem(
                    title = "清理收藏夹",
                    subtitle = "删除所有收藏的翻译",
                    icon = Icons.Default.FavoriteBorder,
                    onClick = {
                        // TODO: 实现清理收藏夹功能
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 账户管理
            item {
                SettingsGroupTitle(title = "账户管理")
            }

            item {
                SettingsItem(
                    title = "导出数据",
                    subtitle = "导出您的翻译历史和设置",
                    icon = Icons.Default.Download,
                    onClick = {
                        // TODO: 实现数据导出功能
                    }
                )
            }

            item {
                SettingsItem(
                    title = "重置所有设置",
                    subtitle = "恢复应用到初始状态",
                    icon = Icons.Default.RestartAlt,
                    onClick = {
                        // TODO: 显示重置确认对话框
                    }
                )
            }

            item {
                SettingsItem(
                    title = "删除所有数据",
                    subtitle = "永久删除所有应用数据",
                    icon = Icons.Default.DeleteForever,
                    onClick = { showClearAllDataDialog = true }
                )
            }

            item {
                SettingsDivider()
            }

            // 隐私政策
            item {
                SettingsGroupTitle(title = "法律信息")
            }

            item {
                SettingsItem(
                    title = "隐私政策",
                    subtitle = "查看我们的隐私保护政策",
                    icon = Icons.Default.Policy,
                    onClick = {
                        // TODO: 打开隐私政策页面
                    }
                )
            }

            item {
                SettingsItem(
                    title = "用户协议",
                    subtitle = "查看服务条款和使用协议",
                    icon = Icons.Default.Gavel,
                    onClick = {
                        // TODO: 打开用户协议页面
                    }
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // 清理翻译历史确认对话框
    if (showClearHistoryDialog) {
        AlertDialog(
            onDismissRequest = { showClearHistoryDialog = false },
            title = { Text("清理翻译历史") },
            text = { Text("确定要删除所有翻译历史记录吗？此操作无法撤销。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        // TODO: 实现清理翻译历史
                        showClearHistoryDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearHistoryDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 清理缓存确认对话框
    if (showClearCacheDialog) {
        AlertDialog(
            onDismissRequest = { showClearCacheDialog = false },
            title = { Text("清理缓存数据") },
            text = { Text("确定要清理所有缓存数据吗？这可能会影响应用的启动速度。") },
            confirmButton = {
                TextButton(
                    onClick = {
                        // TODO: 实现清理缓存
                        showClearCacheDialog = false
                    }
                ) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearCacheDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 删除所有数据确认对话框
    if (showClearAllDataDialog) {
        AlertDialog(
            onDismissRequest = { showClearAllDataDialog = false },
            title = { Text("删除所有数据") },
            text = { Text("警告：此操作将永久删除所有应用数据，包括翻译历史、收藏夹和设置。此操作无法撤销！") },
            confirmButton = {
                TextButton(
                    onClick = {
                        // TODO: 实现删除所有数据
                        showClearAllDataDialog = false
                    }
                ) {
                    Text("确定删除")
                }
            },
            dismissButton = {
                TextButton(onClick = { showClearAllDataDialog = false }) {
                    Text("取消")
                }
            }
        )
    }

    // 加载指示器
    if (uiState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}
