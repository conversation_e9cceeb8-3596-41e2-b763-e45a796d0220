package com.cadence.core.util

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.cadence.domain.model.AppLanguage
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语言管理器
 * 负责应用的多语言支持和动态语言切换
 */
@Singleton
class LocaleManager @Inject constructor() {

    companion object {
        private const val TAG = "LocaleManager"
        private const val PREF_SELECTED_LANGUAGE = "selected_language"
    }

    /**
     * 获取当前应用语言设置
     */
    fun getCurrentAppLanguage(context: Context): AppLanguage {
        val sharedPrefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        val languageCode = sharedPrefs.getString(PREF_SELECTED_LANGUAGE, AppLanguage.SYSTEM.code)
        return AppLanguage.values().find { it.code == languageCode } ?: AppLanguage.SYSTEM
    }

    /**
     * 设置应用语言
     */
    fun setAppLanguage(context: Context, appLanguage: AppLanguage) {
        Timber.d("$TAG: 设置应用语言为: ${appLanguage.displayName}")
        
        // 保存语言设置
        val sharedPrefs = context.getSharedPreferences("app_settings", Context.MODE_PRIVATE)
        sharedPrefs.edit().putString(PREF_SELECTED_LANGUAGE, appLanguage.code).apply()
        
        // 应用语言设置
        applyLanguage(context, appLanguage)
    }

    /**
     * 应用语言设置
     */
    private fun applyLanguage(context: Context, appLanguage: AppLanguage) {
        when (appLanguage) {
            AppLanguage.SYSTEM -> {
                // 跟随系统语言
                AppCompatDelegate.setApplicationLocales(LocaleListCompat.getEmptyLocaleList())
                Timber.d("$TAG: 设置为跟随系统语言")
            }
            else -> {
                // 设置特定语言
                val locale = createLocale(appLanguage.code)
                val localeList = LocaleListCompat.create(locale)
                AppCompatDelegate.setApplicationLocales(localeList)
                Timber.d("$TAG: 设置语言为: ${locale.displayLanguage}")
            }
        }
    }

    /**
     * 创建Locale对象
     */
    private fun createLocale(languageCode: String): Locale {
        return when (languageCode) {
            "zh" -> Locale.CHINESE
            "en" -> Locale.ENGLISH
            "ja" -> Locale.JAPANESE
            "ko" -> Locale.KOREAN
            else -> Locale.getDefault()
        }
    }

    /**
     * 获取系统当前语言
     */
    fun getSystemLanguage(): AppLanguage {
        val systemLocale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            LocaleListCompat.getDefault()[0] ?: Locale.getDefault()
        } else {
            Locale.getDefault()
        }
        
        return when (systemLocale.language) {
            "zh" -> AppLanguage.CHINESE
            "en" -> AppLanguage.ENGLISH
            "ja" -> AppLanguage.JAPANESE
            "ko" -> AppLanguage.KOREAN
            else -> AppLanguage.ENGLISH // 默认英文
        }
    }

    /**
     * 获取所有支持的语言
     */
    fun getSupportedLanguages(): List<AppLanguage> {
        return AppLanguage.values().toList()
    }

    /**
     * 检查是否需要重启Activity来应用语言更改
     */
    fun shouldRecreateActivity(context: Context, newLanguage: AppLanguage): Boolean {
        val currentLanguage = getCurrentAppLanguage(context)
        return currentLanguage != newLanguage
    }

    /**
     * 获取语言显示名称（本地化）
     */
    fun getLanguageDisplayName(context: Context, appLanguage: AppLanguage): String {
        return when (appLanguage) {
            AppLanguage.SYSTEM -> {
                // 根据当前语言环境返回本地化的"跟随系统"文本
                val currentLocale = getCurrentLocale(context)
                when (currentLocale.language) {
                    "zh" -> "跟随系统"
                    "ja" -> "システムに従う"
                    "ko" -> "시스템 따라가기"
                    else -> "Follow System"
                }
            }
            AppLanguage.CHINESE -> "中文"
            AppLanguage.ENGLISH -> "English"
            AppLanguage.JAPANESE -> "日本語"
            AppLanguage.KOREAN -> "한국어"
        }
    }

    /**
     * 获取当前Locale
     */
    private fun getCurrentLocale(context: Context): Locale {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.resources.configuration.locales[0]
        } else {
            @Suppress("DEPRECATION")
            context.resources.configuration.locale
        }
    }

    /**
     * 更新Context的语言配置（用于Service等非Activity组件）
     */
    fun updateContextLocale(context: Context, appLanguage: AppLanguage): Context {
        if (appLanguage == AppLanguage.SYSTEM) {
            return context
        }

        val locale = createLocale(appLanguage.code)
        val configuration = Configuration(context.resources.configuration)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale)
            configuration.setLocales(android.os.LocaleList(locale))
        } else {
            @Suppress("DEPRECATION")
            configuration.locale = locale
        }
        
        return context.createConfigurationContext(configuration)
    }

    /**
     * 初始化语言设置（在Application中调用）
     */
    fun initializeLanguage(context: Context) {
        val savedLanguage = getCurrentAppLanguage(context)
        Timber.d("$TAG: 初始化语言设置: ${savedLanguage.displayName}")
        
        if (savedLanguage != AppLanguage.SYSTEM) {
            applyLanguage(context, savedLanguage)
        }
    }

    /**
     * 获取语言代码对应的资源后缀
     */
    fun getResourceSuffix(appLanguage: AppLanguage): String {
        return when (appLanguage) {
            AppLanguage.SYSTEM -> ""
            AppLanguage.CHINESE -> "-zh"
            AppLanguage.ENGLISH -> "-en"
            AppLanguage.JAPANESE -> "-ja"
            AppLanguage.KOREAN -> "-ko"
        }
    }

    /**
     * 检查是否为RTL语言
     */
    fun isRtlLanguage(appLanguage: AppLanguage): Boolean {
        // 当前支持的语言都是LTR，如果以后添加阿拉伯语等RTL语言需要更新
        return false
    }

    /**
     * 获取语言的ISO 639-1代码
     */
    fun getLanguageCode(appLanguage: AppLanguage): String {
        return when (appLanguage) {
            AppLanguage.SYSTEM -> Locale.getDefault().language
            else -> appLanguage.code
        }
    }
}
