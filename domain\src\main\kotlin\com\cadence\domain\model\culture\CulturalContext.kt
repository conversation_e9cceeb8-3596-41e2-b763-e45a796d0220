package com.cadence.domain.model.culture

/**
 * 文化背景上下文数据模型
 * 包含翻译词汇的文化背景信息
 */
data class CulturalContext(
    val id: String,
    val word: String,
    val sourceLanguage: String,
    val targetLanguage: String,
    val region: String,
    val culturalMeaning: String,
    val historicalBackground: String,
    val usageContext: List<UsageContext>,
    val regionalDifferences: List<RegionalDifference>,
    val relatedConcepts: List<String>,
    val examples: List<CulturalExample>,
    val tags: List<String>,
    val difficulty: CulturalDifficulty,
    val createdAt: Long,
    val updatedAt: Long
)

/**
 * 使用场景
 */
data class UsageContext(
    val context: String,
    val description: String,
    val appropriateness: AppropriatenessLevel,
    val examples: List<String>
)

/**
 * 地域差异
 */
data class RegionalDifference(
    val region: String,
    val difference: String,
    val explanation: String,
    val alternativeExpressions: List<String>
)

/**
 * 文化示例
 */
data class CulturalExample(
    val scenario: String,
    val originalText: String,
    val translatedText: String,
    val culturalNote: String,
    val appropriateUsage: Boolean
)

/**
 * 文化难度等级
 */
enum class CulturalDifficulty(val displayName: String, val level: Int) {
    BASIC("基础", 1),
    INTERMEDIATE("中级", 2),
    ADVANCED("高级", 3),
    EXPERT("专家", 4)
}

/**
 * 适用性等级
 */
enum class AppropriatenessLevel(val displayName: String) {
    HIGHLY_APPROPRIATE("非常合适"),
    APPROPRIATE("合适"),
    NEUTRAL("中性"),
    INAPPROPRIATE("不合适"),
    HIGHLY_INAPPROPRIATE("非常不合适")
}

/**
 * 文化知识类型
 */
enum class CulturalKnowledgeType(val displayName: String) {
    IDIOM("习语"),
    PROVERB("谚语"),
    SLANG("俚语"),
    FORMAL_EXPRESSION("正式表达"),
    CULTURAL_REFERENCE("文化典故"),
    SOCIAL_ETIQUETTE("社交礼仪"),
    BUSINESS_CULTURE("商务文化"),
    HISTORICAL_CONTEXT("历史背景")
}

/**
 * 文化推荐
 */
data class CulturalRecommendation(
    val id: String,
    val title: String,
    val description: String,
    val type: CulturalKnowledgeType,
    val relatedWords: List<String>,
    val content: String,
    val difficulty: CulturalDifficulty,
    val region: String,
    val language: String,
    val tags: List<String>,
    val popularity: Int,
    val createdAt: Long
)

/**
 * 文化学习进度
 */
data class CulturalLearningProgress(
    val userId: String,
    val contextId: String,
    val isLearned: Boolean,
    val difficulty: CulturalDifficulty,
    val lastAccessedAt: Long,
    val accessCount: Int,
    val bookmarked: Boolean,
    val notes: String
)