package com.cadence.cadence

import android.app.Application
import com.cadence.core.performance.*
import com.cadence.core.security.*
import com.cadence.core.logging.*
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import javax.inject.Inject

/**
 * Cadence应用程序主类
 *
 * 负责应用程序的全局初始化，包括：
 * - Hilt依赖注入初始化
 * - Timber日志系统配置
 * - 启动性能监控
 * - 延迟初始化管理
 */
@HiltAndroidApp
class CadenceApplication : Application() {

    @Inject
    lateinit var startupTimeTracker: StartupTimeTracker

    @Inject
    lateinit var lazyInitializationManager: LazyInitializationManager

    @Inject
    lateinit var performanceMonitor: PerformanceMonitor

    @Inject
    lateinit var memoryManager: MemoryManager

    @Inject
    lateinit var networkPerformanceOptimizer: NetworkPerformanceOptimizer

    @Inject
    lateinit var securityAuditLogger: SecurityAuditLogger

    @Inject
    lateinit var privacyManager: PrivacyManager

    @Inject
    lateinit var cryptoManager: CryptoManager

    @Inject
    lateinit var globalExceptionHandler: GlobalExceptionHandler

    @Inject
    lateinit var structuredLogger: StructuredLogger

    @Inject
    lateinit var errorReporter: ErrorReporter

    override fun onCreate() {
        // 记录应用启动开始时间
        startupTimeTracker.recordApplicationStart()

        super.onCreate()

        // 只初始化关键组件
        initializeCriticalComponents()

        // 注册延迟初始化任务
        registerLazyInitializationTasks()

        // 启动性能监控
        performanceMonitor.startMonitoring()

        // 启动安全审计日志
        securityAuditLogger.logSecurityEvent(
            event = "application_started",
            severity = AuditSeverity.INFO,
            details = mapOf(
                "app_version" to BuildConfig.VERSION_NAME,
                "build_type" to BuildConfig.BUILD_TYPE
            )
        )

        // 记录Application创建完成
        startupTimeTracker.recordApplicationCreated()

        Timber.i("Cadence应用程序启动完成，性能监控、安全审计和日志系统已启动")
    }
    
    /**
     * 初始化关键组件（仅启动必需的组件）
     */
    private fun initializeCriticalComponents() {
        // 初始化日志系统（关键组件）
        initializeLogging()

        // 初始化全局异常处理（关键组件）
        initializeGlobalExceptionHandling()

        // 记录关键组件初始化
        startupTimeTracker.recordCustomEvent("critical_components_init", "关键组件初始化完成")
    }

    /**
     * 注册延迟初始化任务
     */
    private fun registerLazyInitializationTasks() {
        // 性能监控（非关键，延迟初始化）
        lazyInitializationManager.registerTask("performance_monitoring") {
            initializePerformanceMonitoring()
        }

        // 崩溃报告（非关键，延迟初始化）
        lazyInitializationManager.registerTask("crash_reporting") {
            initializeCrashReporting()
        }

        // 分析工具（非关键，延迟初始化）
        lazyInitializationManager.registerTask("analytics") {
            initializeAnalytics()
        }

        // 推送通知（非关键，延迟初始化）
        lazyInitializationManager.registerTask("push_notifications") {
            initializePushNotifications()
        }

        // 应用内更新（非关键，延迟初始化）
        lazyInitializationManager.registerTask("in_app_updates") {
            initializeInAppUpdates()
        }

        // 安全功能（关键，但可延迟初始化）
        lazyInitializationManager.registerTask("security_features") {
            initializeSecurityFeatures()
        }
    }

    /**
     * 初始化日志系统
     */
    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            // Debug模式下使用详细日志
            Timber.plant(object : Timber.DebugTree() {
                override fun createStackElementTag(element: StackTraceElement): String {
                    return "Cadence_${super.createStackElementTag(element)}:${element.lineNumber}"
                }
            })
        } else {
            // Release模式下使用简化日志（可以集成Crashlytics等）
            Timber.plant(ReleaseTree())
        }

        // 启动结构化日志器
        structuredLogger.initialize()

        Timber.d("日志系统初始化完成")
    }

    /**
     * 初始化全局异常处理
     */
    private fun initializeGlobalExceptionHandling() {
        // 设置全局异常处理器
        Thread.setDefaultUncaughtExceptionHandler(globalExceptionHandler)

        // 记录异常处理器初始化
        structuredLogger.logInfo(
            message = "全局异常处理器已初始化",
            context = mapOf(
                "handler_class" to globalExceptionHandler::class.java.simpleName,
                "thread_name" to Thread.currentThread().name
            )
        )

        Timber.d("全局异常处理器初始化完成")
    }
    
    /**
     * 初始化性能监控（延迟初始化）
     */
    private suspend fun initializePerformanceMonitoring() {
        try {
            // TODO: 集成Firebase Performance或其他性能监控工具
            Timber.d("性能监控初始化完成")
            startupTimeTracker.recordCustomEvent("performance_monitoring_init", "性能监控初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "性能监控初始化失败")
        }
    }

    /**
     * 初始化崩溃报告（延迟初始化）
     */
    private suspend fun initializeCrashReporting() {
        try {
            // TODO: 集成Firebase Crashlytics或其他崩溃报告工具
            Timber.d("崩溃报告初始化完成")
            startupTimeTracker.recordCustomEvent("crash_reporting_init", "崩溃报告初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "崩溃报告初始化失败")
        }
    }

    /**
     * 初始化分析工具（延迟初始化）
     */
    private suspend fun initializeAnalytics() {
        try {
            // TODO: 集成Firebase Analytics或其他分析工具
            Timber.d("分析工具初始化完成")
            startupTimeTracker.recordCustomEvent("analytics_init", "分析工具初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "分析工具初始化失败")
        }
    }

    /**
     * 初始化推送通知（延迟初始化）
     */
    private suspend fun initializePushNotifications() {
        try {
            // TODO: 集成Firebase Messaging或其他推送服务
            Timber.d("推送通知初始化完成")
            startupTimeTracker.recordCustomEvent("push_notifications_init", "推送通知初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "推送通知初始化失败")
        }
    }

    /**
     * 初始化应用内更新（延迟初始化）
     */
    private suspend fun initializeInAppUpdates() {
        try {
            // TODO: 集成Google Play In-App Updates
            Timber.d("应用内更新初始化完成")
            startupTimeTracker.recordCustomEvent("in_app_updates_init", "应用内更新初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "应用内更新初始化失败")
        }
    }

    /**
     * 初始化安全功能（延迟初始化）
     */
    private suspend fun initializeSecurityFeatures() {
        try {
            // 检查加密功能是否可用
            if (!cryptoManager.isEncryptionAvailable()) {
                Timber.w("设备不支持硬件加密，使用软件加密")
                securityAuditLogger.logSecurityEvent(
                    event = "encryption_fallback",
                    severity = AuditSeverity.WARNING,
                    details = mapOf("reason" to "hardware_encryption_unavailable")
                )
            }

            // 检查隐私同意状态
            if (!privacyManager.hasPrivacyConsent()) {
                Timber.i("用户尚未提供隐私同意")
                securityAuditLogger.logPrivacyEvent(
                    event = "privacy_consent_required",
                    details = mapOf("consent_version" to "1")
                )
            }

            // 清理过期数据
            privacyManager.clearExpiredData()

            Timber.d("安全功能初始化完成")
            startupTimeTracker.recordCustomEvent("security_features_init", "安全功能初始化完成")

            securityAuditLogger.logSecurityEvent(
                event = "security_system_initialized",
                severity = AuditSeverity.INFO,
                details = mapOf(
                    "encryption_available" to cryptoManager.isEncryptionAvailable().toString(),
                    "privacy_consent" to privacyManager.hasPrivacyConsent().toString()
                )
            )
        } catch (e: Exception) {
            Timber.e(e, "安全功能初始化失败")
            securityAuditLogger.logSecurityEvent(
                event = "security_initialization_failed",
                severity = AuditSeverity.ERROR,
                details = mapOf("error" to e.message.orEmpty())
            )
        }
    }

    /**
     * Release模式下的日志树
     * 只记录警告和错误级别的日志
     */
    private class ReleaseTree : Timber.Tree() {
        override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
            if (priority >= android.util.Log.WARN) {
                // 在生产环境中，可以将日志发送到远程服务器
                // 例如：Crashlytics.log(message)
            }
        }
    }
}