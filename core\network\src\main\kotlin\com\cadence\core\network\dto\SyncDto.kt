package com.cadence.core.network.dto

import kotlinx.serialization.Serializable

/**
 * 同步请求基类
 */
@Serializable
abstract class BaseSyncRequest {
    abstract val deviceId: String
    abstract val timestamp: Long
    abstract val userId: String?
}

/**
 * 同步响应基类
 */
@Serializable
abstract class BaseSyncResponse {
    abstract val success: Boolean
    abstract val message: String?
    abstract val timestamp: Long
}

/**
 * 翻译历史同步请求
 */
@Serializable
data class TranslationHistorySyncRequest(
    override val deviceId: String,
    override val timestamp: Long,
    override val userId: String?,
    val translations: List<TranslationDto>,
    val tags: List<TagDto>,
    val translationTags: List<TranslationTagDto>,
    val lastSyncTime: Long
) : BaseSyncRequest()

/**
 * 翻译历史同步响应
 */
@Serializable
data class TranslationHistorySyncResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val translations: List<TranslationDto> = emptyList(),
    val tags: List<TagDto> = emptyList(),
    val translationTags: List<TranslationTagDto> = emptyList(),
    val conflicts: List<SyncConflictDto> = emptyList(),
    val lastSyncTime: Long
) : BaseSyncResponse()

/**
 * 翻译数据传输对象
 */
@Serializable
data class TranslationDto(
    val id: String,
    val sourceText: String,
    val translatedText: String,
    val sourceLanguage: String,
    val targetLanguage: String,
    val sourceRegion: String? = null,
    val targetRegion: String? = null,
    val translationType: String,
    val confidence: Float,
    val isFavorite: Boolean = false,
    val timestamp: Long,
    val lastModified: Long,
    val deviceId: String,
    val version: Int = 1
)

/**
 * 标签数据传输对象
 */
@Serializable
data class TagDto(
    val id: String,
    val name: String,
    val color: String,
    val description: String? = null,
    val usageCount: Int = 0,
    val createdAt: Long,
    val updatedAt: Long,
    val lastModified: Long,
    val deviceId: String,
    val version: Int = 1
)

/**
 * 翻译标签关联数据传输对象
 */
@Serializable
data class TranslationTagDto(
    val translationId: String,
    val tagId: String,
    val createdAt: Long,
    val lastModified: Long,
    val deviceId: String
)

/**
 * 用户偏好同步请求
 */
@Serializable
data class UserPreferencesSyncRequest(
    override val deviceId: String,
    override val timestamp: Long,
    override val userId: String?,
    val preferences: UserPreferencesDto
) : BaseSyncRequest()

/**
 * 用户偏好同步响应
 */
@Serializable
data class UserPreferencesSyncResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val preferences: UserPreferencesDto? = null,
    val conflicts: List<SyncConflictDto> = emptyList()
) : BaseSyncResponse()

/**
 * 用户偏好数据传输对象
 */
@Serializable
data class UserPreferencesDto(
    val userId: String,
    val language: LanguageDto,
    val theme: String,
    val translationSettings: TranslationSettingsDto,
    val privacySettings: PrivacySettingsDto,
    val notificationSettings: NotificationSettingsDto,
    val lastModified: Long,
    val deviceId: String,
    val version: Int = 1
)

/**
 * 语言数据传输对象
 */
@Serializable
data class LanguageDto(
    val code: String,
    val name: String,
    val region: String? = null
)

/**
 * 翻译设置数据传输对象
 */
@Serializable
data class TranslationSettingsDto(
    val autoDetectLanguage: Boolean = true,
    val saveHistory: Boolean = true,
    val enableOfflineMode: Boolean = false,
    val preferredTranslationEngine: String = "gemini",
    val maxHistoryItems: Int = 1000,
    val enableRegionalTranslation: Boolean = true,
    val showConfidenceScore: Boolean = false,
    val enableVoiceInput: Boolean = true,
    val enableImageTranslation: Boolean = true
)

/**
 * 隐私设置数据传输对象
 */
@Serializable
data class PrivacySettingsDto(
    val enableDataCollection: Boolean = true,
    val enableCrashReporting: Boolean = true,
    val enableAnalytics: Boolean = true,
    val autoDeleteHistoryDays: Int? = null
)

/**
 * 通知设置数据传输对象
 */
@Serializable
data class NotificationSettingsDto(
    val enableTranslationComplete: Boolean = true,
    val enableDailyReminder: Boolean = false,
    val enableWeeklyStats: Boolean = false,
    val reminderTime: String = "09:00"
)

/**
 * 同步冲突数据传输对象
 */
@Serializable
data class SyncConflictDto(
    val itemId: String,
    val itemType: String,
    val conflictType: String,
    val localData: String, // JSON格式
    val remoteData: String, // JSON格式
    val localVersion: Int,
    val remoteVersion: Int,
    val localLastModified: Long,
    val remoteLastModified: Long
)

/**
 * 设备信息数据传输对象
 */
@Serializable
data class DeviceInfoDto(
    val deviceId: String,
    val deviceName: String,
    val platform: String = "Android",
    val appVersion: String,
    val lastActiveTime: Long,
    val isCurrentDevice: Boolean = false
)

/**
 * 设备注册请求
 */
@Serializable
data class DeviceRegistrationRequest(
    override val deviceId: String,
    override val timestamp: Long,
    override val userId: String?,
    val deviceInfo: DeviceInfoDto
) : BaseSyncRequest()

/**
 * 设备注册响应
 */
@Serializable
data class DeviceRegistrationResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val deviceId: String? = null
) : BaseSyncResponse()

/**
 * 设备列表响应
 */
@Serializable
data class DeviceListResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val devices: List<DeviceInfoDto> = emptyList()
) : BaseSyncResponse()

/**
 * 同步状态查询响应
 */
@Serializable
data class SyncStatusResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val lastSyncTime: Long,
    val syncStatus: String,
    val pendingChanges: Int = 0,
    val cloudDataSize: Long = 0L
) : BaseSyncResponse()

/**
 * 数据备份请求
 */
@Serializable
data class DataBackupRequest(
    override val deviceId: String,
    override val timestamp: Long,
    override val userId: String?,
    val backupData: BackupDataDto
) : BaseSyncRequest()

/**
 * 备份数据传输对象
 */
@Serializable
data class BackupDataDto(
    val translations: List<TranslationDto>,
    val tags: List<TagDto>,
    val translationTags: List<TranslationTagDto>,
    val preferences: UserPreferencesDto,
    val backupTime: Long,
    val appVersion: String
)

/**
 * 数据恢复响应
 */
@Serializable
data class DataRestoreResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val backupData: BackupDataDto? = null,
    val backupTime: Long? = null
) : BaseSyncResponse()

/**
 * 通用操作响应
 */
@Serializable
data class GenericSyncResponse(
    override val success: Boolean,
    override val message: String?,
    override val timestamp: Long,
    val data: String? = null
) : BaseSyncResponse()
