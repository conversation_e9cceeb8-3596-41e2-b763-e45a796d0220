package com.cadence.feature.offline

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle

/**
 * 离线状态指示器
 */
@Composable
fun OfflineStatusIndicator(
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    showDetails: Boolean = false,
    viewModel: OfflineStatusViewModel = hiltViewModel()
) {
    val networkStatus by viewModel.networkStatus.collectAsStateWithLifecycle()
    val offlineMode by viewModel.offlineMode.collectAsStateWithLifecycle()
    val featureAvailability by viewModel.featureAvailability.collectAsStateWithLifecycle()
    
    val statusInfo = getStatusInfo(networkStatus, offlineMode, featureAvailability)
    
    AnimatedVisibility(
        visible = networkStatus != NetworkStatus.CONNECTED || showDetails,
        enter = slideInVertically() + fadeIn(),
        exit = slideOutVertically() + fadeOut()
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .then(
                    if (onClick != null) {
                        Modifier.clickable { onClick() }
                    } else {
                        Modifier
                    }
                ),
            colors = CardDefaults.cardColors(
                containerColor = statusInfo.backgroundColor
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 状态图标
                Icon(
                    imageVector = statusInfo.icon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = statusInfo.iconColor
                )
                
                // 状态信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = statusInfo.title,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium,
                        color = statusInfo.textColor,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    if (statusInfo.subtitle.isNotEmpty()) {
                        Text(
                            text = statusInfo.subtitle,
                            style = MaterialTheme.typography.bodySmall,
                            color = statusInfo.textColor.copy(alpha = 0.8f),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
                
                // 详情按钮
                if (onClick != null) {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "查看详情",
                        modifier = Modifier.size(16.dp),
                        tint = statusInfo.textColor.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

/**
 * 紧凑型离线状态指示器
 */
@Composable
fun CompactOfflineStatusIndicator(
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    viewModel: OfflineStatusViewModel = hiltViewModel()
) {
    val networkStatus by viewModel.networkStatus.collectAsStateWithLifecycle()
    val offlineMode by viewModel.offlineMode.collectAsStateWithLifecycle()
    val featureAvailability by viewModel.featureAvailability.collectAsStateWithLifecycle()
    
    val statusInfo = getStatusInfo(networkStatus, offlineMode, featureAvailability)
    
    AnimatedVisibility(
        visible = networkStatus != NetworkStatus.CONNECTED,
        enter = scaleIn() + fadeIn(),
        exit = scaleOut() + fadeOut()
    ) {
        Box(
            modifier = modifier
                .clip(RoundedCornerShape(16.dp))
                .background(statusInfo.backgroundColor)
                .then(
                    if (onClick != null) {
                        Modifier.clickable { onClick() }
                    } else {
                        Modifier
                    }
                )
                .padding(horizontal = 8.dp, vertical = 4.dp),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = statusInfo.icon,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp),
                    tint = statusInfo.iconColor
                )
                
                Text(
                    text = statusInfo.compactTitle,
                    style = MaterialTheme.typography.labelSmall,
                    color = statusInfo.textColor,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

/**
 * 离线功能可用性指示器
 */
@Composable
fun OfflineFeatureAvailabilityIndicator(
    feature: OfflineFeature,
    modifier: Modifier = Modifier,
    showLabel: Boolean = true,
    viewModel: OfflineStatusViewModel = hiltViewModel()
) {
    val featureAvailability by viewModel.featureAvailability.collectAsStateWithLifecycle()
    val isAvailable = when (feature) {
        OfflineFeature.ONLINE_TRANSLATION -> featureAvailability.onlineTranslation
        OfflineFeature.OFFLINE_TRANSLATION -> featureAvailability.offlineTranslation
        OfflineFeature.LANGUAGE_PACK_DOWNLOAD -> featureAvailability.languagePackDownload
        OfflineFeature.DATA_SYNC -> featureAvailability.dataSync
        OfflineFeature.CLOUD_BACKUP -> featureAvailability.cloudBackup
        OfflineFeature.REAL_TIME_COLLABORATION -> featureAvailability.realTimeCollaboration
        OfflineFeature.VOICE_INPUT -> featureAvailability.voiceInput
        OfflineFeature.TEXT_TO_SPEECH -> featureAvailability.textToSpeech
        OfflineFeature.HISTORY_ACCESS -> featureAvailability.historyAccess
        OfflineFeature.FAVORITES_ACCESS -> featureAvailability.favoritesAccess
    }
    
    val featureInfo = getFeatureInfo(feature, isAvailable)
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = if (isAvailable) Icons.Default.CheckCircle else Icons.Default.Cancel,
            contentDescription = null,
            modifier = Modifier.size(16.dp),
            tint = if (isAvailable) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.error
            }
        )
        
        if (showLabel) {
            Text(
                text = featureInfo.name,
                style = MaterialTheme.typography.bodyMedium,
                color = if (isAvailable) {
                    MaterialTheme.colorScheme.onSurface
                } else {
                    MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                }
            )
        }
    }
}

/**
 * 网络质量指示器
 */
@Composable
fun NetworkQualityIndicator(
    modifier: Modifier = Modifier,
    showLabel: Boolean = true,
    viewModel: OfflineStatusViewModel = hiltViewModel()
) {
    val networkStatus by viewModel.networkStatus.collectAsStateWithLifecycle()
    
    val qualityInfo = when (networkStatus) {
        NetworkStatus.CONNECTED -> QualityInfo(
            icon = Icons.Default.SignalWifi4Bar,
            color = MaterialTheme.colorScheme.primary,
            label = "网络良好"
        )
        NetworkStatus.LIMITED -> QualityInfo(
            icon = Icons.Default.SignalWifi2Bar,
            color = MaterialTheme.colorScheme.tertiary,
            label = "网络受限"
        )
        NetworkStatus.DISCONNECTED -> QualityInfo(
            icon = Icons.Default.SignalWifiOff,
            color = MaterialTheme.colorScheme.error,
            label = "无网络"
        )
        NetworkStatus.UNKNOWN -> QualityInfo(
            icon = Icons.Default.SignalWifiStatusbarNull,
            color = MaterialTheme.colorScheme.outline,
            label = "检测中"
        )
    }
    
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        Icon(
            imageVector = qualityInfo.icon,
            contentDescription = null,
            modifier = Modifier.size(18.dp),
            tint = qualityInfo.color
        )
        
        if (showLabel) {
            Text(
                text = qualityInfo.label,
                style = MaterialTheme.typography.bodySmall,
                color = qualityInfo.color
            )
        }
    }
}

/**
 * 获取状态信息
 */
private fun getStatusInfo(
    networkStatus: NetworkStatus,
    offlineMode: OfflineModeState,
    featureAvailability: FeatureAvailability
): StatusInfo {
    return when (networkStatus) {
        NetworkStatus.CONNECTED -> StatusInfo(
            icon = Icons.Default.Wifi,
            iconColor = Color(0xFF4CAF50),
            backgroundColor = Color(0xFFE8F5E8),
            textColor = Color(0xFF2E7D32),
            title = "网络已连接",
            subtitle = "所有功能可用",
            compactTitle = "在线"
        )
        
        NetworkStatus.DISCONNECTED -> {
            if (offlineMode.hasOfflineCapability) {
                StatusInfo(
                    icon = Icons.Default.CloudOff,
                    iconColor = Color(0xFFFF9800),
                    backgroundColor = Color(0xFFFFF3E0),
                    textColor = Color(0xFFE65100),
                    title = "离线模式",
                    subtitle = "离线翻译可用 (${offlineMode.availableLanguagePairs}个语言对)",
                    compactTitle = "离线"
                )
            } else {
                StatusInfo(
                    icon = Icons.Default.WifiOff,
                    iconColor = Color(0xFFF44336),
                    backgroundColor = Color(0xFFFFEBEE),
                    textColor = Color(0xFFC62828),
                    title = "无网络连接",
                    subtitle = "请检查网络设置或下载语言包",
                    compactTitle = "无网络"
                )
            }
        }
        
        NetworkStatus.LIMITED -> StatusInfo(
            icon = Icons.Default.SignalWifi2Bar,
            iconColor = Color(0xFFFF9800),
            backgroundColor = Color(0xFFFFF3E0),
            textColor = Color(0xFFE65100),
            title = "网络连接受限",
            subtitle = "部分功能可能不可用",
            compactTitle = "受限"
        )
        
        NetworkStatus.UNKNOWN -> StatusInfo(
            icon = Icons.Default.HelpOutline,
            iconColor = Color(0xFF9E9E9E),
            backgroundColor = Color(0xFFF5F5F5),
            textColor = Color(0xFF424242),
            title = "检测网络状态",
            subtitle = "正在检查连接...",
            compactTitle = "检测中"
        )
    }
}

/**
 * 获取功能信息
 */
private fun getFeatureInfo(feature: OfflineFeature, isAvailable: Boolean): FeatureInfo {
    val name = when (feature) {
        OfflineFeature.ONLINE_TRANSLATION -> "在线翻译"
        OfflineFeature.OFFLINE_TRANSLATION -> "离线翻译"
        OfflineFeature.LANGUAGE_PACK_DOWNLOAD -> "语言包下载"
        OfflineFeature.DATA_SYNC -> "数据同步"
        OfflineFeature.CLOUD_BACKUP -> "云端备份"
        OfflineFeature.REAL_TIME_COLLABORATION -> "实时协作"
        OfflineFeature.VOICE_INPUT -> "语音输入"
        OfflineFeature.TEXT_TO_SPEECH -> "文本转语音"
        OfflineFeature.HISTORY_ACCESS -> "历史记录"
        OfflineFeature.FAVORITES_ACCESS -> "收藏夹"
    }
    
    return FeatureInfo(name = name, isAvailable = isAvailable)
}

/**
 * 状态信息数据类
 */
private data class StatusInfo(
    val icon: ImageVector,
    val iconColor: Color,
    val backgroundColor: Color,
    val textColor: Color,
    val title: String,
    val subtitle: String,
    val compactTitle: String
)

/**
 * 功能信息数据类
 */
private data class FeatureInfo(
    val name: String,
    val isAvailable: Boolean
)

/**
 * 网络质量信息数据类
 */
private data class QualityInfo(
    val icon: ImageVector,
    val color: Color,
    val label: String
)
