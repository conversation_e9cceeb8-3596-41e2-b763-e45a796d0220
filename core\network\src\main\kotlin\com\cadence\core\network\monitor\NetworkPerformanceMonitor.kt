package com.cadence.core.network.monitor

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络性能监控器
 * 监控API请求性能、统计数据和慢请求检测
 */
@Singleton
class NetworkPerformanceMonitor @Inject constructor() {
    
    private val monitorScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 性能统计数据
    private val _performanceStats = MutableStateFlow(NetworkPerformanceStats())
    val performanceStats: StateFlow<NetworkPerformanceStats> = _performanceStats.asStateFlow()
    
    // 请求计数器
    private val totalRequests = AtomicLong(0)
    private val successfulRequests = AtomicLong(0)
    private val failedRequests = AtomicLong(0)
    
    // 响应时间统计
    private val responseTimes = mutableListOf<Long>()
    private val slowRequests = ConcurrentHashMap<String, SlowRequestInfo>()
    
    // 配置参数
    private var slowRequestThreshold = 3000L // 3秒
    private var maxResponseTimeHistory = 100 // 保留最近100次请求的响应时间
    
    /**
     * 记录请求开始
     */
    fun recordRequestStart(url: String, method: String): RequestTracker {
        val startTime = System.currentTimeMillis()
        totalRequests.incrementAndGet()
        
        Timber.d("开始请求: $method $url")
        
        return RequestTracker(
            url = url,
            method = method,
            startTime = startTime,
            monitor = this
        )
    }
    
    /**
     * 记录请求完成
     */
    internal fun recordRequestComplete(
        tracker: RequestTracker,
        success: Boolean,
        responseCode: Int? = null,
        responseSize: Long? = null,
        errorMessage: String? = null
    ) {
        val endTime = System.currentTimeMillis()
        val duration = endTime - tracker.startTime
        
        // 更新计数器
        if (success) {
            successfulRequests.incrementAndGet()
        } else {
            failedRequests.incrementAndGet()
        }
        
        // 记录响应时间
        synchronized(responseTimes) {
            responseTimes.add(duration)
            if (responseTimes.size > maxResponseTimeHistory) {
                responseTimes.removeAt(0)
            }
        }
        
        // 检查慢请求
        if (duration > slowRequestThreshold) {
            val slowRequestInfo = SlowRequestInfo(
                url = tracker.url,
                method = tracker.method,
                duration = duration,
                timestamp = endTime,
                responseCode = responseCode,
                errorMessage = errorMessage
            )
            slowRequests[tracker.url] = slowRequestInfo
            Timber.w("检测到慢请求: ${tracker.method} ${tracker.url} - ${duration}ms")
        }
        
        // 更新统计数据
        updatePerformanceStats()
        
        val status = if (success) "成功" else "失败"
        Timber.d("请求完成: ${tracker.method} ${tracker.url} - $status (${duration}ms)")
    }
    
    /**
     * 更新性能统计数据
     */
    private fun updatePerformanceStats() {
        monitorScope.launch {
            val total = totalRequests.get()
            val successful = successfulRequests.get()
            val failed = failedRequests.get()
            
            val successRate = if (total > 0) (successful.toDouble() / total * 100) else 0.0
            
            val avgResponseTime = synchronized(responseTimes) {
                if (responseTimes.isNotEmpty()) {
                    responseTimes.average()
                } else {
                    0.0
                }
            }
            
            val p95ResponseTime = synchronized(responseTimes) {
                if (responseTimes.isNotEmpty()) {
                    val sorted = responseTimes.sorted()
                    val index = (sorted.size * 0.95).toInt()
                    sorted.getOrNull(index)?.toDouble() ?: 0.0
                } else {
                    0.0
                }
            }
            
            val stats = NetworkPerformanceStats(
                totalRequests = total,
                successfulRequests = successful,
                failedRequests = failed,
                successRate = successRate,
                averageResponseTime = avgResponseTime,
                p95ResponseTime = p95ResponseTime,
                slowRequestCount = slowRequests.size,
                lastUpdated = System.currentTimeMillis()
            )
            
            _performanceStats.value = stats
        }
    }
    
    /**
     * 获取慢请求列表
     */
    fun getSlowRequests(): List<SlowRequestInfo> {
        return slowRequests.values.sortedByDescending { it.timestamp }
    }
    
    /**
     * 清除慢请求记录
     */
    fun clearSlowRequests() {
        slowRequests.clear()
        updatePerformanceStats()
        Timber.d("已清除慢请求记录")
    }
    
    /**
     * 重置所有统计数据
     */
    fun resetStats() {
        totalRequests.set(0)
        successfulRequests.set(0)
        failedRequests.set(0)
        synchronized(responseTimes) {
            responseTimes.clear()
        }
        slowRequests.clear()
        updatePerformanceStats()
        Timber.d("已重置网络性能统计数据")
    }
    
    /**
     * 配置慢请求阈值
     */
    fun configureSlowRequestThreshold(thresholdMs: Long) {
        slowRequestThreshold = thresholdMs
        Timber.d("慢请求阈值已设置为: ${thresholdMs}ms")
    }
    
    /**
     * 配置响应时间历史记录数量
     */
    fun configureResponseTimeHistory(maxHistory: Int) {
        maxResponseTimeHistory = maxHistory
        synchronized(responseTimes) {
            while (responseTimes.size > maxHistory) {
                responseTimes.removeAt(0)
            }
        }
        Timber.d("响应时间历史记录数量已设置为: $maxHistory")
    }
    
    /**
     * 获取网络健康状态
     */
    fun getNetworkHealth(): NetworkHealth {
        val stats = _performanceStats.value
        
        return when {
            stats.successRate >= 95.0 && stats.averageResponseTime < 1000 -> NetworkHealth.EXCELLENT
            stats.successRate >= 90.0 && stats.averageResponseTime < 2000 -> NetworkHealth.GOOD
            stats.successRate >= 80.0 && stats.averageResponseTime < 3000 -> NetworkHealth.FAIR
            stats.successRate >= 70.0 -> NetworkHealth.POOR
            else -> NetworkHealth.CRITICAL
        }
    }
}

/**
 * 请求跟踪器
 */
class RequestTracker internal constructor(
    val url: String,
    val method: String,
    val startTime: Long,
    private val monitor: NetworkPerformanceMonitor
) {
    
    /**
     * 标记请求成功
     */
    fun markSuccess(responseCode: Int = 200, responseSize: Long? = null) {
        monitor.recordRequestComplete(
            tracker = this,
            success = true,
            responseCode = responseCode,
            responseSize = responseSize
        )
    }
    
    /**
     * 标记请求失败
     */
    fun markFailure(responseCode: Int? = null, errorMessage: String? = null) {
        monitor.recordRequestComplete(
            tracker = this,
            success = false,
            responseCode = responseCode,
            errorMessage = errorMessage
        )
    }
}

/**
 * 网络性能统计数据
 */
data class NetworkPerformanceStats(
    val totalRequests: Long = 0,
    val successfulRequests: Long = 0,
    val failedRequests: Long = 0,
    val successRate: Double = 0.0,
    val averageResponseTime: Double = 0.0,
    val p95ResponseTime: Double = 0.0,
    val slowRequestCount: Int = 0,
    val lastUpdated: Long = System.currentTimeMillis()
)

/**
 * 慢请求信息
 */
data class SlowRequestInfo(
    val url: String,
    val method: String,
    val duration: Long,
    val timestamp: Long,
    val responseCode: Int? = null,
    val errorMessage: String? = null
)

/**
 * 网络健康状态
 */
enum class NetworkHealth {
    EXCELLENT,  // 优秀
    GOOD,       // 良好
    FAIR,       // 一般
    POOR,       // 较差
    CRITICAL    // 严重
}

/**
 * 网络健康状态扩展函数
 */
fun NetworkHealth.getDisplayName(): String = when (this) {
    NetworkHealth.EXCELLENT -> "网络状态优秀"
    NetworkHealth.GOOD -> "网络状态良好"
    NetworkHealth.FAIR -> "网络状态一般"
    NetworkHealth.POOR -> "网络状态较差"
    NetworkHealth.CRITICAL -> "网络状态严重"
}

fun NetworkHealth.getColor(): String = when (this) {
    NetworkHealth.EXCELLENT -> "#4CAF50" // 绿色
    NetworkHealth.GOOD -> "#8BC34A"      // 浅绿色
    NetworkHealth.FAIR -> "#FFC107"      // 黄色
    NetworkHealth.POOR -> "#FF9800"      // 橙色
    NetworkHealth.CRITICAL -> "#F44336"  // 红色
}
