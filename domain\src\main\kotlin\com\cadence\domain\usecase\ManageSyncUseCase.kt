package com.cadence.domain.usecase

import com.cadence.domain.model.*
import com.cadence.domain.repository.SyncRepository
import com.cadence.domain.repository.TranslationRepository
import com.cadence.domain.repository.TagRepository
import com.cadence.domain.repository.UserPreferenceRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import timber.log.Timber
import javax.inject.Inject

/**
 * 同步管理用例
 * 处理数据同步相关的业务逻辑
 */
class ManageSyncUseCase @Inject constructor(
    private val syncRepository: SyncRepository,
    private val translationRepository: TranslationRepository,
    private val tagRepository: TagRepository,
    private val userPreferenceRepository: UserPreferenceRepository
) {
    
    /**
     * 获取同步配置
     * @return 同步配置流
     */
    fun getSyncConfig(): Flow<SyncConfig> {
        return syncRepository.getSyncConfig()
    }
    
    /**
     * 更新同步配置
     * @param config 新的同步配置
     * @return 操作结果
     */
    suspend fun updateSyncConfig(config: SyncConfig): Result<Unit> {
        return try {
            val result = syncRepository.updateSyncConfig(config)
            
            if (result.isSuccess) {
                Timber.d("更新同步配置成功")
            } else {
                Timber.e("更新同步配置失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新同步配置过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取同步状态
     * @return 同步状态流
     */
    fun getSyncStatus(): Flow<SyncStatus> {
        return syncRepository.getSyncStatus()
    }
    
    /**
     * 执行完整同步
     * @param syncType 同步类型
     * @param forceSync 是否强制同步
     * @return 同步结果
     */
    suspend fun performSync(
        syncType: SyncType = SyncType.ALL,
        forceSync: Boolean = false
    ): Result<SyncResult> {
        return try {
            // 检查是否可以同步
            if (!syncRepository.canSync()) {
                return Result.failure(IllegalStateException("当前网络状态不支持同步"))
            }
            
            // 检查同步配置
            val config = syncRepository.getSyncConfig().first()
            if (!config.isEnabled) {
                return Result.failure(IllegalStateException("同步功能未启用"))
            }
            
            // 检查是否需要同步
            if (!forceSync && !shouldSync(config)) {
                Timber.d("距离上次同步时间未达到间隔，跳过同步")
                return Result.success(
                    SyncResult(
                        syncType = syncType,
                        status = SyncStatus.SUCCESS,
                        startTime = System.currentTimeMillis(),
                        endTime = System.currentTimeMillis()
                    )
                )
            }
            
            val result = syncRepository.performSync(syncType, forceSync)
            
            if (result.isSuccess) {
                val syncResult = result.getOrThrow()
                Timber.d("同步完成: ${syncResult.status}, 上传 ${syncResult.uploadedCount} 项, 下载 ${syncResult.downloadedCount} 项")
            } else {
                Timber.e("同步失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "同步过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 同步翻译历史
     * @return 同步结果
     */
    suspend fun syncTranslationHistory(): Result<SyncResult> {
        return try {
            // 获取本地翻译历史
            val localTranslations = translationRepository.getTranslationHistory(
                TranslationHistoryQuery(limit = Int.MAX_VALUE)
            ).first()
            
            // 上传到云端
            val uploadResult = syncRepository.uploadTranslationHistory(localTranslations)
            if (uploadResult.isFailure) {
                return uploadResult
            }
            
            // 从云端下载
            val config = syncRepository.getSyncConfig().first()
            val downloadResult = syncRepository.downloadTranslationHistory(config.lastSyncTime)
            if (downloadResult.isFailure) {
                return Result.failure(downloadResult.exceptionOrNull()!!)
            }
            
            // 合并数据
            val syncData = downloadResult.getOrThrow()
            val mergeResult = mergeTranslationData(syncData)
            
            if (mergeResult.isSuccess) {
                Timber.d("翻译历史同步成功")
            } else {
                Timber.e("翻译历史同步失败: ${mergeResult.exceptionOrNull()?.message}")
            }
            
            mergeResult
        } catch (e: Exception) {
            Timber.e(e, "同步翻译历史过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 同步标签数据
     * @return 同步结果
     */
    suspend fun syncTags(): Result<SyncResult> {
        return try {
            // 获取本地标签
            val localTags = tagRepository.getAllTags().first()
            
            // 上传到云端
            val uploadResult = syncRepository.uploadTags(localTags)
            if (uploadResult.isFailure) {
                return uploadResult
            }
            
            // 从云端下载
            val config = syncRepository.getSyncConfig().first()
            val downloadResult = syncRepository.downloadTags(config.lastSyncTime)
            if (downloadResult.isFailure) {
                return Result.failure(downloadResult.exceptionOrNull()!!)
            }
            
            // 合并标签数据
            val remoteTags = downloadResult.getOrThrow()
            val mergeResult = mergeTagData(remoteTags)
            
            if (mergeResult.isSuccess) {
                Timber.d("标签数据同步成功")
            } else {
                Timber.e("标签数据同步失败: ${mergeResult.exceptionOrNull()?.message}")
            }
            
            mergeResult
        } catch (e: Exception) {
            Timber.e(e, "同步标签数据过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 同步用户偏好
     * @return 同步结果
     */
    suspend fun syncUserPreferences(): Result<SyncResult> {
        return try {
            // 获取本地用户偏好
            val localPreferences = userPreferenceRepository.getUserPreference().first()
            
            // 上传到云端
            val uploadResult = syncRepository.uploadUserPreferences(localPreferences)
            if (uploadResult.isFailure) {
                return uploadResult
            }
            
            // 从云端下载
            val downloadResult = syncRepository.downloadUserPreferences()
            if (downloadResult.isFailure) {
                return Result.failure(downloadResult.exceptionOrNull()!!)
            }
            
            // 合并用户偏好数据
            val remotePreferences = downloadResult.getOrThrow()
            val mergeResult = mergeUserPreferences(remotePreferences)
            
            if (mergeResult.isSuccess) {
                Timber.d("用户偏好同步成功")
            } else {
                Timber.e("用户偏好同步失败: ${mergeResult.exceptionOrNull()?.message}")
            }
            
            mergeResult
        } catch (e: Exception) {
            Timber.e(e, "同步用户偏好过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取同步历史
     * @param limit 限制数量
     * @return 同步历史流
     */
    fun getSyncHistory(limit: Int = 50): Flow<List<SyncResult>> {
        return syncRepository.getSyncHistory(limit)
    }
    
    /**
     * 获取同步统计信息
     * @return 统计信息
     */
    suspend fun getSyncStatistics(): Result<SyncStatistics> {
        return try {
            val result = syncRepository.getSyncStatistics()
            
            if (result.isSuccess) {
                Timber.d("获取同步统计信息成功")
            } else {
                Timber.e("获取同步统计信息失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "获取同步统计信息过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 备份所有数据
     * @return 备份结果
     */
    suspend fun backupAllData(): Result<SyncResult> {
        return try {
            val result = syncRepository.backupAllData()
            
            if (result.isSuccess) {
                Timber.d("数据备份成功")
            } else {
                Timber.e("数据备份失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "数据备份过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 恢复所有数据
     * @param replaceLocal 是否替换本地数据
     * @return 恢复结果
     */
    suspend fun restoreAllData(replaceLocal: Boolean = false): Result<SyncResult> {
        return try {
            val result = syncRepository.restoreAllData(replaceLocal)
            
            if (result.isSuccess) {
                Timber.d("数据恢复成功")
            } else {
                Timber.e("数据恢复失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "数据恢复过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 检查是否需要同步
     */
    private fun shouldSync(config: SyncConfig): Boolean {
        val currentTime = System.currentTimeMillis()
        return currentTime - config.lastSyncTime >= config.syncInterval
    }
    
    /**
     * 合并翻译数据
     */
    private suspend fun mergeTranslationData(syncData: TranslationSyncData): Result<SyncResult> {
        return try {
            var uploadedCount = 0
            var downloadedCount = 0
            
            // 保存远程翻译记录
            syncData.translations.forEach { translation ->
                val saveResult = translationRepository.saveTranslation(translation)
                if (saveResult.isSuccess) {
                    downloadedCount++
                }
            }
            
            // 保存远程标签
            syncData.tags.forEach { tag ->
                val createResult = tagRepository.createTag(
                    CreateTagRequest(
                        name = tag.name,
                        color = tag.color,
                        description = tag.description
                    )
                )
                if (createResult.isSuccess) {
                    downloadedCount++
                }
            }
            
            // 保存标签关联关系
            syncData.translationTags.forEach { relation ->
                val addResult = tagRepository.addTagToTranslation(
                    relation.translationId,
                    relation.tagId
                )
                if (addResult.isSuccess) {
                    downloadedCount++
                }
            }
            
            Result.success(
                SyncResult(
                    syncType = SyncType.TRANSLATION_HISTORY,
                    status = SyncStatus.SUCCESS,
                    startTime = System.currentTimeMillis(),
                    endTime = System.currentTimeMillis(),
                    uploadedCount = uploadedCount,
                    downloadedCount = downloadedCount
                )
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 合并标签数据
     */
    private suspend fun mergeTagData(remoteTags: List<Tag>): Result<SyncResult> {
        return try {
            var downloadedCount = 0
            
            remoteTags.forEach { tag ->
                val createResult = tagRepository.createTag(
                    CreateTagRequest(
                        name = tag.name,
                        color = tag.color,
                        description = tag.description
                    )
                )
                if (createResult.isSuccess) {
                    downloadedCount++
                }
            }
            
            Result.success(
                SyncResult(
                    syncType = SyncType.TAGS,
                    status = SyncStatus.SUCCESS,
                    startTime = System.currentTimeMillis(),
                    endTime = System.currentTimeMillis(),
                    downloadedCount = downloadedCount
                )
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 合并用户偏好数据
     */
    private suspend fun mergeUserPreferences(remotePreferences: UserPreference): Result<SyncResult> {
        return try {
            val updateResult = userPreferenceRepository.updateUserPreference(remotePreferences)
            
            if (updateResult.isSuccess) {
                Result.success(
                    SyncResult(
                        syncType = SyncType.USER_PREFERENCES,
                        status = SyncStatus.SUCCESS,
                        startTime = System.currentTimeMillis(),
                        endTime = System.currentTimeMillis(),
                        downloadedCount = 1
                    )
                )
            } else {
                Result.failure(updateResult.exceptionOrNull()!!)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
