package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.domain.model.learning.LearningProgress
import com.cadence.domain.model.learning.MasteryLevel

/**
 * 掌握程度卡片组件
 * 显示特定掌握程度的单词列表
 */
@Composable
fun MasteryLevelCard(
    masteryLevel: MasteryLevel,
    progressList: List<LearningProgress>,
    onWordClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = getMasteryIcon(masteryLevel),
                        contentDescription = masteryLevel.displayName,
                        modifier = Modifier.size(24.dp),
                        tint = getMasteryColor(masteryLevel)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = masteryLevel.displayName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                // 数量徽章
                Badge(
                    containerColor = getMasteryColor(masteryLevel).copy(alpha = 0.2f),
                    contentColor = getMasteryColor(masteryLevel)
                ) {
                    Text(
                        text = progressList.size.toString(),
                        style = MaterialTheme.typography.labelMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
            
            if (progressList.isEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "暂无${masteryLevel.displayName}的单词",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            } else {
                Spacer(modifier = Modifier.height(12.dp))
                
                // 单词进度列表
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(progressList.take(10)) { progress -> // 最多显示10个
                        WordProgressChip(
                            progress = progress,
                            onClick = { onWordClick(progress.wordId) }
                        )
                    }
                    
                    if (progressList.size > 10) {
                        item {
                            MoreWordsChip(
                                count = progressList.size - 10,
                                onClick = { /* 导航到完整列表 */ }
                            )
                        }
                    }
                }
                
                // 统计信息
                if (progressList.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    val avgAccuracy = progressList.map { it.accuracyRate }.average()
                    val totalAttempts = progressList.sumOf { it.totalAttempts }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "平均正确率: ${(avgAccuracy * 100).toInt()}%",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Text(
                            text = "总练习次数: $totalAttempts",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

/**
 * 单词进度芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WordProgressChip(
    progress: LearningProgress,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AssistChip(
        onClick = onClick,
        label = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "单词${progress.wordId.take(4)}...", // 简化显示
                    style = MaterialTheme.typography.labelSmall
                )
                Text(
                    text = "${(progress.accuracyRate * 100).toInt()}%",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        modifier = modifier,
        colors = AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    )
}

/**
 * 更多单词芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MoreWordsChip(
    count: Int,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AssistChip(
        onClick = onClick,
        label = {
            Text(
                text = "+$count",
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
        },
        modifier = modifier,
        colors = AssistChipDefaults.assistChipColors(
            containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
            labelColor = MaterialTheme.colorScheme.primary
        )
    )
}

/**
 * 获取掌握程度对应的图标
 */
private fun getMasteryIcon(masteryLevel: MasteryLevel): ImageVector {
    return when (masteryLevel) {
        MasteryLevel.NEW -> Icons.Default.FiberNew
        MasteryLevel.LEARNING -> Icons.Default.School
        MasteryLevel.FAMILIAR -> Icons.Default.ThumbUp
        MasteryLevel.KNOWN -> Icons.Default.CheckCircle
        MasteryLevel.MASTERED -> Icons.Default.Star
    }
}

/**
 * 获取掌握程度对应的颜色
 */
@Composable
private fun getMasteryColor(masteryLevel: MasteryLevel): androidx.compose.ui.graphics.Color {
    return when (masteryLevel) {
        MasteryLevel.NEW -> MaterialTheme.colorScheme.outline
        MasteryLevel.LEARNING -> MaterialTheme.colorScheme.primary
        MasteryLevel.FAMILIAR -> MaterialTheme.colorScheme.secondary
        MasteryLevel.KNOWN -> MaterialTheme.colorScheme.tertiary
        MasteryLevel.MASTERED -> MaterialTheme.colorScheme.primary
    }
}