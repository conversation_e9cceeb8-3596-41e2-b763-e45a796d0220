package com.cadence.core.security

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 隐私管理器
 * 负责用户隐私保护，包括数据收集同意、数据清理等
 */
@Singleton
class PrivacyManager @Inject constructor(
    private val context: Context,
    private val secureStorageManager: SecureStorageManager,
    private val auditLogger: SecurityAuditLogger
) {
    
    companion object {
        private const val PRIVACY_CONSENT_KEY = "privacy_consent"
        private const val ANALYTICS_CONSENT_KEY = "analytics_consent"
        private const val CRASH_REPORTING_CONSENT_KEY = "crash_reporting_consent"
        private const val PERSONALIZATION_CONSENT_KEY = "personalization_consent"
        private const val MARKETING_CONSENT_KEY = "marketing_consent"
        private const val CONSENT_VERSION_KEY = "consent_version"
        private const val CONSENT_TIMESTAMP_KEY = "consent_timestamp"
        
        private const val CURRENT_CONSENT_VERSION = 1
        private const val DATA_RETENTION_DAYS = 365 // 1年数据保留期
    }
    
    /**
     * 检查是否已获得隐私同意
     */
    fun hasPrivacyConsent(): Boolean {
        val consentVersion = secureStorageManager.getUserPreference(CONSENT_VERSION_KEY, 0)
        val hasConsent = secureStorageManager.getUserPreference(PRIVACY_CONSENT_KEY, false)
        
        return hasConsent && consentVersion >= CURRENT_CONSENT_VERSION
    }
    
    /**
     * 设置隐私同意
     */
    fun setPrivacyConsent(consent: PrivacyConsent) {
        secureStorageManager.storeUserPreference(PRIVACY_CONSENT_KEY, consent.generalConsent)
        secureStorageManager.storeUserPreference(ANALYTICS_CONSENT_KEY, consent.analyticsConsent)
        secureStorageManager.storeUserPreference(CRASH_REPORTING_CONSENT_KEY, consent.crashReportingConsent)
        secureStorageManager.storeUserPreference(PERSONALIZATION_CONSENT_KEY, consent.personalizationConsent)
        secureStorageManager.storeUserPreference(MARKETING_CONSENT_KEY, consent.marketingConsent)
        secureStorageManager.storeUserPreference(CONSENT_VERSION_KEY, CURRENT_CONSENT_VERSION)
        secureStorageManager.storeUserPreference(CONSENT_TIMESTAMP_KEY, System.currentTimeMillis())
        
        // 记录隐私同意事件
        auditLogger.logPrivacyEvent(
            event = "privacy_consent_updated",
            details = mapOf(
                "general" to consent.generalConsent.toString(),
                "analytics" to consent.analyticsConsent.toString(),
                "crash_reporting" to consent.crashReportingConsent.toString(),
                "personalization" to consent.personalizationConsent.toString(),
                "marketing" to consent.marketingConsent.toString(),
                "version" to CURRENT_CONSENT_VERSION.toString()
            )
        )
        
        Timber.i("隐私同意已更新")
    }
    
    /**
     * 获取当前隐私同意状态
     */
    fun getPrivacyConsent(): PrivacyConsent {
        return PrivacyConsent(
            generalConsent = secureStorageManager.getUserPreference(PRIVACY_CONSENT_KEY, false),
            analyticsConsent = secureStorageManager.getUserPreference(ANALYTICS_CONSENT_KEY, false),
            crashReportingConsent = secureStorageManager.getUserPreference(CRASH_REPORTING_CONSENT_KEY, false),
            personalizationConsent = secureStorageManager.getUserPreference(PERSONALIZATION_CONSENT_KEY, false),
            marketingConsent = secureStorageManager.getUserPreference(MARKETING_CONSENT_KEY, false),
            consentVersion = secureStorageManager.getUserPreference(CONSENT_VERSION_KEY, 0),
            consentTimestamp = secureStorageManager.getUserPreference(CONSENT_TIMESTAMP_KEY, 0L)
        )
    }
    
    /**
     * 撤销隐私同意
     */
    suspend fun revokePrivacyConsent() = withContext(Dispatchers.IO) {
        // 记录撤销事件
        auditLogger.logPrivacyEvent(
            event = "privacy_consent_revoked",
            details = mapOf("timestamp" to System.currentTimeMillis().toString())
        )
        
        // 清除所有用户数据
        clearAllUserData()
        
        // 重置同意状态
        secureStorageManager.storeUserPreference(PRIVACY_CONSENT_KEY, false)
        secureStorageManager.storeUserPreference(ANALYTICS_CONSENT_KEY, false)
        secureStorageManager.storeUserPreference(CRASH_REPORTING_CONSENT_KEY, false)
        secureStorageManager.storeUserPreference(PERSONALIZATION_CONSENT_KEY, false)
        secureStorageManager.storeUserPreference(MARKETING_CONSENT_KEY, false)
        
        Timber.i("隐私同意已撤销，用户数据已清除")
    }
    
    /**
     * 检查是否可以收集分析数据
     */
    fun canCollectAnalytics(): Boolean {
        return hasPrivacyConsent() && 
               secureStorageManager.getUserPreference(ANALYTICS_CONSENT_KEY, false)
    }
    
    /**
     * 检查是否可以收集崩溃报告
     */
    fun canCollectCrashReports(): Boolean {
        return hasPrivacyConsent() && 
               secureStorageManager.getUserPreference(CRASH_REPORTING_CONSENT_KEY, false)
    }
    
    /**
     * 检查是否可以进行个性化
     */
    fun canPersonalize(): Boolean {
        return hasPrivacyConsent() && 
               secureStorageManager.getUserPreference(PERSONALIZATION_CONSENT_KEY, false)
    }
    
    /**
     * 检查是否可以发送营销信息
     */
    fun canSendMarketing(): Boolean {
        return hasPrivacyConsent() && 
               secureStorageManager.getUserPreference(MARKETING_CONSENT_KEY, false)
    }
    
    /**
     * 数据匿名化处理
     */
    suspend fun anonymizeData(data: String): String = withContext(Dispatchers.Default) {
        var anonymizedData = data
        
        // 移除或替换个人标识信息
        anonymizedData = anonymizedData.replace(Regex("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b"), "[EMAIL]")
        anonymizedData = anonymizedData.replace(Regex("\\b\\d{3}-\\d{3}-\\d{4}\\b"), "[PHONE]")
        anonymizedData = anonymizedData.replace(Regex("\\b\\d{4}\\s?\\d{4}\\s?\\d{4}\\s?\\d{4}\\b"), "[CARD]")
        
        // 生成数据哈希用于统计分析
        val dataHash = java.security.MessageDigest.getInstance("SHA-256")
            .digest(data.toByteArray())
            .joinToString("") { "%02x".format(it) }
        
        auditLogger.logPrivacyEvent(
            event = "data_anonymized",
            details = mapOf(
                "original_hash" to dataHash,
                "anonymized_length" to anonymizedData.length.toString()
            )
        )
        
        anonymizedData
    }
    
    /**
     * 清除过期数据
     */
    suspend fun clearExpiredData() = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        val retentionPeriod = DATA_RETENTION_DAYS * 24 * 60 * 60 * 1000L
        
        // 这里应该实现具体的数据清理逻辑
        // 例如：清理过期的翻译历史、缓存等
        
        auditLogger.logPrivacyEvent(
            event = "expired_data_cleared",
            details = mapOf(
                "retention_days" to DATA_RETENTION_DAYS.toString(),
                "cleanup_timestamp" to currentTime.toString()
            )
        )
        
        Timber.i("过期数据清理完成")
    }
    
    /**
     * 清除所有用户数据
     */
    suspend fun clearAllUserData() = withContext(Dispatchers.IO) {
        // 清除安全存储中的所有数据
        secureStorageManager.clearAllUserData()
        
        // 清除应用缓存
        clearApplicationCache()
        
        // 清除数据库中的用户数据
        // 这里应该调用数据库清理方法
        
        auditLogger.logPrivacyEvent(
            event = "all_user_data_cleared",
            details = mapOf("timestamp" to System.currentTimeMillis().toString())
        )
        
        Timber.i("所有用户数据已清除")
    }
    
    /**
     * 清除应用缓存
     */
    private fun clearApplicationCache() {
        try {
            val cacheDir = context.cacheDir
            cacheDir.deleteRecursively()
            
            val externalCacheDir = context.externalCacheDir
            externalCacheDir?.deleteRecursively()
            
            Timber.d("应用缓存已清除")
        } catch (e: Exception) {
            Timber.e(e, "清除应用缓存失败")
        }
    }
    
    /**
     * 生成隐私报告
     */
    fun generatePrivacyReport(): PrivacyReport {
        val consent = getPrivacyConsent()
        val storageUsage = secureStorageManager.getStorageUsage()
        
        return PrivacyReport(
            consentStatus = consent,
            dataCollectionEnabled = canCollectAnalytics(),
            crashReportingEnabled = canCollectCrashReports(),
            personalizationEnabled = canPersonalize(),
            marketingEnabled = canSendMarketing(),
            storageUsage = storageUsage,
            lastDataCleanup = getLastDataCleanupTime(),
            dataRetentionDays = DATA_RETENTION_DAYS
        )
    }
    
    /**
     * 获取最后数据清理时间
     */
    private fun getLastDataCleanupTime(): Long {
        return secureStorageManager.getUserPreference("last_data_cleanup", 0L)
    }
    
    /**
     * 设置最后数据清理时间
     */
    fun setLastDataCleanupTime(timestamp: Long) {
        secureStorageManager.storeUserPreference("last_data_cleanup", timestamp)
    }
    
    /**
     * 检查是否需要更新隐私政策同意
     */
    fun needsConsentUpdate(): Boolean {
        val currentVersion = secureStorageManager.getUserPreference(CONSENT_VERSION_KEY, 0)
        return currentVersion < CURRENT_CONSENT_VERSION
    }
    
    /**
     * 获取隐私政策URL
     */
    fun getPrivacyPolicyUrl(): String {
        return "https://cadence.app/privacy-policy"
    }
    
    /**
     * 获取使用条款URL
     */
    fun getTermsOfServiceUrl(): String {
        return "https://cadence.app/terms-of-service"
    }
}

/**
 * 隐私同意数据类
 */
data class PrivacyConsent(
    val generalConsent: Boolean,
    val analyticsConsent: Boolean,
    val crashReportingConsent: Boolean,
    val personalizationConsent: Boolean,
    val marketingConsent: Boolean,
    val consentVersion: Int,
    val consentTimestamp: Long
)

/**
 * 隐私报告数据类
 */
data class PrivacyReport(
    val consentStatus: PrivacyConsent,
    val dataCollectionEnabled: Boolean,
    val crashReportingEnabled: Boolean,
    val personalizationEnabled: Boolean,
    val marketingEnabled: Boolean,
    val storageUsage: StorageUsage,
    val lastDataCleanup: Long,
    val dataRetentionDays: Int
)
