package com.cadence.core.speech.di

import android.content.Context
import com.cadence.core.speech.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.serialization.json.Json
import javax.inject.Singleton

/**
 * 语音功能依赖注入模块
 * 提供语音识别和文字转语音服务的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object SpeechModule {

    /**
     * 提供JSON序列化器
     */
    @Provides
    @Singleton
    fun provideJson(): Json {
        return Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
            prettyPrint = false
        }
    }

    /**
     * 提供语音缓存管理器
     */
    @Provides
    @Singleton
    fun provideSpeechCacheManager(
        @ApplicationContext context: Context,
        json: Json
    ): SpeechCacheManager {
        return SpeechCacheManager(context, json)
    }

    /**
     * 提供音频处理器
     */
    @Provides
    @Singleton
    fun provideAudioProcessor(
        @ApplicationContext context: Context
    ): AudioProcessor {
        return AudioProcessor(context)
    }

    /**
     * 提供离线语音引擎
     */
    @Provides
    @Singleton
    fun provideOfflineSpeechEngine(
        @ApplicationContext context: Context,
        speechCacheManager: SpeechCacheManager
    ): OfflineSpeechEngine {
        return OfflineSpeechEngine(context, speechCacheManager)
    }

    /**
     * 提供语音配置管理器
     */
    @Provides
    @Singleton
    fun provideSpeechConfigManager(
        @ApplicationContext context: Context,
        json: Json
    ): SpeechConfigManager {
        return SpeechConfigManager(context, json)
    }

    /**
     * 提供语音识别服务
     */
    @Provides
    @Singleton
    fun provideSpeechRecognitionService(
        @ApplicationContext context: Context
    ): SpeechRecognitionService {
        return SpeechRecognitionService(context)
    }

    /**
     * 提供文字转语音服务
     */
    @Provides
    @Singleton
    fun provideTextToSpeechService(
        @ApplicationContext context: Context
    ): TextToSpeechService {
        return TextToSpeechService(context)
    }
}
