package com.cadence.domain.repository

import com.cadence.domain.model.learning.*
import kotlinx.coroutines.flow.Flow

/**
 * 学习功能Repository接口
 * 定义学习相关的数据操作
 */
interface LearningRepository {
    
    // 单词管理
    suspend fun addWord(word: Word)
    suspend fun getWordById(id: String): Word?
    suspend fun getAllWords(): Flow<List<Word>>
    suspend fun getWordsByLanguage(language: String): Flow<List<Word>>
    suspend fun getWordsByCategory(category: WordCategory): Flow<List<Word>>
    suspend fun searchWords(query: String): Flow<List<Word>>
    suspend fun getRandomWords(limit: Int): List<Word>
    suspend fun updateWord(word: Word)
    suspend fun deleteWord(wordId: String)
    
    // 学习进度管理
    suspend fun getLearningProgress(userId: String, wordId: String): LearningProgress?
    suspend fun getUserLearningProgress(userId: String): Flow<List<LearningProgress>>
    suspend fun updateLearningProgress(progress: LearningProgress)
    suspend fun getWordsToReview(userId: String): List<Word>
    suspend fun getNewWords(userId: String, limit: Int): List<Word>
    suspend fun getBookmarkedWords(userId: String): List<Word>
    suspend fun toggleWordBookmark(userId: String, wordId: String)
    
    // 学习会话管理
    suspend fun startStudySession(userId: String, wordIds: List<String>, sessionType: SessionType): StudySession
    suspend fun updateStudySession(session: StudySession)
    suspend fun completeStudySession(sessionId: String, correctAnswers: Int, totalQuestions: Int)
    suspend fun getUserStudySessions(userId: String): Flow<List<StudySession>>
    suspend fun getRecentStudySessions(userId: String, limit: Int): List<StudySession>
    
    // 学习统计
    suspend fun getLearningStatistics(userId: String): LearningStatistics
    suspend fun updateLearningStatistics(userId: String)
    suspend fun getDailyProgress(userId: String, days: Int): List<DailyProgress>
    suspend fun getCategoryProgress(userId: String): Map<WordCategory, com.cadence.feature.learning.presentation.component.CategoryProgress>
    
    // 复习提醒
    suspend fun scheduleReview(userId: String, wordId: String, masteryLevel: MasteryLevel)
    suspend fun getScheduledReviews(userId: String): List<LearningProgress>
    suspend fun markReviewCompleted(userId: String, wordId: String, success: Boolean)
}