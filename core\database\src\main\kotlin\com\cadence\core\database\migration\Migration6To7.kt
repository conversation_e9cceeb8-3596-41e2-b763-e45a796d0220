package com.cadence.core.database.migration

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 数据库迁移：版本6到版本7
 * 添加文化背景相关表
 */
val MIGRATION_6_7 = object : Migration(6, 7) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // 创建文化背景上下文表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS cultural_contexts (
                id TEXT NOT NULL PRIMARY KEY,
                word TEXT NOT NULL,
                source_language TEXT NOT NULL,
                target_language TEXT NOT NULL,
                region TEXT NOT NULL,
                cultural_meaning TEXT NOT NULL,
                historical_background TEXT NOT NULL,
                related_concepts TEXT NOT NULL,
                tags TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                created_at INTEGER NOT NULL,
                updated_at INTEGER NOT NULL
            )
        """)
        
        // 创建使用场景表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS usage_contexts (
                id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                context_id TEXT NOT NULL,
                context TEXT NOT NULL,
                description TEXT NOT NULL,
                appropriateness TEXT NOT NULL,
                examples TEXT NOT NULL,
                FOREIGN KEY(context_id) REFERENCES cultural_contexts(id) ON DELETE CASCADE
            )
        """)
        
        // 创建地域差异表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS regional_differences (
                id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                context_id TEXT NOT NULL,
                region TEXT NOT NULL,
                difference TEXT NOT NULL,
                explanation TEXT NOT NULL,
                alternative_expressions TEXT NOT NULL,
                FOREIGN KEY(context_id) REFERENCES cultural_contexts(id) ON DELETE CASCADE
            )
        """)
        
        // 创建文化示例表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS cultural_examples (
                id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                context_id TEXT NOT NULL,
                scenario TEXT NOT NULL,
                original_text TEXT NOT NULL,
                translated_text TEXT NOT NULL,
                cultural_note TEXT NOT NULL,
                appropriate_usage INTEGER NOT NULL,
                FOREIGN KEY(context_id) REFERENCES cultural_contexts(id) ON DELETE CASCADE
            )
        """)
        
        // 创建文化推荐表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS cultural_recommendations (
                id TEXT NOT NULL PRIMARY KEY,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                type TEXT NOT NULL,
                related_words TEXT NOT NULL,
                content TEXT NOT NULL,
                difficulty TEXT NOT NULL,
                region TEXT NOT NULL,
                language TEXT NOT NULL,
                tags TEXT NOT NULL,
                popularity INTEGER NOT NULL,
                created_at INTEGER NOT NULL
            )
        """)
        
        // 创建文化学习进度表
        database.execSQL("""
            CREATE TABLE IF NOT EXISTS cultural_learning_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                user_id TEXT NOT NULL,
                context_id TEXT NOT NULL,
                is_learned INTEGER NOT NULL,
                difficulty TEXT NOT NULL,
                last_accessed_at INTEGER NOT NULL,
                access_count INTEGER NOT NULL,
                bookmarked INTEGER NOT NULL,
                notes TEXT NOT NULL,
                FOREIGN KEY(context_id) REFERENCES cultural_contexts(id) ON DELETE CASCADE
            )
        """)
        
        // 创建索引
        createIndexes(database)
    }
    
    private fun createIndexes(database: SupportSQLiteDatabase) {
        // cultural_contexts表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_contexts_word ON cultural_contexts(word)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_contexts_languages ON cultural_contexts(source_language, target_language)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_contexts_region ON cultural_contexts(region)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_contexts_difficulty ON cultural_contexts(difficulty)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_contexts_created_at ON cultural_contexts(created_at)")
        
        // usage_contexts表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_usage_contexts_context_id ON usage_contexts(context_id)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_usage_contexts_appropriateness ON usage_contexts(appropriateness)")
        
        // regional_differences表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_regional_differences_context_id ON regional_differences(context_id)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_regional_differences_region ON regional_differences(region)")
        
        // cultural_examples表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_examples_context_id ON cultural_examples(context_id)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_examples_appropriate_usage ON cultural_examples(appropriate_usage)")
        
        // cultural_recommendations表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_type ON cultural_recommendations(type)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_difficulty ON cultural_recommendations(difficulty)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_region ON cultural_recommendations(region)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_language ON cultural_recommendations(language)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_popularity ON cultural_recommendations(popularity)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_recommendations_created_at ON cultural_recommendations(created_at)")
        
        // cultural_learning_progress表索引
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_learning_progress_user_id ON cultural_learning_progress(user_id)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_learning_progress_context_id ON cultural_learning_progress(context_id)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_learning_progress_is_learned ON cultural_learning_progress(is_learned)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_learning_progress_bookmarked ON cultural_learning_progress(bookmarked)")
        database.execSQL("CREATE INDEX IF NOT EXISTS index_cultural_learning_progress_last_accessed_at ON cultural_learning_progress(last_accessed_at)")
    }
}