package com.cadence.core.network.interceptor

import com.cadence.core.network.monitor.NetworkMonitor
import com.cadence.core.network.monitor.NetworkQuality
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.util.concurrent.PriorityBlockingQueue
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 请求优先级拦截器
 * 根据网络状态和请求类型管理请求优先级和并发数
 */
@Singleton
class RequestPriorityInterceptor @Inject constructor(
    private val networkMonitor: NetworkMonitor
) : Interceptor {
    
    companion object {
        // 并发请求限制
        private const val MAX_CONCURRENT_REQUESTS_WIFI = 6
        private const val MAX_CONCURRENT_REQUESTS_CELLULAR = 3
        private const val MAX_CONCURRENT_REQUESTS_POOR = 1
        
        // 请求超时时间（毫秒）
        private const val REQUEST_TIMEOUT_MS = 30_000L
    }
    
    // 信号量控制并发请求数
    private var requestSemaphore = Semaphore(MAX_CONCURRENT_REQUESTS_WIFI)
    
    // 请求队列（按优先级排序）
    private val requestQueue = PriorityBlockingQueue<PriorityRequest>()
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val priority = determineRequestPriority(request.url.toString())
        
        // 根据网络状态调整并发数
        adjustConcurrencyLimit()
        
        val priorityRequest = PriorityRequest(
            priority = priority,
            timestamp = System.currentTimeMillis(),
            chain = chain
        )
        
        // 获取执行许可
        val acquired = try {
            requestSemaphore.tryAcquire(REQUEST_TIMEOUT_MS, TimeUnit.MILLISECONDS)
        } catch (e: InterruptedException) {
            Thread.currentThread().interrupt()
            throw InterruptedException("请求被中断")
        }
        
        if (!acquired) {
            Timber.w("请求超时，无法获取执行许可: ${request.url}")
            throw RequestTimeoutException("请求队列已满，请稍后重试")
        }
        
        try {
            Timber.d("执行请求 - 优先级: ${priority.name}, URL: ${request.url}")
            return chain.proceed(request)
        } finally {
            requestSemaphore.release()
        }
    }
    
    /**
     * 根据网络状态调整并发限制
     */
    private fun adjustConcurrencyLimit() {
        val networkQuality = networkMonitor.getNetworkQuality()
        val newLimit = when (networkQuality) {
            NetworkQuality.EXCELLENT -> MAX_CONCURRENT_REQUESTS_WIFI
            NetworkQuality.GOOD -> MAX_CONCURRENT_REQUESTS_WIFI
            NetworkQuality.FAIR -> MAX_CONCURRENT_REQUESTS_CELLULAR
            NetworkQuality.POOR -> MAX_CONCURRENT_REQUESTS_POOR
            NetworkQuality.NO_CONNECTION -> 0
        }
        
        // 如果限制发生变化，重新创建信号量
        if (requestSemaphore.availablePermits() + requestSemaphore.queueLength != newLimit) {
            val currentPermits = requestSemaphore.availablePermits()
            requestSemaphore = Semaphore(newLimit)
            
            Timber.d("调整并发限制: $newLimit (网络质量: ${networkQuality.name})")
        }
    }
    
    /**
     * 确定请求优先级
     */
    private fun determineRequestPriority(url: String): RequestPriority {
        return when {
            // 翻译请求 - 高优先级
            url.contains("generateContent") || url.contains("translate") -> RequestPriority.HIGH
            
            // 同步请求 - 中优先级
            url.contains("sync") -> RequestPriority.MEDIUM
            
            // 用户设置 - 中优先级
            url.contains("preferences") || url.contains("settings") -> RequestPriority.MEDIUM
            
            // 学习数据 - 中优先级
            url.contains("learning") || url.contains("progress") -> RequestPriority.MEDIUM
            
            // 文化背景 - 低优先级
            url.contains("cultural") || url.contains("context") -> RequestPriority.LOW
            
            // 统计和分析 - 低优先级
            url.contains("analytics") || url.contains("stats") -> RequestPriority.LOW
            
            // 模型信息 - 低优先级
            url.contains("models") || url.contains("info") -> RequestPriority.LOW
            
            // 默认中优先级
            else -> RequestPriority.MEDIUM
        }
    }
    
    /**
     * 获取当前队列状态
     */
    fun getQueueStatus(): QueueStatus {
        return QueueStatus(
            availablePermits = requestSemaphore.availablePermits(),
            queueLength = requestSemaphore.queueLength,
            maxConcurrency = when (networkMonitor.getNetworkQuality()) {
                NetworkQuality.EXCELLENT, NetworkQuality.GOOD -> MAX_CONCURRENT_REQUESTS_WIFI
                NetworkQuality.FAIR -> MAX_CONCURRENT_REQUESTS_CELLULAR
                NetworkQuality.POOR -> MAX_CONCURRENT_REQUESTS_POOR
                NetworkQuality.NO_CONNECTION -> 0
            }
        )
    }
}

/**
 * 请求优先级枚举
 */
enum class RequestPriority(val value: Int) {
    HIGH(3),    // 高优先级：翻译请求
    MEDIUM(2),  // 中优先级：同步、设置、学习数据
    LOW(1)      // 低优先级：文化背景、统计、模型信息
}

/**
 * 优先级请求包装类
 */
private data class PriorityRequest(
    val priority: RequestPriority,
    val timestamp: Long,
    val chain: Interceptor.Chain
) : Comparable<PriorityRequest> {
    
    override fun compareTo(other: PriorityRequest): Int {
        // 首先按优先级排序（高优先级在前）
        val priorityComparison = other.priority.value.compareTo(this.priority.value)
        if (priorityComparison != 0) {
            return priorityComparison
        }
        
        // 优先级相同时按时间排序（早的在前）
        return this.timestamp.compareTo(other.timestamp)
    }
}

/**
 * 队列状态数据类
 */
data class QueueStatus(
    val availablePermits: Int,
    val queueLength: Int,
    val maxConcurrency: Int
) {
    val utilizationRate: Float
        get() = if (maxConcurrency > 0) {
            (maxConcurrency - availablePermits).toFloat() / maxConcurrency
        } else {
            0f
        }
    
    val isOverloaded: Boolean
        get() = queueLength > maxConcurrency
}

/**
 * 请求超时异常
 */
class RequestTimeoutException(message: String) : Exception(message)

/**
 * 请求优先级扩展函数
 */
fun RequestPriority.getDisplayName(): String = when (this) {
    RequestPriority.HIGH -> "高优先级"
    RequestPriority.MEDIUM -> "中优先级"
    RequestPriority.LOW -> "低优先级"
}

fun RequestPriority.getDescription(): String = when (this) {
    RequestPriority.HIGH -> "翻译请求等核心功能，优先处理"
    RequestPriority.MEDIUM -> "同步、设置、学习数据等重要功能"
    RequestPriority.LOW -> "文化背景、统计信息等辅助功能"
}

fun RequestPriority.getColor(): String = when (this) {
    RequestPriority.HIGH -> "#F44336"    // 红色
    RequestPriority.MEDIUM -> "#FF9800"  // 橙色
    RequestPriority.LOW -> "#4CAF50"     // 绿色
}
