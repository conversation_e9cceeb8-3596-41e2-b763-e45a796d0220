package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.TagEntity
import com.cadence.core.database.entity.TranslationTagEntity
import kotlinx.coroutines.flow.Flow

/**
 * 标签数据访问对象
 * 提供标签相关的数据库操作
 */
@Dao
interface TagDao {
    
    /**
     * 获取所有标签
     * @return 标签列表流
     */
    @Query("SELECT * FROM tags ORDER BY usage_count DESC, name ASC")
    fun getAllTags(): Flow<List<TagEntity>>
    
    /**
     * 根据ID获取标签
     * @param tagId 标签ID
     * @return 标签实体
     */
    @Query("SELECT * FROM tags WHERE id = :tagId")
    suspend fun getTagById(tagId: String): TagEntity?
    
    /**
     * 根据名称获取标签
     * @param name 标签名称
     * @return 标签实体
     */
    @Query("SELECT * FROM tags WHERE name = :name")
    suspend fun getTagByName(name: String): TagEntity?
    
    /**
     * 搜索标签
     * @param query 搜索关键词
     * @return 匹配的标签列表流
     */
    @Query("SELECT * FROM tags WHERE name LIKE '%' || :query || '%' ORDER BY usage_count DESC")
    fun searchTags(query: String): Flow<List<TagEntity>>
    
    /**
     * 获取热门标签
     * @param limit 限制数量
     * @return 热门标签列表流
     */
    @Query("SELECT * FROM tags WHERE usage_count > 0 ORDER BY usage_count DESC LIMIT :limit")
    fun getPopularTags(limit: Int = 10): Flow<List<TagEntity>>
    
    /**
     * 插入标签
     * @param tag 标签实体
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTag(tag: TagEntity)
    
    /**
     * 批量插入标签
     * @param tags 标签列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTags(tags: List<TagEntity>)
    
    /**
     * 更新标签
     * @param tag 标签实体
     */
    @Update
    suspend fun updateTag(tag: TagEntity)
    
    /**
     * 删除标签
     * @param tag 标签实体
     */
    @Delete
    suspend fun deleteTag(tag: TagEntity)
    
    /**
     * 根据ID删除标签
     * @param tagId 标签ID
     */
    @Query("DELETE FROM tags WHERE id = :tagId")
    suspend fun deleteTagById(tagId: String)
    
    /**
     * 增加标签使用次数
     * @param tagId 标签ID
     */
    @Query("UPDATE tags SET usage_count = usage_count + 1, updated_at = :updatedAt WHERE id = :tagId")
    suspend fun incrementUsageCount(tagId: String, updatedAt: Long)
    
    /**
     * 减少标签使用次数
     * @param tagId 标签ID
     */
    @Query("UPDATE tags SET usage_count = CASE WHEN usage_count > 0 THEN usage_count - 1 ELSE 0 END, updated_at = :updatedAt WHERE id = :tagId")
    suspend fun decrementUsageCount(tagId: String, updatedAt: Long)
    
    // 翻译标签关联操作
    
    /**
     * 获取翻译的所有标签
     * @param translationId 翻译ID
     * @return 标签列表流
     */
    @Query("""
        SELECT t.* FROM tags t 
        INNER JOIN translation_tags tt ON t.id = tt.tag_id 
        WHERE tt.translation_id = :translationId 
        ORDER BY t.name ASC
    """)
    fun getTagsForTranslation(translationId: String): Flow<List<TagEntity>>
    
    /**
     * 获取使用指定标签的翻译ID列表
     * @param tagId 标签ID
     * @return 翻译ID列表流
     */
    @Query("SELECT translation_id FROM translation_tags WHERE tag_id = :tagId ORDER BY created_at DESC")
    fun getTranslationIdsForTag(tagId: String): Flow<List<String>>
    
    /**
     * 为翻译添加标签
     * @param translationTag 翻译标签关联实体
     */
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addTagToTranslation(translationTag: TranslationTagEntity)
    
    /**
     * 批量为翻译添加标签
     * @param translationTags 翻译标签关联列表
     */
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun addTagsToTranslation(translationTags: List<TranslationTagEntity>)
    
    /**
     * 从翻译中移除标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     */
    @Query("DELETE FROM translation_tags WHERE translation_id = :translationId AND tag_id = :tagId")
    suspend fun removeTagFromTranslation(translationId: String, tagId: String)
    
    /**
     * 移除翻译的所有标签
     * @param translationId 翻译ID
     */
    @Query("DELETE FROM translation_tags WHERE translation_id = :translationId")
    suspend fun removeAllTagsFromTranslation(translationId: String)
    
    /**
     * 移除标签的所有关联
     * @param tagId 标签ID
     */
    @Query("DELETE FROM translation_tags WHERE tag_id = :tagId")
    suspend fun removeAllTranslationsFromTag(tagId: String)
    
    /**
     * 检查翻译是否有指定标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     * @return 是否存在关联
     */
    @Query("SELECT COUNT(*) > 0 FROM translation_tags WHERE translation_id = :translationId AND tag_id = :tagId")
    suspend fun hasTag(translationId: String, tagId: String): Boolean
    
    /**
     * 获取标签统计信息
     * @return 标签数量
     */
    @Query("SELECT COUNT(*) FROM tags")
    suspend fun getTagCount(): Int
    
    /**
     * 清理未使用的标签
     */
    @Query("DELETE FROM tags WHERE usage_count = 0")
    suspend fun cleanupUnusedTags()
}
