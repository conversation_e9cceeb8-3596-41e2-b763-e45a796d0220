package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 标签数据实体
 * 存储用户创建的标签信息
 */
@Entity(
    tableName = "tags",
    indices = [
        Index(value = ["name"], unique = true),
        Index(value = ["created_at"])
    ]
)
@Serializable
data class TagEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,
    
    @ColumnInfo(name = "name")
    val name: String,
    
    @ColumnInfo(name = "color")
    val color: String? = null, // 标签颜色，十六进制格式如 "#FF5722"
    
    @ColumnInfo(name = "description")
    val description: String? = null,
    
    @ColumnInfo(name = "usage_count")
    val usageCount: Int = 0, // 使用次数统计
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)

/**
 * 翻译标签关联数据实体
 * 存储翻译记录与标签的多对多关系
 */
@Entity(
    tableName = "translation_tags",
    primaryKeys = ["translation_id", "tag_id"],
    indices = [
        Index(value = ["translation_id"]),
        Index(value = ["tag_id"]),
        Index(value = ["created_at"])
    ]
)
@Serializable
data class TranslationTagEntity(
    @ColumnInfo(name = "translation_id")
    val translationId: String,
    
    @ColumnInfo(name = "tag_id")
    val tagId: String,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long
)
