package com.cadence.core.database.dao.learning

import androidx.room.*
import com.cadence.core.database.entity.learning.StudySessionEntity
import kotlinx.coroutines.flow.Flow

/**
 * 学习会话数据访问对象
 */
@Dao
interface StudySessionDao {
    
    /**
     * 获取用户的所有学习会话
     */
    @Query("SELECT * FROM study_sessions WHERE user_id = :userId ORDER BY start_time DESC")
    fun getUserStudySessions(userId: String): Flow<List<StudySessionEntity>>
    
    /**
     * 获取最近的学习会话
     */
    @Query("SELECT * FROM study_sessions WHERE user_id = :userId ORDER BY start_time DESC LIMIT :limit")
    suspend fun getRecentStudySessions(userId: String, limit: Int): List<StudySessionEntity>
    
    /**
     * 获取特定类型的学习会话
     */
    @Query("SELECT * FROM study_sessions WHERE user_id = :userId AND session_type = :sessionType ORDER BY start_time DESC")
    fun getSessionsByType(userId: String, sessionType: String): Flow<List<StudySessionEntity>>
    
    /**
     * 获取指定时间范围内的学习会话
     */
    @Query("""
        SELECT * FROM study_sessions 
        WHERE user_id = :userId 
        AND start_time >= :startTime 
        AND start_time <= :endTime 
        ORDER BY start_time DESC
    """)
    suspend fun getSessionsInTimeRange(userId: String, startTime: Long, endTime: Long): List<StudySessionEntity>
    
    /**
     * 获取今日学习会话
     */
    @Query("""
        SELECT * FROM study_sessions 
        WHERE user_id = :userId 
        AND start_time >= :todayStart 
        ORDER BY start_time DESC
    """)
    suspend fun getTodayStudySessions(userId: String, todayStart: Long): List<StudySessionEntity>
    
    /**
     * 获取学习会话统计
     */
    @Query("""
        SELECT 
            COUNT(*) as total_sessions,
            SUM(time_spent) as total_time,
            SUM(correct_answers) as total_correct,
            SUM(total_questions) as total_questions,
            AVG(CASE WHEN total_questions > 0 THEN CAST(correct_answers AS FLOAT) / total_questions ELSE 0 END) as avg_accuracy
        FROM study_sessions 
        WHERE user_id = :userId 
        AND end_time IS NOT NULL
    """)
    suspend fun getSessionStatistics(userId: String): SessionStatisticsResult?
    
    /**
     * 获取最后一次学习时间
     */
    @Query("SELECT MAX(start_time) FROM study_sessions WHERE user_id = :userId")
    suspend fun getLastStudyTime(userId: String): Long?
    
    /**
     * 插入学习会话
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStudySession(session: StudySessionEntity)
    
    /**
     * 更新学习会话
     */
    @Update
    suspend fun updateStudySession(session: StudySessionEntity)
    
    /**
     * 完成学习会话
     */
    @Query("""
        UPDATE study_sessions 
        SET end_time = :endTime,
            correct_answers = :correctAnswers,
            total_questions = :totalQuestions,
            time_spent = :timeSpent
        WHERE id = :sessionId
    """)
    suspend fun completeStudySession(
        sessionId: String,
        endTime: Long,
        correctAnswers: Int,
        totalQuestions: Int,
        timeSpent: Long
    )
    
    /**
     * 删除学习会话
     */
    @Query("DELETE FROM study_sessions WHERE id = :sessionId")
    suspend fun deleteStudySession(sessionId: String)
    
    /**
     * 删除用户所有学习会话
     */
    @Query("DELETE FROM study_sessions WHERE user_id = :userId")
    suspend fun deleteAllUserSessions(userId: String)
    
    /**
     * 删除指定时间之前的会话
     */
    @Query("DELETE FROM study_sessions WHERE start_time < :beforeTime")
    suspend fun deleteSessionsBefore(beforeTime: Long)
}

/**
 * 学习会话统计查询结果
 */
data class SessionStatisticsResult(
    val total_sessions: Int,
    val total_time: Long,
    val total_correct: Int,
    val total_questions: Int,
    val avg_accuracy: Float
)