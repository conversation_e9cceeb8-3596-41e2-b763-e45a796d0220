package com.cadence.feature.learning.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.learning.presentation.viewmodel.ProgressViewModel
import com.cadence.feature.learning.presentation.component.ProgressChart
import com.cadence.feature.learning.presentation.component.MasteryLevelCard
import com.cadence.feature.learning.presentation.component.CategoryProgressCard

/**
 * 学习进度跟踪界面
 * 显示用户的学习进度和统计信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProgressScreen(
    onNavigateBack: () -> Unit,
    onNavigateToWordDetail: (String) -> Unit,
    viewModel: ProgressViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val statistics by viewModel.statistics.collectAsStateWithLifecycle()
    val progressByMastery by viewModel.progressByMastery.collectAsStateWithLifecycle()
    val categoryProgress by viewModel.categoryProgress.collectAsStateWithLifecycle()
    val dailyProgress by viewModel.dailyProgress.collectAsStateWithLifecycle()
    
    LaunchedEffect(Unit) {
        viewModel.loadProgressData()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("学习进度") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            }
        )
        
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 总体统计卡片
                item {
                    statistics?.let { stats ->
                        OverallStatsCard(statistics = stats)
                    }
                }
                
                // 每日进度图表
                item {
                    if (dailyProgress.isNotEmpty()) {
                        ProgressChart(
                            dailyProgress = dailyProgress,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
                
                // 掌握程度分布
                item {
                    Text(
                        text = "掌握程度分布",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                items(progressByMastery.entries.toList()) { (masteryLevel, progressList) ->
                    MasteryLevelCard(
                        masteryLevel = masteryLevel,
                        progressList = progressList,
                        onWordClick = onNavigateToWordDetail
                    )
                }
                
                // 分类进度
                item {
                    Text(
                        text = "分类学习进度",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                items(categoryProgress.entries.toList()) { (category, progress) ->
                    CategoryProgressCard(
                        category = category,
                        progress = progress
                    )
                }
            }
        }
        
        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误提示
            }
        }
    }
}

/**
 * 总体统计卡片
 */
@Composable
private fun OverallStatsCard(
    statistics: com.cadence.domain.model.learning.LearningStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "学习统计",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 统计数据网格
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    icon = Icons.Default.School,
                    label = "总单词",
                    value = statistics.totalWordsLearned.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    icon = Icons.Default.Star,
                    label = "已掌握",
                    value = statistics.masteredWords.toString(),
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    icon = Icons.Default.TrendingUp,
                    label = "正确率",
                    value = "${(statistics.averageAccuracy * 100).toInt()}%",
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    icon = Icons.Default.AccessTime,
                    label = "学习时长",
                    value = formatStudyTime(statistics.totalStudyTime),
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    icon = Icons.Default.LocalFireDepartment,
                    label = "连续天数",
                    value = "${statistics.currentStreak}天",
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    icon = Icons.Default.Refresh,
                    label = "待复习",
                    value = statistics.wordsToReview.toString(),
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(24.dp),
            tint = MaterialTheme.colorScheme.onPrimaryContainer
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
        )
    }
}

/**
 * 格式化学习时长
 */
private fun formatStudyTime(timeInMillis: Long): String {
    val hours = timeInMillis / (1000 * 60 * 60)
    val minutes = (timeInMillis % (1000 * 60 * 60)) / (1000 * 60)
    
    return when {
        hours > 0 -> "${hours}h ${minutes}m"
        minutes > 0 -> "${minutes}m"
        else -> "< 1m"
    }
}