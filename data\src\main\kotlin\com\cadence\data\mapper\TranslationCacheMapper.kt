package com.cadence.data.mapper

import com.cadence.core.database.entity.TranslationCacheEntity
import com.cadence.domain.model.*
import java.security.MessageDigest
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译缓存映射器
 * 处理领域模型与缓存实体之间的转换
 */
@Singleton
class TranslationCacheMapper @Inject constructor() {
    
    /**
     * 将网络翻译结果转换为缓存实体
     */
    fun toEntity(
        translationResult: com.cadence.core.network.dto.TranslationResult,
        request: TranslationRequest,
        translationId: String = UUID.randomUUID().toString(),
        processingTimeMs: Long = 0L,
        translationSource: String = TranslationCacheEntity.SOURCE_ONLINE
    ): TranslationCacheEntity {
        val currentTime = System.currentTimeMillis()
        val cacheKey = generateCacheKey(request)
        val cacheSizeBytes = calculateCacheSize(translationResult, request)
        
        return TranslationCacheEntity(
            id = UUID.randomUUID().toString(),
            cacheKey = cacheKey,
            translationId = translationId,
            sourceText = request.text,
            translatedText = translationResult.translatedText,
            sourceLanguageCode = request.sourceLanguage.code,
            sourceLanguageName = request.sourceLanguage.name,
            sourceRegionCode = request.sourceRegion?.code,
            sourceRegionName = request.sourceRegion?.name,
            targetLanguageCode = request.targetLanguage.code,
            targetLanguageName = request.targetLanguage.name,
            targetRegionCode = request.targetRegion?.code,
            targetRegionName = request.targetRegion?.name,
            confidenceScore = translationResult.confidence,
            culturalContext = translationResult.culturalContext,
            translationSource = translationSource,
            processingTimeMs = processingTimeMs,
            accessCount = 0,
            lastAccessedAt = currentTime,
            createdAt = currentTime,
            updatedAt = currentTime,
            expiresAt = currentTime + TranslationCacheEntity.DEFAULT_CACHE_DURATION_MS,
            isFavorite = false,
            cacheSizeBytes = cacheSizeBytes,
            cachePriority = TranslationCacheEntity.PRIORITY_NORMAL,
            isPinned = false
        )
    }
    
    /**
     * 将缓存实体转换为网络翻译结果
     */
    fun toNetworkTranslationResult(entity: TranslationCacheEntity): com.cadence.core.network.dto.TranslationResult {
        return com.cadence.core.network.dto.TranslationResult(
            translatedText = entity.translatedText,
            sourceLanguage = entity.sourceLanguageCode,
            targetLanguage = entity.targetLanguageCode,
            confidence = entity.confidenceScore ?: 0.9f,
            culturalContext = entity.culturalContext
        )
    }

    /**
     * 将缓存实体转换为领域翻译结果
     */
    fun toDomainTranslationResult(entity: TranslationCacheEntity): com.cadence.domain.model.TranslationResult {
        val translation = Translation(
            id = entity.translationId,
            sourceText = entity.sourceText,
            translatedText = entity.translatedText,
            sourceLanguage = Language(entity.sourceLanguageCode, entity.sourceLanguageName),
            targetLanguage = Language(entity.targetLanguageCode, entity.targetLanguageName),
            sourceRegion = entity.sourceRegionCode?.let {
                Region(it, entity.sourceRegionName ?: it)
            },
            targetRegion = entity.targetRegionCode?.let {
                Region(it, entity.targetRegionName ?: it)
            },
            confidenceScore = entity.confidenceScore ?: 0.9f,
            isFavorite = entity.isFavorite,
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            translationType = when (entity.translationSource) {
                TranslationCacheEntity.SOURCE_ONLINE -> TranslationType.ONLINE
                TranslationCacheEntity.SOURCE_OFFLINE -> TranslationType.OFFLINE
                TranslationCacheEntity.SOURCE_HYBRID -> TranslationType.HYBRID
                else -> TranslationType.ONLINE
            },
            culturalContext = entity.culturalContext,
            tags = emptyList(),
            notes = null
        )

        return com.cadence.domain.model.TranslationResult(
            translation = translation,
            isFromCache = true,
            processingTimeMs = entity.processingTimeMs
        )
    }
    
    /**
     * 将缓存实体转换为翻译历史记录
     */
    fun toTranslationHistory(entity: TranslationCacheEntity): TranslationHistory {
        return TranslationHistory(
            id = entity.translationId,
            sourceText = entity.sourceText,
            translatedText = entity.translatedText,
            sourceLanguage = Language(entity.sourceLanguageCode, entity.sourceLanguageName),
            targetLanguage = Language(entity.targetLanguageCode, entity.targetLanguageName),
            sourceRegion = entity.sourceRegionCode?.let { 
                Region(it, entity.sourceRegionName ?: it) 
            },
            targetRegion = entity.targetRegionCode?.let { 
                Region(it, entity.targetRegionName ?: it) 
            },
            confidence = entity.confidenceScore ?: 0.9f,
            culturalContext = entity.culturalContext,
            translationType = when (entity.translationSource) {
                TranslationCacheEntity.SOURCE_ONLINE -> TranslationType.ONLINE
                TranslationCacheEntity.SOURCE_OFFLINE -> TranslationType.OFFLINE
                TranslationCacheEntity.SOURCE_HYBRID -> TranslationType.HYBRID
                else -> TranslationType.ONLINE
            },
            isFavorite = entity.isFavorite,
            createdAt = entity.createdAt,
            lastAccessedAt = entity.lastAccessedAt,
            accessCount = entity.accessCount
        )
    }
    
    /**
     * 将翻译历史记录转换为缓存实体
     */
    fun fromTranslationHistory(history: TranslationHistory): TranslationCacheEntity {
        val currentTime = System.currentTimeMillis()
        val request = TranslationRequest(
            text = history.sourceText,
            sourceLanguage = history.sourceLanguage,
            targetLanguage = history.targetLanguage,
            sourceRegion = history.sourceRegion,
            targetRegion = history.targetRegion
        )
        val cacheKey = generateCacheKey(request)
        val cacheSizeBytes = calculateCacheSize(history)
        
        return TranslationCacheEntity(
            id = UUID.randomUUID().toString(),
            cacheKey = cacheKey,
            translationId = history.id,
            sourceText = history.sourceText,
            translatedText = history.translatedText,
            sourceLanguageCode = history.sourceLanguage.code,
            sourceLanguageName = history.sourceLanguage.name,
            sourceRegionCode = history.sourceRegion?.code,
            sourceRegionName = history.sourceRegion?.name,
            targetLanguageCode = history.targetLanguage.code,
            targetLanguageName = history.targetLanguage.name,
            targetRegionCode = history.targetRegion?.code,
            targetRegionName = history.targetRegion?.name,
            confidenceScore = history.confidence,
            culturalContext = history.culturalContext,
            translationSource = when (history.translationType) {
                TranslationType.ONLINE -> TranslationCacheEntity.SOURCE_ONLINE
                TranslationType.OFFLINE -> TranslationCacheEntity.SOURCE_OFFLINE
                TranslationType.HYBRID -> TranslationCacheEntity.SOURCE_HYBRID
            },
            processingTimeMs = 0L,
            accessCount = history.accessCount,
            lastAccessedAt = history.lastAccessedAt,
            createdAt = history.createdAt,
            updatedAt = currentTime,
            expiresAt = if (history.isFavorite) {
                currentTime + TranslationCacheEntity.FAVORITE_CACHE_DURATION_MS
            } else {
                currentTime + TranslationCacheEntity.DEFAULT_CACHE_DURATION_MS
            },
            isFavorite = history.isFavorite,
            cacheSizeBytes = cacheSizeBytes,
            cachePriority = if (history.isFavorite) {
                TranslationCacheEntity.PRIORITY_HIGH
            } else {
                TranslationCacheEntity.PRIORITY_NORMAL
            },
            isPinned = false
        )
    }
    
    /**
     * 生成缓存键
     */
    fun generateCacheKey(request: TranslationRequest): String {
        val keyComponents = buildString {
            append(request.text.trim().lowercase())
            append("|")
            append(request.sourceLanguage.code)
            append("|")
            append(request.targetLanguage.code)
            append("|")
            append(request.sourceRegion?.code ?: "")
            append("|")
            append(request.targetRegion?.code ?: "")
            append("|")
            append(request.includeCulturalContext)
        }
        
        return generateMD5Hash(keyComponents)
    }
    
    /**
     * 生成缓存键（简化版本）
     */
    fun generateCacheKey(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String? = null,
        targetRegion: String? = null,
        includeCulturalContext: Boolean = false
    ): String {
        val keyComponents = buildString {
            append(text.trim().lowercase())
            append("|")
            append(sourceLanguage)
            append("|")
            append(targetLanguage)
            append("|")
            append(sourceRegion ?: "")
            append("|")
            append(targetRegion ?: "")
            append("|")
            append(includeCulturalContext)
        }
        
        return generateMD5Hash(keyComponents)
    }
    
    /**
     * 计算缓存大小
     */
    private fun calculateCacheSize(translationResult: TranslationResult, request: TranslationRequest): Int {
        var size = 0
        size += request.text.toByteArray(Charsets.UTF_8).size
        size += translationResult.translatedText.toByteArray(Charsets.UTF_8).size
        size += request.sourceLanguage.code.toByteArray(Charsets.UTF_8).size
        size += request.targetLanguage.code.toByteArray(Charsets.UTF_8).size
        size += request.sourceLanguage.name.toByteArray(Charsets.UTF_8).size
        size += request.targetLanguage.name.toByteArray(Charsets.UTF_8).size
        size += (request.sourceRegion?.code?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (request.targetRegion?.code?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (request.sourceRegion?.name?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (request.targetRegion?.name?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (translationResult.culturalContext?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += 100 // 其他字段的估算大小
        return size
    }
    
    /**
     * 计算缓存大小（历史记录版本）
     */
    private fun calculateCacheSize(history: TranslationHistory): Int {
        var size = 0
        size += history.sourceText.toByteArray(Charsets.UTF_8).size
        size += history.translatedText.toByteArray(Charsets.UTF_8).size
        size += history.sourceLanguage.code.toByteArray(Charsets.UTF_8).size
        size += history.targetLanguage.code.toByteArray(Charsets.UTF_8).size
        size += history.sourceLanguage.name.toByteArray(Charsets.UTF_8).size
        size += history.targetLanguage.name.toByteArray(Charsets.UTF_8).size
        size += (history.sourceRegion?.code?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (history.targetRegion?.code?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (history.sourceRegion?.name?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (history.targetRegion?.name?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += (history.culturalContext?.toByteArray(Charsets.UTF_8)?.size ?: 0)
        size += 100 // 其他字段的估算大小
        return size
    }
    
    /**
     * 生成MD5哈希
     */
    private fun generateMD5Hash(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val digest = md.digest(input.toByteArray(Charsets.UTF_8))
        return digest.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 批量转换缓存实体为翻译历史记录
     */
    fun toTranslationHistoryList(entities: List<TranslationCacheEntity>): List<TranslationHistory> {
        return entities.map { toTranslationHistory(it) }
    }
    
    /**
     * 批量转换翻译历史记录为缓存实体
     */
    fun fromTranslationHistoryList(histories: List<TranslationHistory>): List<TranslationCacheEntity> {
        return histories.map { fromTranslationHistory(it) }
    }
    
    /**
     * 创建缓存统计摘要
     */
    fun createCacheStatisticsSummary(
        totalCaches: Int,
        validCaches: Int,
        favoriteCaches: Int,
        pinnedCaches: Int,
        totalSizeBytes: Long,
        avgAccessCount: Float,
        maxAccessCount: Int
    ): CacheStatisticsSummary {
        return CacheStatisticsSummary(
            totalCount = totalCaches,
            validCount = validCaches,
            expiredCount = totalCaches - validCaches,
            favoriteCount = favoriteCaches,
            pinnedCount = pinnedCaches,
            totalSizeBytes = totalSizeBytes,
            averageSizeBytes = if (totalCaches > 0) totalSizeBytes.toFloat() / totalCaches else 0f,
            avgAccessCount = avgAccessCount,
            maxAccessCount = maxAccessCount,
            hitRate = if (totalCaches > 0) validCaches.toFloat() / totalCaches else 0f
        )
    }
}

/**
 * 缓存统计摘要
 */
data class CacheStatisticsSummary(
    val totalCount: Int,
    val validCount: Int,
    val expiredCount: Int,
    val favoriteCount: Int,
    val pinnedCount: Int,
    val totalSizeBytes: Long,
    val averageSizeBytes: Float,
    val avgAccessCount: Float,
    val maxAccessCount: Int,
    val hitRate: Float
)
