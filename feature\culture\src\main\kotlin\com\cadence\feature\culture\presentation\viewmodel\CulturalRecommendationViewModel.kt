package com.cadence.feature.culture.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.culture.*
import com.cadence.domain.repository.culture.CulturalContextRepository
import com.cadence.feature.culture.presentation.state.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 文化推荐界面ViewModel
 * 负责管理文化知识推荐、筛选、个性化推荐等功能
 */
@HiltViewModel
class CulturalRecommendationViewModel @Inject constructor(
    private val culturalContextRepository: CulturalContextRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CulturalRecommendationUiState())
    val uiState: StateFlow<CulturalRecommendationUiState> = _uiState.asStateFlow()

    private val _userInteractionState = MutableStateFlow(UserInteractionState())
    val userInteractionState: StateFlow<UserInteractionState> = _userInteractionState.asStateFlow()

    init {
        loadRecommendations()
    }

    /**
     * 加载所有推荐内容
     */
    fun loadRecommendations() {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                // 并行加载不同类型的推荐
                val popularJob = launch { loadPopularRecommendations() }
                val personalizedJob = launch { loadPersonalizedRecommendations() }
                val categoryJob = launch { loadCategoryRecommendations() }
                
                // 等待所有加载完成
                popularJob.join()
                personalizedJob.join()
                categoryJob.join()
                
                _uiState.update { it.copy(isLoading = false) }
                
                // 加载用户交互状态
                loadUserInteractions()
                
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "加载推荐内容失败"
                    )
                }
            }
        }
    }

    /**
     * 应用筛选条件
     */
    fun applyFilters(filters: RecommendationFilters) {
        viewModelScope.launch {
            _uiState.update { 
                it.copy(
                    selectedFilters = filters,
                    isFiltering = true
                )
            }
            
            culturalContextRepository.getFilteredRecommendations(filters)
                .onSuccess { filteredRecommendations ->
                    _uiState.update { 
                        it.copy(
                            filteredRecommendations = filteredRecommendations,
                            isFiltering = false
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isFiltering = false,
                            error = error.message ?: "筛选失败"
                        )
                    }
                }
        }
    }

    /**
     * 清除筛选条件
     */
    fun clearFilters() {
        _uiState.update { 
            it.copy(
                selectedFilters = RecommendationFilters(),
                filteredRecommendations = emptyList()
            )
        }
    }

    /**
     * 点赞推荐
     */
    fun likeRecommendation(recommendationId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isLiking = true,
                    lastAction = UserAction.Like
                )
            }
            
            culturalContextRepository.likeRecommendation(recommendationId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            likedRecommendations = state.likedRecommendations + recommendationId
                        )
                    }
                    
                    // 更新推荐的点赞数
                    updateRecommendationLikes(recommendationId, true)
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "点赞失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isLiking = false, lastAction = null)
            }
        }
    }

    /**
     * 取消点赞推荐
     */
    fun unlikeRecommendation(recommendationId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(isLiking = true)
            }
            
            culturalContextRepository.unlikeRecommendation(recommendationId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            likedRecommendations = state.likedRecommendations - recommendationId
                        )
                    }
                    
                    // 更新推荐的点赞数
                    updateRecommendationLikes(recommendationId, false)
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "取消点赞失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isLiking = false)
            }
        }
    }

    /**
     * 收藏推荐
     */
    fun bookmarkRecommendation(recommendationId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isBookmarking = true,
                    lastAction = UserAction.Bookmark
                )
            }
            
            culturalContextRepository.bookmarkRecommendation(recommendationId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            bookmarkedRecommendations = state.bookmarkedRecommendations + recommendationId
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "收藏失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isBookmarking = false, lastAction = null)
            }
        }
    }

    /**
     * 取消收藏推荐
     */
    fun unbookmarkRecommendation(recommendationId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(isBookmarking = true)
            }
            
            culturalContextRepository.unbookmarkRecommendation(recommendationId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            bookmarkedRecommendations = state.bookmarkedRecommendations - recommendationId
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "取消收藏失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isBookmarking = false)
            }
        }
    }

    /**
     * 刷新推荐内容
     */
    fun refreshRecommendations() {
        loadRecommendations()
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }

    /**
     * 获取推荐统计信息
     */
    fun getRecommendationStatistics(): StateFlow<RecommendationStatistics> {
        return _uiState.map { state ->
            val allRecommendations = state.popularRecommendations + 
                                   state.personalizedRecommendations + 
                                   state.categoryRecommendations.values.flatten()
            
            RecommendationStatistics(
                totalRecommendations = allRecommendations.size,
                likedCount = state.likedRecommendations.size,
                bookmarkedCount = state.bookmarkedRecommendations.size,
                typeDistribution = allRecommendations
                    .groupBy { it.type }
                    .mapValues { it.value.size },
                difficultyDistribution = allRecommendations
                    .groupBy { it.difficulty }
                    .mapValues { it.value.size },
                regionDistribution = allRecommendations
                    .groupBy { it.region }
                    .mapValues { it.value.size }
            )
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = RecommendationStatistics()
        )
    }

    // 私有辅助方法

    /**
     * 加载热门推荐
     */
    private suspend fun loadPopularRecommendations() {
        culturalContextRepository.getPopularRecommendations(limit = 10)
            .onSuccess { recommendations ->
                _uiState.update { 
                    it.copy(popularRecommendations = recommendations)
                }
            }
            .onFailure { error ->
                _uiState.update { 
                    it.copy(error = error.message ?: "加载热门推荐失败")
                }
            }
    }

    /**
     * 加载个性化推荐
     */
    private suspend fun loadPersonalizedRecommendations() {
        culturalContextRepository.getPersonalizedRecommendations(limit = 10)
            .onSuccess { recommendations ->
                _uiState.update { 
                    it.copy(personalizedRecommendations = recommendations)
                }
            }
            .onFailure { error ->
                _uiState.update { 
                    it.copy(error = error.message ?: "加载个性化推荐失败")
                }
            }
    }

    /**
     * 加载分类推荐
     */
    private suspend fun loadCategoryRecommendations() {
        val categoryRecommendations = mutableMapOf<CulturalKnowledgeType, List<CulturalRecommendation>>()
        
        // 为每个知识类型加载推荐
        CulturalKnowledgeType.values().forEach { type ->
            culturalContextRepository.getRecommendationsByType(type, limit = 5)
                .onSuccess { recommendations ->
                    if (recommendations.isNotEmpty()) {
                        categoryRecommendations[type] = recommendations
                    }
                }
        }
        
        _uiState.update { 
            it.copy(categoryRecommendations = categoryRecommendations)
        }
    }

    /**
     * 加载用户交互状态
     */
    private suspend fun loadUserInteractions() {
        // 加载点赞状态
        culturalContextRepository.getLikedRecommendationIds()
            .onSuccess { likedIds ->
                _uiState.update { 
                    it.copy(likedRecommendations = likedIds.toSet())
                }
            }
        
        // 加载收藏状态
        culturalContextRepository.getBookmarkedRecommendationIds()
            .onSuccess { bookmarkedIds ->
                _uiState.update { 
                    it.copy(bookmarkedRecommendations = bookmarkedIds.toSet())
                }
            }
    }

    /**
     * 更新推荐的点赞数
     */
    private fun updateRecommendationLikes(recommendationId: String, isLiked: Boolean) {
        _uiState.update { state ->
            val updateRecommendation = { recommendations: List<CulturalRecommendation> ->
                recommendations.map { recommendation ->
                    if (recommendation.id == recommendationId) {
                        recommendation.copy(
                            popularity = if (isLiked) 
                                recommendation.popularity + 1 
                            else 
                                maxOf(0, recommendation.popularity - 1)
                        )
                    } else {
                        recommendation
                    }
                }
            }
            
            state.copy(
                popularRecommendations = updateRecommendation(state.popularRecommendations),
                personalizedRecommendations = updateRecommendation(state.personalizedRecommendations),
                categoryRecommendations = state.categoryRecommendations.mapValues { (_, recommendations) ->
                    updateRecommendation(recommendations)
                },
                filteredRecommendations = updateRecommendation(state.filteredRecommendations)
            )
        }
    }
}

/**
 * 推荐统计数据
 */
data class RecommendationStatistics(
    val totalRecommendations: Int = 0,
    val likedCount: Int = 0,
    val bookmarkedCount: Int = 0,
    val typeDistribution: Map<CulturalKnowledgeType, Int> = emptyMap(),
    val difficultyDistribution: Map<CulturalDifficulty, Int> = emptyMap(),
    val regionDistribution: Map<String, Int> = emptyMap()
)