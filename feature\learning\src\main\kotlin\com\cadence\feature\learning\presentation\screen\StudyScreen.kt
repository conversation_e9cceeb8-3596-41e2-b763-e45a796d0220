package com.cadence.feature.learning.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.learning.presentation.viewmodel.StudyViewModel
import com.cadence.feature.learning.presentation.viewmodel.StudyMode
import com.cadence.feature.learning.presentation.component.StudyProgressBar

/**
 * 学习交互界面
 * 提供实际的学习体验，包括答题、评分等
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StudyScreen(
    wordIds: List<String>,
    studyMode: StudyMode,
    onNavigateBack: () -> Unit,
    onStudyComplete: (Int, Int) -> Unit, // correctAnswers, totalQuestions
    viewModel: StudyViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val currentWord by viewModel.currentWord.collectAsStateWithLifecycle()
    val studySession by viewModel.studySession.collectAsStateWithLifecycle()
    
    LaunchedEffect(wordIds, studyMode) {
        viewModel.startStudySession(wordIds, studyMode)
    }
    
    // 监听学习完成
    LaunchedEffect(uiState.isCompleted) {
        if (uiState.isCompleted) {
            studySession?.let { session ->
                onStudyComplete(session.correctAnswers, session.totalQuestions)
            }
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { 
                Text("${studyMode.displayName} - ${uiState.currentIndex + 1}/${uiState.totalWords}")
            },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "退出学习"
                    )
                }
            }
        )
        
        // 进度条
        StudyProgressBar(
            current = uiState.currentIndex,
            total = uiState.totalWords,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else if (uiState.isCompleted) {
            // 学习完成界面
            StudyCompletionScreen(
                correctAnswers = studySession?.correctAnswers ?: 0,
                totalQuestions = studySession?.totalQuestions ?: 0,
                onFinish = onNavigateBack
            )
        } else {
            currentWord?.let { word ->
                when (studyMode) {
                    StudyMode.FLASHCARD -> FlashcardStudyContent(
                        word = word,
                        onAnswer = { isCorrect ->
                            viewModel.submitAnswer(isCorrect)
                        }
                    )
                    StudyMode.MULTIPLE_CHOICE -> MultipleChoiceStudyContent(
                        word = word,
                        options = uiState.currentOptions,
                        onAnswer = { selectedOption ->
                            viewModel.submitMultipleChoiceAnswer(selectedOption)
                        }
                    )
                    StudyMode.TYPING -> TypingStudyContent(
                        word = word,
                        onAnswer = { userInput ->
                            viewModel.submitTypingAnswer(userInput)
                        }
                    )
                    StudyMode.LISTENING -> ListeningStudyContent(
                        word = word,
                        onAnswer = { userInput ->
                            viewModel.submitListeningAnswer(userInput)
                        }
                    )
                }
            }
        }
        
        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误提示
            }
        }
    }
}

/**
 * 闪卡学习内容
 */
@Composable
private fun FlashcardStudyContent(
    word: com.cadence.domain.model.learning.Word,
    onAnswer: (Boolean) -> Unit
) {
    var showAnswer by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (!showAnswer) {
                        // 显示单词
                        Text(
                            text = word.text,
                            style = MaterialTheme.typography.headlineLarge,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                        word.pronunciation?.let { pronunciation ->
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "[$pronunciation]",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                            )
                        }
                    } else {
                        // 显示答案
                        Text(
                            text = word.translation,
                            style = MaterialTheme.typography.headlineMedium,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onPrimaryContainer,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = word.definition,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f),
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        if (!showAnswer) {
            Button(
                onClick = { showAnswer = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("显示答案")
            }
        } else {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                OutlinedButton(
                    onClick = { 
                        onAnswer(false)
                        showAnswer = false
                    },
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("不认识")
                }
                
                Button(
                    onClick = { 
                        onAnswer(true)
                        showAnswer = false
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("认识")
                }
            }
        }
    }
}

/**
 * 选择题学习内容
 */
@Composable
private fun MultipleChoiceStudyContent(
    word: com.cadence.domain.model.learning.Word,
    options: List<String>,
    onAnswer: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 问题
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "选择正确的翻译：",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = word.text,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                word.pronunciation?.let { pronunciation ->
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "[$pronunciation]",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 选项
        options.forEachIndexed { index, option ->
            Card(
                onClick = { onAnswer(option) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Text(
                    text = "${('A' + index)}. $option",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

/**
 * 拼写练习内容
 */
@Composable
private fun TypingStudyContent(
    word: com.cadence.domain.model.learning.Word,
    onAnswer: (String) -> Unit
) {
    var userInput by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 提示
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "请拼写单词：",
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = word.translation,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = word.definition,
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 输入框
        OutlinedTextField(
            value = userInput,
            onValueChange = { userInput = it },
            label = { Text("输入单词") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { 
                onAnswer(userInput)
                userInput = ""
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = userInput.isNotBlank()
        ) {
            Text("提交答案")
        }
    }
}

/**
 * 听力练习内容
 */
@Composable
private fun ListeningStudyContent(
    word: com.cadence.domain.model.learning.Word,
    onAnswer: (String) -> Unit
) {
    var userInput by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        
        // 播放按钮
        Card(
            modifier = Modifier.size(120.dp),
            shape = RoundedCornerShape(60.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primary
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                IconButton(
                    onClick = { /* 播放音频 */ },
                    modifier = Modifier.size(60.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = "播放",
                        modifier = Modifier.size(40.dp),
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "听音频，输入你听到的单词",
            style = MaterialTheme.typography.titleMedium,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 输入框
        OutlinedTextField(
            value = userInput,
            onValueChange = { userInput = it },
            label = { Text("输入听到的单词") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = { 
                onAnswer(userInput)
                userInput = ""
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = userInput.isNotBlank()
        ) {
            Text("提交答案")
        }
    }
}

/**
 * 学习完成界面
 */
@Composable
private fun StudyCompletionScreen(
    correctAnswers: Int,
    totalQuestions: Int,
    onFinish: () -> Unit
) {
    val accuracy = if (totalQuestions > 0) (correctAnswers.toFloat() / totalQuestions * 100).toInt() else 0
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.CheckCircle,
            contentDescription = null,
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Text(
            text = "学习完成！",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "正确率",
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = "$accuracy%",
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = "$correctAnswers / $totalQuestions",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Button(
            onClick = onFinish,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("完成")
        }
    }
}