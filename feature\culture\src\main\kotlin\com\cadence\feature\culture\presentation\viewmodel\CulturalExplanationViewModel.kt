package com.cadence.feature.culture.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.culture.*
import com.cadence.domain.repository.culture.CulturalContextRepository
import com.cadence.feature.culture.presentation.state.*
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 文化解释界面ViewModel
 * 负责管理文化背景查询、筛选、搜索等功能
 */
@HiltViewModel
class CulturalExplanationViewModel @Inject constructor(
    private val culturalContextRepository: CulturalContextRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CulturalExplanationUiState())
    val uiState: StateFlow<CulturalExplanationUiState> = _uiState.asStateFlow()

    private val _userInteractionState = MutableStateFlow(UserInteractionState())
    val userInteractionState: StateFlow<UserInteractionState> = _userInteractionState.asStateFlow()

    private val _searchResultState = MutableStateFlow(SearchResultState())
    val searchResultState: StateFlow<SearchResultState> = _searchResultState.asStateFlow()

    /**
     * 根据单词加载文化背景信息
     */
    fun loadCulturalContexts(word: String, sourceLanguage: String, targetLanguage: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            culturalContextRepository.getCulturalContextsByWord(word)
                .onSuccess { contexts ->
                    // 筛选匹配语言的文化背景
                    val filteredContexts = contexts.filter { context ->
                        context.sourceLanguage == sourceLanguage && 
                        context.targetLanguage == targetLanguage
                    }
                    
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            culturalContexts = filteredContexts,
                            error = null
                        )
                    }
                    
                    // 加载用户的收藏和学习状态
                    loadUserPreferences(filteredContexts.map { it.id })
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = error.message ?: "加载文化背景信息失败"
                        )
                    }
                }
        }
    }

    /**
     * 搜索文化背景
     */
    fun searchCulturalContexts(query: String) {
        if (query.isBlank()) {
            _searchResultState.update { SearchResultState() }
            return
        }

        viewModelScope.launch {
            _searchResultState.update { 
                it.copy(
                    isLoading = true,
                    query = query,
                    error = null
                )
            }
            
            culturalContextRepository.searchCulturalContexts(
                query = query,
                filters = _uiState.value.selectedFilters
            )
                .onSuccess { results ->
                    _searchResultState.update { 
                        it.copy(
                            isLoading = false,
                            results = results,
                            totalCount = results.size,
                            hasMore = false // 简化实现，实际可能需要分页
                        )
                    }
                }
                .onFailure { error ->
                    _searchResultState.update { 
                        it.copy(
                            isLoading = false,
                            error = error.message ?: "搜索失败"
                        )
                    }
                }
        }
    }

    /**
     * 更新搜索查询
     */
    fun updateSearchQuery(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
        
        // 实时搜索（防抖处理可以在实际项目中添加）
        if (query.length >= 2) {
            searchCulturalContexts(query)
        } else {
            _searchResultState.update { SearchResultState() }
        }
    }

    /**
     * 应用筛选条件
     */
    fun applyFilters(filters: CulturalFilters) {
        _uiState.update { it.copy(selectedFilters = filters) }
        
        // 重新筛选当前结果
        val currentContexts = _uiState.value.culturalContexts
        val filteredContexts = filterContexts(currentContexts, filters)
        
        _uiState.update { it.copy(culturalContexts = filteredContexts) }
    }

    /**
     * 收藏文化背景
     */
    fun bookmarkContext(contextId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isBookmarking = true,
                    lastAction = UserAction.Bookmark
                )
            }
            
            culturalContextRepository.bookmarkCulturalContext(contextId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            bookmarkedContexts = state.bookmarkedContexts + contextId
                        )
                    }
                }
                .onFailure { error ->
                    // 处理错误，可以显示Toast或Snackbar
                    _uiState.update { 
                        it.copy(error = error.message ?: "收藏失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isBookmarking = false, lastAction = null)
            }
        }
    }

    /**
     * 取消收藏文化背景
     */
    fun unbookmarkContext(contextId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(isBookmarking = true)
            }
            
            culturalContextRepository.unbookmarkCulturalContext(contextId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            bookmarkedContexts = state.bookmarkedContexts - contextId
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "取消收藏失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isBookmarking = false)
            }
        }
    }

    /**
     * 标记为已学习
     */
    fun markAsLearned(contextId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(
                    isMarkingAsLearned = true,
                    lastAction = UserAction.MarkAsLearned
                )
            }
            
            culturalContextRepository.markCulturalContextAsLearned(contextId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            learnedContexts = state.learnedContexts + contextId
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "标记学习状态失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isMarkingAsLearned = false, lastAction = null)
            }
        }
    }

    /**
     * 取消已学习标记
     */
    fun unmarkAsLearned(contextId: String) {
        viewModelScope.launch {
            _userInteractionState.update { 
                it.copy(isMarkingAsLearned = true)
            }
            
            culturalContextRepository.unmarkCulturalContextAsLearned(contextId)
                .onSuccess {
                    _uiState.update { state ->
                        state.copy(
                            learnedContexts = state.learnedContexts - contextId
                        )
                    }
                }
                .onFailure { error ->
                    _uiState.update { 
                        it.copy(error = error.message ?: "取消学习标记失败")
                    }
                }
            
            _userInteractionState.update { 
                it.copy(isMarkingAsLearned = false)
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
        _searchResultState.update { it.copy(error = null) }
    }

    /**
     * 重试加载
     */
    fun retry(word: String, sourceLanguage: String, targetLanguage: String) {
        loadCulturalContexts(word, sourceLanguage, targetLanguage)
    }

    /**
     * 获取学习进度统计
     */
    fun getLearningProgress(): StateFlow<LearningProgressState> {
        return _uiState.map { state ->
            val totalContexts = state.culturalContexts.size
            val learnedCount = state.learnedContexts.size
            val bookmarkedCount = state.bookmarkedContexts.size
            
            // 按难度统计进度
            val difficultyProgress = state.culturalContexts
                .filter { it.id in state.learnedContexts }
                .groupBy { it.difficulty }
                .mapValues { it.value.size }
            
            // 按地区统计进度
            val regionProgress = state.culturalContexts
                .filter { it.id in state.learnedContexts }
                .groupBy { it.region }
                .mapValues { it.value.size }
            
            LearningProgressState(
                totalContexts = totalContexts,
                learnedContexts = learnedCount,
                bookmarkedContexts = bookmarkedCount,
                difficultyProgress = difficultyProgress,
                regionProgress = regionProgress
            )
        }.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = LearningProgressState()
        )
    }

    // 私有辅助方法

    /**
     * 加载用户偏好设置（收藏和学习状态）
     */
    private suspend fun loadUserPreferences(contextIds: List<String>) {
        // 加载收藏状态
        culturalContextRepository.getBookmarkedContextIds()
            .onSuccess { bookmarkedIds ->
                val relevantBookmarks = bookmarkedIds.filter { it in contextIds }.toSet()
                _uiState.update { it.copy(bookmarkedContexts = relevantBookmarks) }
            }
        
        // 加载学习状态
        culturalContextRepository.getLearnedContextIds()
            .onSuccess { learnedIds ->
                val relevantLearned = learnedIds.filter { it in contextIds }.toSet()
                _uiState.update { it.copy(learnedContexts = relevantLearned) }
            }
    }

    /**
     * 根据筛选条件过滤文化背景
     */
    private fun filterContexts(
        contexts: List<CulturalContext>,
        filters: CulturalFilters
    ): List<CulturalContext> {
        return contexts.filter { context ->
            // 难度筛选
            (filters.difficulty == null || context.difficulty == filters.difficulty) &&
            // 地区筛选
            (filters.region == null || context.region == filters.region) &&
            // 知识类型筛选
            (filters.knowledgeType == null || context.knowledgeType == filters.knowledgeType) &&
            // 适用性筛选
            (filters.appropriateness == null || 
             context.usageContext.any { it.appropriateness == filters.appropriateness }) &&
            // 标签筛选
            (filters.tags.isEmpty() || 
             filters.tags.any { tag -> context.tags.contains(tag) })
        }
    }
}