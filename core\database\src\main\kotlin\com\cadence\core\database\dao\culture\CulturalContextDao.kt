package com.cadence.core.database.dao.culture

import androidx.room.*
import com.cadence.core.database.entity.culture.*
import kotlinx.coroutines.flow.Flow

/**
 * 文化背景上下文DAO
 */
@Dao
interface CulturalContextDao {
    
    // 基础CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCulturalContext(context: CulturalContextEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCulturalContexts(contexts: List<CulturalContextEntity>)
    
    @Update
    suspend fun updateCulturalContext(context: CulturalContextEntity)
    
    @Delete
    suspend fun deleteCulturalContext(context: CulturalContextEntity)
    
    @Query("DELETE FROM cultural_contexts WHERE id = :contextId")
    suspend fun deleteCulturalContextById(contextId: String)
    
    // 查询操作
    @Query("SELECT * FROM cultural_contexts WHERE id = :contextId")
    suspend fun getCulturalContextById(contextId: String): CulturalContextEntity?
    
    @Query("SELECT * FROM cultural_contexts WHERE word = :word")
    suspend fun getCulturalContextsByWord(word: String): List<CulturalContextEntity>
    
    @Query("""
        SELECT * FROM cultural_contexts 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
    """)
    suspend fun getCulturalContextsByLanguages(
        sourceLanguage: String,
        targetLanguage: String
    ): List<CulturalContextEntity>
    
    @Query("SELECT * FROM cultural_contexts WHERE region = :region")
    suspend fun getCulturalContextsByRegion(region: String): List<CulturalContextEntity>
    
    @Query("SELECT * FROM cultural_contexts WHERE difficulty = :difficulty")
    suspend fun getCulturalContextsByDifficulty(difficulty: String): List<CulturalContextEntity>
    
    @Query("""
        SELECT * FROM cultural_contexts 
        WHERE word LIKE '%' || :query || '%' 
        OR cultural_meaning LIKE '%' || :query || '%'
        OR historical_background LIKE '%' || :query || '%'
        ORDER BY created_at DESC
    """)
    suspend fun searchCulturalContexts(query: String): List<CulturalContextEntity>
    
    @Query("""
        SELECT * FROM cultural_contexts 
        ORDER BY created_at DESC 
        LIMIT :limit OFFSET :offset
    """)
    suspend fun getCulturalContextsPaged(limit: Int, offset: Int): List<CulturalContextEntity>
    
    // 完整信息查询
    @Transaction
    @Query("SELECT * FROM cultural_contexts WHERE id = :contextId")
    suspend fun getCulturalContextWithDetails(contextId: String): CulturalContextWithDetails?
    
    @Transaction
    @Query("SELECT * FROM cultural_contexts WHERE word = :word")
    suspend fun getCulturalContextsWithDetailsByWord(word: String): List<CulturalContextWithDetails>
    
    @Transaction
    @Query("""
        SELECT * FROM cultural_contexts 
        WHERE source_language = :sourceLanguage 
        AND target_language = :targetLanguage
        ORDER BY created_at DESC
    """)
    suspend fun getCulturalContextsWithDetailsByLanguages(
        sourceLanguage: String,
        targetLanguage: String
    ): List<CulturalContextWithDetails>
    
    // Flow查询（用于实时更新）
    @Query("SELECT * FROM cultural_contexts ORDER BY created_at DESC")
    fun getAllCulturalContextsFlow(): Flow<List<CulturalContextEntity>>
    
    @Query("""
        SELECT * FROM cultural_contexts 
        WHERE word LIKE '%' || :query || '%' 
        ORDER BY created_at DESC
    """)
    fun searchCulturalContextsFlow(query: String): Flow<List<CulturalContextEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM cultural_contexts")
    suspend fun getCulturalContextCount(): Int
    
    @Query("SELECT COUNT(*) FROM cultural_contexts WHERE region = :region")
    suspend fun getCulturalContextCountByRegion(region: String): Int
    
    @Query("SELECT COUNT(*) FROM cultural_contexts WHERE difficulty = :difficulty")
    suspend fun getCulturalContextCountByDifficulty(difficulty: String): Int
    
    @Query("""
        SELECT region, COUNT(*) as count 
        FROM cultural_contexts 
        GROUP BY region 
        ORDER BY count DESC
    """)
    suspend fun getCulturalContextCountByRegions(): List<RegionCount>
    
    @Query("""
        SELECT difficulty, COUNT(*) as count 
        FROM cultural_contexts 
        GROUP BY difficulty 
        ORDER BY count DESC
    """)
    suspend fun getCulturalContextCountByDifficulties(): List<DifficultyCount>
}

/**
 * 地区统计数据
 */
data class RegionCount(
    val region: String,
    val count: Int
)

/**
 * 难度统计数据
 */
data class DifficultyCount(
    val difficulty: String,
    val count: Int
)