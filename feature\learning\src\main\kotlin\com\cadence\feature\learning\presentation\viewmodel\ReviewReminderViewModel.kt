package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.LearningProgress
import com.cadence.domain.repository.LearningRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 复习提醒ViewModel
 * 管理复习提醒界面的状态和数据
 */
@HiltViewModel
class ReviewReminderViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ReviewReminderUiState())
    val uiState: StateFlow<ReviewReminderUiState> = _uiState.asStateFlow()
    
    private val _wordsToReview = MutableStateFlow<List<LearningProgress>>(emptyList())
    val wordsToReview: StateFlow<List<LearningProgress>> = _wordsToReview.asStateFlow()
    
    private val _reminderSettings = MutableStateFlow(ReminderSettings())
    val reminderSettings: StateFlow<ReminderSettings> = _reminderSettings.asStateFlow()
    
    private val _upcomingReviews = MutableStateFlow<List<LearningProgress>>(emptyList())
    val upcomingReviews: StateFlow<List<LearningProgress>> = _upcomingReviews.asStateFlow()
    
    /**
     * 加载复习数据
     */
    fun loadReviewData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val userId = "current_user"
                
                // 加载待复习单词
                val reviewWords = learningRepository.getScheduledReviews(userId)
                _wordsToReview.value = reviewWords.filter { it.needsReview }
                
                // 加载即将到期的复习（未来24小时内）
                val currentTime = System.currentTimeMillis()
                val next24Hours = currentTime + (24 * 60 * 60 * 1000L)
                val upcoming = reviewWords.filter { progress ->
                    progress.nextReviewAt?.let { nextReview ->
                        nextReview > currentTime && nextReview <= next24Hours
                    } ?: false
                }
                _upcomingReviews.value = upcoming
                
                // 加载提醒设置
                loadReminderSettings()
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载复习数据失败"
                )
            }
        }
    }
    
    /**
     * 刷新复习数据
     */
    fun refreshReviewData() {
        loadReviewData()
    }
    
    /**
     * 更新提醒设置
     */
    fun updateReminderSettings(settings: ReminderSettings) {
        viewModelScope.launch {
            try {
                _reminderSettings.value = settings
                saveReminderSettings(settings)
                
                // 如果启用了提醒，安排通知
                if (settings.enabled) {
                    scheduleReviewNotifications()
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "更新提醒设置失败"
                )
            }
        }
    }
    
    /**
     * 标记单词复习完成
     */
    fun markWordReviewed(wordId: String, success: Boolean) {
        viewModelScope.launch {
            try {
                learningRepository.markReviewCompleted("current_user", wordId, success)
                
                // 刷新数据
                refreshReviewData()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "标记复习完成失败"
                )
            }
        }
    }
    
    /**
     * 获取复习统计
     */
    fun getReviewStats(): ReviewStats {
        val words = _wordsToReview.value
        val overdue = words.count { progress ->
            progress.nextReviewAt?.let { it < System.currentTimeMillis() } ?: false
        }
        val today = words.size - overdue
        val upcoming = _upcomingReviews.value.size
        
        return ReviewStats(
            totalReviews = words.size,
            overdueReviews = overdue,
            todayReviews = today,
            upcomingReviews = upcoming
        )
    }
    
    /**
     * 加载提醒设置
     */
    private suspend fun loadReminderSettings() {
        // 从本地存储或服务器加载设置
        // 这里使用默认设置
        _reminderSettings.value = ReminderSettings(
            enabled = true,
            dailyReminderTime = "09:00",
            reminderDays = setOf(1, 2, 3, 4, 5, 6, 7), // 每天
            soundEnabled = true,
            vibrationEnabled = true
        )
    }
    
    /**
     * 保存提醒设置
     */
    private suspend fun saveReminderSettings(settings: ReminderSettings) {
        // 保存到本地存储或服务器
        // 简化实现
    }
    
    /**
     * 安排复习通知
     */
    private suspend fun scheduleReviewNotifications() {
        val settings = _reminderSettings.value
        if (!settings.enabled) return
        
        // 安排每日提醒通知
        // 这里需要与Android通知系统集成
        // 简化实现
    }
}

/**
 * 复习提醒界面UI状态
 */
data class ReviewReminderUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)

/**
 * 提醒设置
 */
data class ReminderSettings(
    val enabled: Boolean = true,
    val dailyReminderTime: String = "09:00", // HH:mm格式
    val reminderDays: Set<Int> = setOf(1, 2, 3, 4, 5, 6, 7), // 1=周一, 7=周日
    val soundEnabled: Boolean = true,
    val vibrationEnabled: Boolean = true,
    val advanceNotice: Int = 1 // 提前多少小时提醒
)

/**
 * 复习统计
 */
data class ReviewStats(
    val totalReviews: Int,
    val overdueReviews: Int,
    val todayReviews: Int,
    val upcomingReviews: Int
)