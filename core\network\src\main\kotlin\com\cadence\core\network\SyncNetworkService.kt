package com.cadence.core.network

import com.cadence.core.network.api.SyncApiService
import com.cadence.core.network.dto.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 同步网络服务实现
 * 封装同步API调用，提供数据同步功能
 */
@Singleton
class SyncNetworkService @Inject constructor(
    private val syncApiService: SyncApiService
) {

    /**
     * 处理网络异常的通用方法
     */
    private fun handleNetworkException(e: Exception, operation: String): NetworkResult.Error {
        return when (e) {
            is UnknownHostException -> {
                Timber.e(e, "$operation: 网络连接失败")
                NetworkResult.Error(NetworkError.NetworkConnectionError)
            }
            is SocketTimeoutException -> {
                Timber.e(e, "$operation: 网络请求超时")
                NetworkResult.Error(NetworkError.TimeoutError)
            }
            is IOException -> {
                Timber.e(e, "$operation: 网络IO异常")
                NetworkResult.Error(
                    exception = e,
                    message = "网络异常: ${e.message}"
                )
            }
            else -> {
                Timber.e(e, "$operation: 异常")
                NetworkResult.Error(
                    exception = e,
                    message = "$operation 失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 上传翻译历史
     */
    suspend fun uploadTranslationHistory(
        deviceId: String,
        userId: String?,
        translations: List<TranslationDto>,
        tags: List<TagDto>,
        translationTags: List<TranslationTagDto>,
        lastSyncTime: Long
    ): NetworkResult<TranslationHistorySyncResponse> = withContext(Dispatchers.IO) {
        try {
            val request = TranslationHistorySyncRequest(
                deviceId = deviceId,
                timestamp = System.currentTimeMillis(),
                userId = userId,
                translations = translations,
                tags = tags,
                translationTags = translationTags,
                lastSyncTime = lastSyncTime
            )

            val response = syncApiService.uploadTranslationHistory(request)

            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("上传翻译历史成功: ${translations.size} 条记录")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "上传失败"
                    Timber.e("上传翻译历史失败: $errorMsg")
                    NetworkResult.Error(
                        exception = Exception(errorMsg),
                        code = response.code()
                    )
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("上传翻译历史网络错误: $errorMsg")
                NetworkResult.Error(
                    exception = Exception(errorMsg),
                    code = response.code()
                )
            }
        } catch (e: Exception) {
            handleNetworkException(e, "上传翻译历史")
        }
    }
    
    /**
     * 下载翻译历史
     */
    suspend fun downloadTranslationHistory(
        deviceId: String,
        userId: String?,
        lastSyncTime: Long
    ): NetworkResult<TranslationHistorySyncResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.downloadTranslationHistory(
                deviceId = deviceId,
                userId = userId,
                lastSyncTime = lastSyncTime
            )
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("下载翻译历史成功: ${body.translations.size} 条记录")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "下载失败"
                    Timber.e("下载翻译历史失败: $errorMsg")
                    NetworkResult.Error(
                        exception = Exception(errorMsg),
                        code = response.code()
                    )
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("下载翻译历史网络错误: $errorMsg")
                NetworkResult.Error(
                    exception = Exception(errorMsg),
                    code = response.code()
                )
            }
        } catch (e: UnknownHostException) {
            Timber.e(e, "网络连接失败")
            NetworkResult.Error(NetworkError.NetworkConnectionError)
        } catch (e: SocketTimeoutException) {
            Timber.e(e, "网络请求超时")
            NetworkResult.Error(NetworkError.TimeoutError)
        } catch (e: IOException) {
            Timber.e(e, "网络IO异常")
            NetworkResult.Error(
                exception = e,
                message = "网络异常: ${e.message}"
            )
        } catch (e: Exception) {
            Timber.e(e, "下载翻译历史异常")
            NetworkResult.Error(
                exception = e,
                message = "下载失败: ${e.message}"
            )
        }
    }
    
    /**
     * 双向同步翻译历史
     */
    suspend fun syncTranslationHistory(
        deviceId: String,
        userId: String?,
        translations: List<TranslationDto>,
        tags: List<TagDto>,
        translationTags: List<TranslationTagDto>,
        lastSyncTime: Long
    ): NetworkResult<TranslationHistorySyncResponse> = withContext(Dispatchers.IO) {
        try {
            val request = TranslationHistorySyncRequest(
                deviceId = deviceId,
                timestamp = System.currentTimeMillis(),
                userId = userId,
                translations = translations,
                tags = tags,
                translationTags = translationTags,
                lastSyncTime = lastSyncTime
            )
            
            val response = syncApiService.syncTranslationHistory(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("同步翻译历史成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "同步失败"
                    Timber.e("同步翻译历史失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("同步翻译历史网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "同步翻译历史异常")
            NetworkResult.Error("同步失败: ${e.message}", -1)
        }
    }
    
    /**
     * 上传标签
     */
    suspend fun uploadTags(
        deviceId: String,
        userId: String?,
        tags: List<TagDto>
    ): NetworkResult<GenericSyncResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.uploadTags(
                deviceId = deviceId,
                userId = userId,
                tags = tags
            )
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("上传标签成功: ${tags.size} 个标签")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "上传失败"
                    Timber.e("上传标签失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("上传标签网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "上传标签异常")
            NetworkResult.Error("上传失败: ${e.message}", -1)
        }
    }
    
    /**
     * 下载标签
     */
    suspend fun downloadTags(
        deviceId: String,
        userId: String?,
        lastSyncTime: Long
    ): NetworkResult<List<TagDto>> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.downloadTags(
                deviceId = deviceId,
                userId = userId,
                lastSyncTime = lastSyncTime
            )
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    Timber.d("下载标签成功: ${body.size} 个标签")
                    NetworkResult.Success(body)
                } else {
                    Timber.e("下载标签失败: 响应体为空")
                    NetworkResult.Error("下载失败", response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("下载标签网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "下载标签异常")
            NetworkResult.Error("下载失败: ${e.message}", -1)
        }
    }
    
    /**
     * 上传用户偏好
     */
    suspend fun uploadUserPreferences(
        deviceId: String,
        userId: String?,
        preferences: UserPreferencesDto
    ): NetworkResult<UserPreferencesSyncResponse> = withContext(Dispatchers.IO) {
        try {
            val request = UserPreferencesSyncRequest(
                deviceId = deviceId,
                timestamp = System.currentTimeMillis(),
                userId = userId,
                preferences = preferences
            )
            
            val response = syncApiService.uploadUserPreferences(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("上传用户偏好成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "上传失败"
                    Timber.e("上传用户偏好失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("上传用户偏好网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "上传用户偏好异常")
            NetworkResult.Error("上传失败: ${e.message}", -1)
        }
    }
    
    /**
     * 下载用户偏好
     */
    suspend fun downloadUserPreferences(
        deviceId: String,
        userId: String?
    ): NetworkResult<UserPreferencesSyncResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.downloadUserPreferences(
                deviceId = deviceId,
                userId = userId
            )
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("下载用户偏好成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "下载失败"
                    Timber.e("下载用户偏好失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("下载用户偏好网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "下载用户偏好异常")
            NetworkResult.Error("下载失败: ${e.message}", -1)
        }
    }
    
    /**
     * 注册设备
     */
    suspend fun registerDevice(
        deviceId: String,
        userId: String?,
        deviceInfo: DeviceInfoDto
    ): NetworkResult<DeviceRegistrationResponse> = withContext(Dispatchers.IO) {
        try {
            val request = DeviceRegistrationRequest(
                deviceId = deviceId,
                timestamp = System.currentTimeMillis(),
                userId = userId,
                deviceInfo = deviceInfo
            )
            
            val response = syncApiService.registerDevice(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("注册设备成功: ${deviceInfo.deviceName}")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "注册失败"
                    Timber.e("注册设备失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("注册设备网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "注册设备异常")
            NetworkResult.Error("注册失败: ${e.message}", -1)
        }
    }
    
    /**
     * 获取设备列表
     */
    suspend fun getDeviceList(
        userId: String?
    ): NetworkResult<DeviceListResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.getDeviceList(userId)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("获取设备列表成功: ${body.devices.size} 个设备")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "获取失败"
                    Timber.e("获取设备列表失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("获取设备列表网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "获取设备列表异常")
            NetworkResult.Error("获取失败: ${e.message}", -1)
        }
    }
    
    /**
     * 获取同步状态
     */
    suspend fun getSyncStatus(
        deviceId: String,
        userId: String?
    ): NetworkResult<SyncStatusResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.getSyncStatus(deviceId, userId)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("获取同步状态成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "获取失败"
                    Timber.e("获取同步状态失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("获取同步状态网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "获取同步状态异常")
            NetworkResult.Error("获取失败: ${e.message}", -1)
        }
    }
    
    /**
     * 备份所有数据
     */
    suspend fun backupAllData(
        deviceId: String,
        userId: String?,
        backupData: BackupDataDto
    ): NetworkResult<GenericSyncResponse> = withContext(Dispatchers.IO) {
        try {
            val request = DataBackupRequest(
                deviceId = deviceId,
                timestamp = System.currentTimeMillis(),
                userId = userId,
                backupData = backupData
            )
            
            val response = syncApiService.backupAllData(request)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("备份数据成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "备份失败"
                    Timber.e("备份数据失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("备份数据网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "备份数据异常")
            NetworkResult.Error("备份失败: ${e.message}", -1)
        }
    }
    
    /**
     * 恢复所有数据
     */
    suspend fun restoreAllData(
        deviceId: String,
        userId: String?,
        backupTime: Long? = null
    ): NetworkResult<DataRestoreResponse> = withContext(Dispatchers.IO) {
        try {
            val response = syncApiService.restoreAllData(
                deviceId = deviceId,
                userId = userId,
                backupTime = backupTime
            )
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null && body.success) {
                    Timber.d("恢复数据成功")
                    NetworkResult.Success(body)
                } else {
                    val errorMsg = body?.message ?: "恢复失败"
                    Timber.e("恢复数据失败: $errorMsg")
                    NetworkResult.Error(errorMsg, response.code())
                }
            } else {
                val errorMsg = "HTTP ${response.code()}: ${response.message()}"
                Timber.e("恢复数据网络错误: $errorMsg")
                NetworkResult.Error(errorMsg, response.code())
            }
        } catch (e: Exception) {
            Timber.e(e, "恢复数据异常")
            NetworkResult.Error("恢复失败: ${e.message}", -1)
        }
    }
}
