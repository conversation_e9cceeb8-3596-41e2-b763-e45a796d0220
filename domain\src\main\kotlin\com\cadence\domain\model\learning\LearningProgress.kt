package com.cadence.domain.model.learning

/**
 * 学习进度领域模型
 * 跟踪用户对特定单词的学习进度
 */
data class LearningProgress(
    val id: String,
    val userId: String,
    val wordId: String,
    val masteryLevel: MasteryLevel,
    val correctAnswers: Int = 0,
    val totalAttempts: Int = 0,
    val lastStudiedAt: Long? = null,
    val nextReviewAt: Long? = null,
    val studyStreak: Int = 0,
    val isBookmarked: Boolean = false,
    val createdAt: Long,
    val updatedAt: Long
) {
    /**
     * 计算正确率
     */
    val accuracyRate: Float
        get() = if (totalAttempts > 0) correctAnswers.toFloat() / totalAttempts else 0f
} {
    /**
     * 计算正确率
     */
    val accuracyRate: Float
        get() = if (totalAttempts > 0) correctAnswers.toFloat() / totalAttempts else 0f
    
    /**
     * 是否需要复习
     */
    val needsReview: Boolean
        get() = nextReviewAt?.let { it <= System.currentTimeMillis() } ?: true
}

/**
 * 掌握程度等级
 */
enum class MasteryLevel(val displayName: String, val level: Int, val reviewInterval: Long) {
    NEW("新学", 0, 0L),
    LEARNING("学习中", 1, 1 * 24 * 60 * 60 * 1000L), // 1天
    FAMILIAR("熟悉", 2, 3 * 24 * 60 * 60 * 1000L), // 3天
    KNOWN("已知", 3, 7 * 24 * 60 * 60 * 1000L), // 7天
    MASTERED("掌握", 4, 30 * 24 * 60 * 60 * 1000L) // 30天
}