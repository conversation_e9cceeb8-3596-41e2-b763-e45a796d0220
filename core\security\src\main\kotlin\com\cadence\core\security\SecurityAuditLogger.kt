package com.cadence.core.security

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ConcurrentLinkedQueue
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 安全审计日志管理器
 * 负责记录和管理安全相关的审计日志
 */
@Singleton
class SecurityAuditLogger @Inject constructor(
    private val context: Context,
    private val cryptoManager: CryptoManager
) {
    
    companion object {
        private const val AUDIT_LOG_DIR = "audit_logs"
        private const val MAX_LOG_FILE_SIZE = 5 * 1024 * 1024 // 5MB
        private const val MAX_LOG_FILES = 10
        private const val LOG_RETENTION_DAYS = 30
        private const val BATCH_SIZE = 50
        private const val FLUSH_INTERVAL_MS = 30000L // 30秒
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    // 日志队列
    private val logQueue = ConcurrentLinkedQueue<AuditLogEntry>()
    
    // 审计状态
    private val _auditState = MutableStateFlow(AuditState())
    val auditState: StateFlow<AuditState> = _auditState.asStateFlow()
    
    // 日志文件管理
    private val logDirectory: File by lazy {
        File(context.filesDir, AUDIT_LOG_DIR).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    private var currentLogFile: File? = null
    private var flushJob: Job? = null
    
    init {
        startAuditLogging()
    }
    
    /**
     * 启动审计日志记录
     */
    private fun startAuditLogging() {
        currentLogFile = createNewLogFile()
        
        // 启动定期刷新任务
        flushJob = scope.launch {
            while (isActive) {
                try {
                    flushLogs()
                    delay(FLUSH_INTERVAL_MS)
                } catch (e: Exception) {
                    Timber.e(e, "审计日志刷新失败")
                }
            }
        }
        
        // 记录审计系统启动
        logSecurityEvent(
            event = "audit_system_started",
            severity = AuditSeverity.INFO,
            details = mapOf("timestamp" to System.currentTimeMillis().toString())
        )
    }
    
    /**
     * 记录安全事件
     */
    fun logSecurityEvent(
        event: String,
        severity: AuditSeverity = AuditSeverity.INFO,
        details: Map<String, String> = emptyMap(),
        userId: String? = null
    ) {
        val logEntry = AuditLogEntry(
            timestamp = System.currentTimeMillis(),
            eventType = AuditEventType.SECURITY,
            event = event,
            severity = severity,
            details = details,
            userId = userId,
            sessionId = getCurrentSessionId(),
            deviceId = getDeviceId(),
            appVersion = getAppVersion()
        )
        
        addLogEntry(logEntry)
    }
    
    /**
     * 记录认证事件
     */
    fun logAuthenticationEvent(
        event: String,
        success: Boolean,
        userId: String? = null,
        details: Map<String, String> = emptyMap()
    ) {
        val severity = if (success) AuditSeverity.INFO else AuditSeverity.WARNING
        
        val logEntry = AuditLogEntry(
            timestamp = System.currentTimeMillis(),
            eventType = AuditEventType.AUTHENTICATION,
            event = event,
            severity = severity,
            details = details + mapOf("success" to success.toString()),
            userId = userId,
            sessionId = getCurrentSessionId(),
            deviceId = getDeviceId(),
            appVersion = getAppVersion()
        )
        
        addLogEntry(logEntry)
    }
    
    /**
     * 记录数据访问事件
     */
    fun logDataAccessEvent(
        event: String,
        dataType: String,
        operation: String,
        userId: String? = null,
        details: Map<String, String> = emptyMap()
    ) {
        val logEntry = AuditLogEntry(
            timestamp = System.currentTimeMillis(),
            eventType = AuditEventType.DATA_ACCESS,
            event = event,
            severity = AuditSeverity.INFO,
            details = details + mapOf(
                "data_type" to dataType,
                "operation" to operation
            ),
            userId = userId,
            sessionId = getCurrentSessionId(),
            deviceId = getDeviceId(),
            appVersion = getAppVersion()
        )
        
        addLogEntry(logEntry)
    }
    
    /**
     * 记录隐私事件
     */
    fun logPrivacyEvent(
        event: String,
        details: Map<String, String> = emptyMap(),
        userId: String? = null
    ) {
        val logEntry = AuditLogEntry(
            timestamp = System.currentTimeMillis(),
            eventType = AuditEventType.PRIVACY,
            event = event,
            severity = AuditSeverity.INFO,
            details = details,
            userId = userId,
            sessionId = getCurrentSessionId(),
            deviceId = getDeviceId(),
            appVersion = getAppVersion()
        )
        
        addLogEntry(logEntry)
    }
    
    /**
     * 记录网络事件
     */
    fun logNetworkEvent(
        event: String,
        url: String,
        method: String,
        statusCode: Int,
        details: Map<String, String> = emptyMap()
    ) {
        val severity = when (statusCode) {
            in 200..299 -> AuditSeverity.INFO
            in 400..499 -> AuditSeverity.WARNING
            in 500..599 -> AuditSeverity.ERROR
            else -> AuditSeverity.INFO
        }
        
        val logEntry = AuditLogEntry(
            timestamp = System.currentTimeMillis(),
            eventType = AuditEventType.NETWORK,
            event = event,
            severity = severity,
            details = details + mapOf(
                "url" to sanitizeUrl(url),
                "method" to method,
                "status_code" to statusCode.toString()
            ),
            userId = null,
            sessionId = getCurrentSessionId(),
            deviceId = getDeviceId(),
            appVersion = getAppVersion()
        )
        
        addLogEntry(logEntry)
    }
    
    /**
     * 添加日志条目到队列
     */
    private fun addLogEntry(logEntry: AuditLogEntry) {
        logQueue.offer(logEntry)
        
        // 更新审计状态
        val currentState = _auditState.value
        _auditState.value = currentState.copy(
            totalEvents = currentState.totalEvents + 1,
            lastEventTime = logEntry.timestamp,
            queueSize = logQueue.size
        )
        
        // 如果是高严重性事件，立即刷新
        if (logEntry.severity == AuditSeverity.ERROR || logEntry.severity == AuditSeverity.CRITICAL) {
            scope.launch {
                flushLogs()
            }
        }
    }
    
    /**
     * 刷新日志到文件
     */
    private suspend fun flushLogs() = withContext(Dispatchers.IO) {
        if (logQueue.isEmpty()) return@withContext
        
        val logsToFlush = mutableListOf<AuditLogEntry>()
        repeat(minOf(BATCH_SIZE, logQueue.size)) {
            logQueue.poll()?.let { logsToFlush.add(it) }
        }
        
        if (logsToFlush.isNotEmpty()) {
            writeLogsToFile(logsToFlush)
            
            // 更新审计状态
            val currentState = _auditState.value
            _auditState.value = currentState.copy(
                queueSize = logQueue.size,
                lastFlushTime = System.currentTimeMillis()
            )
        }
    }
    
    /**
     * 写入日志到文件
     */
    private suspend fun writeLogsToFile(logs: List<AuditLogEntry>) {
        try {
            val logFile = getCurrentLogFile()
            
            val logContent = logs.joinToString("\n") { entry ->
                formatLogEntry(entry)
            } + "\n"
            
            // 加密日志内容
            val encryptedContent = cryptoManager.encrypt(logContent)
            val encryptedData = "${encryptedContent.encryptedData.joinToString(",")};${encryptedContent.iv.joinToString(",")}"
            
            logFile.appendText(encryptedData + "\n")
            
            // 检查文件大小，如果超过限制则创建新文件
            if (logFile.length() > MAX_LOG_FILE_SIZE) {
                currentLogFile = createNewLogFile()
                cleanupOldLogFiles()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "写入审计日志失败")
        }
    }
    
    /**
     * 格式化日志条目
     */
    private fun formatLogEntry(entry: AuditLogEntry): String {
        return buildString {
            append("[${dateFormat.format(Date(entry.timestamp))}] ")
            append("[${entry.severity.name}] ")
            append("[${entry.eventType.name}] ")
            append("${entry.event} ")
            
            if (entry.userId != null) {
                append("user=${entry.userId} ")
            }
            
            append("session=${entry.sessionId} ")
            append("device=${entry.deviceId} ")
            append("version=${entry.appVersion}")
            
            if (entry.details.isNotEmpty()) {
                append(" details={")
                append(entry.details.entries.joinToString(", ") { "${it.key}=${it.value}" })
                append("}")
            }
        }
    }
    
    /**
     * 获取当前日志文件
     */
    private fun getCurrentLogFile(): File {
        return currentLogFile ?: createNewLogFile().also { currentLogFile = it }
    }
    
    /**
     * 创建新的日志文件
     */
    private fun createNewLogFile(): File {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return File(logDirectory, "audit_$timestamp.log")
    }
    
    /**
     * 清理旧的日志文件
     */
    private fun cleanupOldLogFiles() {
        try {
            val logFiles = logDirectory.listFiles { file ->
                file.name.startsWith("audit_") && file.name.endsWith(".log")
            }?.sortedByDescending { it.lastModified() }
            
            // 删除超过数量限制的文件
            logFiles?.drop(MAX_LOG_FILES)?.forEach { file ->
                file.delete()
                Timber.d("删除旧日志文件: ${file.name}")
            }
            
            // 删除超过保留期的文件
            val retentionTime = System.currentTimeMillis() - (LOG_RETENTION_DAYS * 24 * 60 * 60 * 1000L)
            logFiles?.filter { it.lastModified() < retentionTime }?.forEach { file ->
                file.delete()
                Timber.d("删除过期日志文件: ${file.name}")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "清理旧日志文件失败")
        }
    }
    
    /**
     * 获取当前会话ID
     */
    private fun getCurrentSessionId(): String {
        // 这里应该从会话管理器获取当前会话ID
        return "session_${System.currentTimeMillis()}"
    }
    
    /**
     * 获取设备ID
     */
    private fun getDeviceId(): String {
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown"
    }
    
    /**
     * 获取应用版本
     */
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            "${packageInfo.versionName}-${packageInfo.versionCode}"
        } catch (e: Exception) {
            "unknown"
        }
    }
    
    /**
     * 清理URL中的敏感信息
     */
    private fun sanitizeUrl(url: String): String {
        return url.replace(Regex("key=[^&]+"), "key=***")
            .replace(Regex("token=[^&]+"), "token=***")
            .replace(Regex("password=[^&]+"), "password=***")
    }
    
    /**
     * 获取审计报告
     */
    suspend fun getAuditReport(startTime: Long, endTime: Long): AuditReport = withContext(Dispatchers.IO) {
        // 这里应该实现从日志文件中读取和分析审计数据的逻辑
        // 由于实现复杂，这里返回一个简化的报告
        
        AuditReport(
            startTime = startTime,
            endTime = endTime,
            totalEvents = _auditState.value.totalEvents,
            securityEvents = 0, // 需要从文件中统计
            authenticationEvents = 0,
            dataAccessEvents = 0,
            privacyEvents = 0,
            networkEvents = 0,
            criticalEvents = 0,
            errorEvents = 0,
            warningEvents = 0
        )
    }
    
    /**
     * 停止审计日志记录
     */
    fun stopAuditLogging() {
        flushJob?.cancel()
        
        scope.launch {
            flushLogs() // 最后一次刷新
            
            logSecurityEvent(
                event = "audit_system_stopped",
                severity = AuditSeverity.INFO,
                details = mapOf("timestamp" to System.currentTimeMillis().toString())
            )
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopAuditLogging()
        scope.cancel()
    }
}

/**
 * 审计日志条目
 */
data class AuditLogEntry(
    val timestamp: Long,
    val eventType: AuditEventType,
    val event: String,
    val severity: AuditSeverity,
    val details: Map<String, String>,
    val userId: String?,
    val sessionId: String,
    val deviceId: String,
    val appVersion: String
)

/**
 * 审计事件类型
 */
enum class AuditEventType {
    SECURITY, AUTHENTICATION, DATA_ACCESS, PRIVACY, NETWORK, SYSTEM
}

/**
 * 审计严重性级别
 */
enum class AuditSeverity {
    INFO, WARNING, ERROR, CRITICAL
}

/**
 * 审计状态
 */
data class AuditState(
    val totalEvents: Long = 0,
    val lastEventTime: Long = 0,
    val lastFlushTime: Long = 0,
    val queueSize: Int = 0
)

/**
 * 审计报告
 */
data class AuditReport(
    val startTime: Long,
    val endTime: Long,
    val totalEvents: Long,
    val securityEvents: Long,
    val authenticationEvents: Long,
    val dataAccessEvents: Long,
    val privacyEvents: Long,
    val networkEvents: Long,
    val criticalEvents: Long,
    val errorEvents: Long,
    val warningEvents: Long
)
