package com.cadence.feature.ocr.data

import android.graphics.Bitmap
import android.graphics.Rect
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.TextRecognizer
import com.google.mlkit.vision.text.chinese.ChineseTextRecognizerOptions
import com.google.mlkit.vision.text.devanagari.DevanagariTextRecognizerOptions
import com.google.mlkit.vision.text.japanese.JapaneseTextRecognizerOptions
import com.google.mlkit.vision.text.korean.KoreanTextRecognizerOptions
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.suspendCancellableCoroutine
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

/**
 * OCR引擎
 * 基于Google ML Kit实现多语言文字识别
 */
@Singleton
class OcrEngine @Inject constructor() {
    
    // 不同语言的文字识别器
    private val latinRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    }
    
    private val chineseRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(ChineseTextRecognizerOptions.Builder().build())
    }
    
    private val japaneseRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(JapaneseTextRecognizerOptions.Builder().build())
    }
    
    private val koreanRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(KoreanTextRecognizerOptions.Builder().build())
    }
    
    private val devanagariRecognizer: TextRecognizer by lazy {
        TextRecognition.getClient(DevanagariTextRecognizerOptions.Builder().build())
    }
    
    /**
     * OCR识别结果
     */
    data class OcrResult(
        val text: String,
        val confidence: Float,
        val textBlocks: List<TextBlock>,
        val processingTimeMs: Long,
        val language: String
    ) {
        val isEmpty: Boolean get() = text.isBlank()
        val hasHighConfidence: Boolean get() = confidence >= 0.8f
    }
    
    /**
     * 文字块
     */
    data class TextBlock(
        val text: String,
        val confidence: Float,
        val boundingBox: Rect,
        val lines: List<TextLine>,
        val recognizedLanguage: String?
    )
    
    /**
     * 文字行
     */
    data class TextLine(
        val text: String,
        val confidence: Float,
        val boundingBox: Rect,
        val elements: List<TextElement>
    )
    
    /**
     * 文字元素
     */
    data class TextElement(
        val text: String,
        val confidence: Float,
        val boundingBox: Rect
    )
    
    /**
     * 支持的语言类型
     */
    enum class LanguageType {
        LATIN,      // 拉丁文（英语、法语、德语等）
        CHINESE,    // 中文
        JAPANESE,   // 日语
        KOREAN,     // 韩语
        DEVANAGARI, // 梵文（印地语等）
        AUTO        // 自动检测
    }
    
    /**
     * 识别图片中的文字
     */
    suspend fun recognizeText(
        bitmap: Bitmap,
        languageType: LanguageType = LanguageType.AUTO,
        rotationDegrees: Int = 0
    ): Flow<OcrResult> = flow {
        val startTime = System.currentTimeMillis()
        
        try {
            val inputImage = InputImage.fromBitmap(bitmap, rotationDegrees)
            
            when (languageType) {
                LanguageType.AUTO -> {
                    // 自动检测：并行运行多个识别器，选择最佳结果
                    val results = runMultipleRecognizers(inputImage)
                    val bestResult = selectBestResult(results, startTime)
                    emit(bestResult)
                }
                else -> {
                    val recognizer = getRecognizerForLanguage(languageType)
                    val result = recognizeWithRecognizer(recognizer, inputImage, languageType.name, startTime)
                    emit(result)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "OCR识别失败")
            emit(createErrorResult(e, startTime))
        }
    }
    
    /**
     * 运行多个识别器
     */
    private suspend fun runMultipleRecognizers(inputImage: InputImage): List<OcrResult> {
        val results = mutableListOf<OcrResult>()
        val startTime = System.currentTimeMillis()
        
        try {
            // 并行运行所有识别器
            val recognizers = listOf(
                latinRecognizer to "LATIN",
                chineseRecognizer to "CHINESE",
                japaneseRecognizer to "JAPANESE",
                koreanRecognizer to "KOREAN",
                devanagariRecognizer to "DEVANAGARI"
            )
            
            recognizers.forEach { (recognizer, language) ->
                try {
                    val result = recognizeWithRecognizer(recognizer, inputImage, language, startTime)
                    if (!result.isEmpty) {
                        results.add(result)
                    }
                } catch (e: Exception) {
                    Timber.w(e, "识别器 $language 失败")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "多识别器运行失败")
        }
        
        return results
    }
    
    /**
     * 选择最佳识别结果
     */
    private fun selectBestResult(results: List<OcrResult>, startTime: Long): OcrResult {
        if (results.isEmpty()) {
            return createEmptyResult(startTime)
        }
        
        // 根据置信度和文字长度选择最佳结果
        return results.maxByOrNull { result ->
            val confidenceScore = result.confidence * 0.7f
            val lengthScore = (result.text.length / 100f).coerceAtMost(1f) * 0.3f
            confidenceScore + lengthScore
        } ?: results.first()
    }
    
    /**
     * 使用指定识别器进行识别
     */
    private suspend fun recognizeWithRecognizer(
        recognizer: TextRecognizer,
        inputImage: InputImage,
        language: String,
        startTime: Long
    ): OcrResult = suspendCancellableCoroutine { continuation ->
        recognizer.process(inputImage)
            .addOnSuccessListener { visionText ->
                val result = convertToOcrResult(visionText, language, startTime)
                continuation.resume(result)
            }
            .addOnFailureListener { e ->
                Timber.e(e, "文字识别失败: $language")
                continuation.resume(createErrorResult(e, startTime))
            }
    }
    
    /**
     * 转换ML Kit结果为OCR结果
     */
    private fun convertToOcrResult(visionText: Text, language: String, startTime: Long): OcrResult {
        val processingTime = System.currentTimeMillis() - startTime
        
        val textBlocks = visionText.textBlocks.map { block ->
            TextBlock(
                text = block.text,
                confidence = calculateBlockConfidence(block),
                boundingBox = block.boundingBox ?: Rect(),
                lines = block.lines.map { line ->
                    TextLine(
                        text = line.text,
                        confidence = calculateLineConfidence(line),
                        boundingBox = line.boundingBox ?: Rect(),
                        elements = line.elements.map { element ->
                            TextElement(
                                text = element.text,
                                confidence = calculateElementConfidence(element),
                                boundingBox = element.boundingBox ?: Rect()
                            )
                        }
                    )
                },
                recognizedLanguage = block.recognizedLanguage
            )
        }
        
        val fullText = visionText.text
        val overallConfidence = calculateOverallConfidence(textBlocks)
        
        return OcrResult(
            text = fullText,
            confidence = overallConfidence,
            textBlocks = textBlocks,
            processingTimeMs = processingTime,
            language = language
        )
    }
    
    /**
     * 计算文字块置信度
     */
    private fun calculateBlockConfidence(block: Text.TextBlock): Float {
        if (block.lines.isEmpty()) return 0f
        
        val lineConfidences = block.lines.map { calculateLineConfidence(it) }
        return lineConfidences.average().toFloat()
    }
    
    /**
     * 计算文字行置信度
     */
    private fun calculateLineConfidence(line: Text.Line): Float {
        if (line.elements.isEmpty()) return 0f
        
        val elementConfidences = line.elements.map { calculateElementConfidence(it) }
        return elementConfidences.average().toFloat()
    }
    
    /**
     * 计算文字元素置信度
     */
    private fun calculateElementConfidence(element: Text.Element): Float {
        // ML Kit没有直接提供置信度，根据文字特征估算
        val text = element.text
        
        return when {
            text.isBlank() -> 0f
            text.length == 1 && !text.first().isLetterOrDigit() -> 0.3f // 单个符号
            text.all { it.isDigit() } -> 0.9f // 纯数字
            text.all { it.isLetter() } -> 0.8f // 纯字母
            text.any { it.isLetterOrDigit() } -> 0.7f // 混合字符
            else -> 0.5f // 其他情况
        }
    }
    
    /**
     * 计算整体置信度
     */
    private fun calculateOverallConfidence(textBlocks: List<TextBlock>): Float {
        if (textBlocks.isEmpty()) return 0f
        
        val blockConfidences = textBlocks.map { it.confidence }
        return blockConfidences.average().toFloat()
    }
    
    /**
     * 获取指定语言的识别器
     */
    private fun getRecognizerForLanguage(languageType: LanguageType): TextRecognizer {
        return when (languageType) {
            LanguageType.LATIN -> latinRecognizer
            LanguageType.CHINESE -> chineseRecognizer
            LanguageType.JAPANESE -> japaneseRecognizer
            LanguageType.KOREAN -> koreanRecognizer
            LanguageType.DEVANAGARI -> devanagariRecognizer
            LanguageType.AUTO -> latinRecognizer // 默认使用拉丁文识别器
        }
    }
    
    /**
     * 创建空结果
     */
    private fun createEmptyResult(startTime: Long): OcrResult {
        return OcrResult(
            text = "",
            confidence = 0f,
            textBlocks = emptyList(),
            processingTimeMs = System.currentTimeMillis() - startTime,
            language = "UNKNOWN"
        )
    }
    
    /**
     * 创建错误结果
     */
    private fun createErrorResult(exception: Throwable, startTime: Long): OcrResult {
        return OcrResult(
            text = "",
            confidence = 0f,
            textBlocks = emptyList(),
            processingTimeMs = System.currentTimeMillis() - startTime,
            language = "ERROR"
        )
    }
    
    /**
     * 检测图片中的主要语言
     */
    suspend fun detectLanguage(bitmap: Bitmap): LanguageType {
        return try {
            val inputImage = InputImage.fromBitmap(bitmap, 0)
            val results = runMultipleRecognizers(inputImage)
            
            if (results.isEmpty()) {
                LanguageType.LATIN
            } else {
                val bestResult = selectBestResult(results, System.currentTimeMillis())
                LanguageType.valueOf(bestResult.language)
            }
        } catch (e: Exception) {
            Timber.e(e, "语言检测失败")
            LanguageType.LATIN
        }
    }
    
    /**
     * 释放资源
     */
    fun release() {
        try {
            latinRecognizer.close()
            chineseRecognizer.close()
            japaneseRecognizer.close()
            koreanRecognizer.close()
            devanagariRecognizer.close()
            Timber.d("OCR引擎资源已释放")
        } catch (e: Exception) {
            Timber.e(e, "释放OCR引擎资源失败")
        }
    }
}
