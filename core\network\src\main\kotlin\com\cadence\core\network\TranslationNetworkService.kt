package com.cadence.core.network

import com.cadence.core.network.api.GeminiApiService
import com.cadence.core.network.dto.*
import com.cadence.core.translation.RegionalTranslationEngine
import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import com.cadence.domain.model.TranslationRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 翻译网络服务实现
 * 封装Gemini API调用，提供翻译、语言检测等功能
 */
@Singleton
class TranslationNetworkService @Inject constructor(
    private val geminiApiService: GeminiApiService,
    private val regionalTranslationEngine: RegionalTranslationEngine
) {
    
    /**
     * 执行翻译
     */
    suspend fun translate(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String? = null,
        targetRegion: String? = null,
        includeCulturalContext: Boolean = false
    ): NetworkResult<TranslationResult> = withContext(Dispatchers.IO) {
        try {
            // 创建翻译请求对象
            val translationRequest = TranslationRequest(
                text = text,
                sourceLanguage = Language(sourceLanguage, getLanguageName(sourceLanguage)),
                targetLanguage = Language(targetLanguage, getLanguageName(targetLanguage)),
                sourceRegion = sourceRegion?.let { Region(it, getRegionName(it)) },
                targetRegion = targetRegion?.let { Region(it, getRegionName(it)) },
                includeCulturalContext = includeCulturalContext
            )

            // 使用区域翻译引擎增强请求
            val enhancedRequest = regionalTranslationEngine.enhanceTranslationRequest(translationRequest)
            val prompt = regionalTranslationEngine.buildRegionalPrompt(enhancedRequest)
            
            val request = GeminiTranslationRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                ),
                generationConfig = GenerationConfig(
                    temperature = 0.1f,
                    topK = 1,
                    topP = 0.8f,
                    maxOutputTokens = 2048
                )
            )
            
            val response = geminiApiService.generateContent(
                model = "gemini-pro",
                request = request
            )
            
            if (response.isSuccessful) {
                val geminiResponse = response.body()
                if (geminiResponse?.error != null) {
                    NetworkResult.Error(
                        exception = Exception(geminiResponse.error.message),
                        code = geminiResponse.error.code
                    )
                } else {
                    val translatedText = extractTranslationFromResponse(geminiResponse)
                    if (translatedText.isNotBlank()) {
                        NetworkResult.Success(
                            TranslationResult(
                                translatedText = translatedText,
                                sourceLanguage = sourceLanguage,
                                targetLanguage = targetLanguage,
                                confidence = 0.9f // Gemini通常有较高的置信度
                            )
                        )
                    } else {
                        NetworkResult.Error(
                            exception = Exception("翻译结果为空"),
                            message = "API返回了空的翻译结果"
                        )
                    }
                }
            } else {
                handleHttpError(response.code(), response.message())
            }
        } catch (e: Exception) {
            handleException(e)
        }
    }
    
    /**
     * 检测语言
     */
    suspend fun detectLanguage(text: String): NetworkResult<LanguageDetectionResult> = 
        withContext(Dispatchers.IO) {
            try {
                val prompt = "请检测以下文本的语言，只返回语言代码（如zh、en、ja等）：\n\n$text"
                
                val request = GeminiTranslationRequest(
                    contents = listOf(
                        Content(
                            parts = listOf(Part(text = prompt))
                        )
                    ),
                    generationConfig = GenerationConfig(
                        temperature = 0.0f,
                        maxOutputTokens = 10
                    )
                )
                
                val response = geminiApiService.generateContent(request = request)
                
                if (response.isSuccessful) {
                    val detectedLanguage = extractLanguageFromResponse(response.body())
                    if (detectedLanguage.isNotBlank()) {
                        NetworkResult.Success(
                            LanguageDetectionResult(
                                detectedLanguage = detectedLanguage,
                                confidence = 0.85f,
                                isReliable = true
                            )
                        )
                    } else {
                        NetworkResult.Error(
                            exception = Exception("语言检测失败"),
                            message = "无法检测文本语言"
                        )
                    }
                } else {
                    handleHttpError(response.code(), response.message())
                }
            } catch (e: Exception) {
                handleException(e)
            }
        }
    
    /**
     * 获取文化背景解释
     */
    suspend fun getCulturalContext(
        text: String,
        sourceLanguage: String,
        targetLanguage: String
    ): NetworkResult<String> = withContext(Dispatchers.IO) {
        try {
            val prompt = """
                请解释以下文本在从${sourceLanguage}翻译到${targetLanguage}时的文化背景和含义差异：
                
                原文：$text
                
                请提供：
                1. 文化背景解释
                2. 使用场景说明
                3. 可能的理解差异
                
                请用中文回答，保持简洁明了。
            """.trimIndent()
            
            val request = CulturalContextRequest(
                contents = listOf(
                    Content(
                        parts = listOf(Part(text = prompt))
                    )
                )
            )
            
            val response = geminiApiService.generateContent(request = request)
            
            if (response.isSuccessful) {
                val context = extractTextFromResponse(response.body())
                if (context.isNotBlank()) {
                    NetworkResult.Success(context)
                } else {
                    NetworkResult.Error(
                        exception = Exception("文化背景解释为空"),
                        message = "无法获取文化背景信息"
                    )
                }
            } else {
                handleHttpError(response.code(), response.message())
            }
        } catch (e: Exception) {
            handleException(e)
        }
    }
    
    /**
     * 构建翻译提示词
     */
    private fun buildTranslationPrompt(
        text: String,
        sourceLanguage: String,
        targetLanguage: String,
        sourceRegion: String?,
        targetRegion: String?,
        includeCulturalContext: Boolean
    ): String {
        val regionInfo = buildString {
            if (sourceRegion != null) append("（${sourceRegion}地区）")
            if (targetRegion != null) append("翻译为${targetLanguage}（${targetRegion}地区）")
        }
        
        return buildString {
            append("请将以下${sourceLanguage}${regionInfo}文本翻译为${targetLanguage}：\n\n")
            append(text)
            append("\n\n要求：")
            append("1. 保持原文的语气和风格")
            append("2. 考虑地区语言特色")
            if (includeCulturalContext) {
                append("3. 如有必要，请在翻译后添加文化背景说明")
            }
            append("\n\n请只返回翻译结果，不要包含其他解释。")
        }
    }
    
    /**
     * 从响应中提取翻译结果
     */
    private fun extractTranslationFromResponse(response: GeminiResponse?): String {
        return response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
    }
    
    /**
     * 从响应中提取语言代码
     */
    private fun extractLanguageFromResponse(response: GeminiResponse?): String {
        val text = response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
        // 提取语言代码（通常是2-3个字符）
        return text.lowercase().take(3)
    }
    
    /**
     * 从响应中提取文本内容
     */
    private fun extractTextFromResponse(response: GeminiResponse?): String {
        return response?.candidates?.firstOrNull()?.content?.parts?.firstOrNull()?.text?.trim() ?: ""
    }
    
    /**
     * 处理HTTP错误
     */
    private fun handleHttpError(code: Int, message: String): NetworkResult.Error {
        Timber.e("HTTP错误: $code - $message")
        return when (code) {
            401 -> NetworkResult.Error(NetworkError.ApiKeyError)
            429 -> NetworkResult.Error(NetworkError.RateLimitError)
            in 500..599 -> NetworkResult.Error(NetworkError.ServerError(code, message))
            else -> NetworkResult.Error(NetworkError.UnknownError("HTTP错误: $code"))
        }
    }
    
    /**
     * 处理异常
     */
    private fun handleException(exception: Exception): NetworkResult.Error {
        Timber.e(exception, "网络请求异常")
        return when (exception) {
            is UnknownHostException -> NetworkResult.Error(NetworkError.NetworkConnectionError)
            is SocketTimeoutException -> NetworkResult.Error(NetworkError.TimeoutError)
            is IOException -> NetworkResult.Error(NetworkError.NetworkConnectionError)
            else -> NetworkResult.Error(NetworkError.UnknownError(exception.message ?: "未知错误"))
        }
    }

    /**
     * 获取语言名称
     */
    private fun getLanguageName(languageCode: String): String {
        return when (languageCode) {
            "zh" -> "中文"
            "en" -> "English"
            "ja" -> "日本語"
            "ko" -> "한국어"
            "es" -> "Español"
            "fr" -> "Français"
            "de" -> "Deutsch"
            "it" -> "Italiano"
            "pt" -> "Português"
            "ru" -> "Русский"
            "ar" -> "العربية"
            "hi" -> "हिन्दी"
            "th" -> "ไทย"
            "vi" -> "Tiếng Việt"
            "tr" -> "Türkçe"
            "pl" -> "Polski"
            "nl" -> "Nederlands"
            "sv" -> "Svenska"
            "da" -> "Dansk"
            "no" -> "Norsk"
            "fi" -> "Suomi"
            "cs" -> "Čeština"
            "sk" -> "Slovenčina"
            "hu" -> "Magyar"
            "ro" -> "Română"
            "bg" -> "Български"
            "hr" -> "Hrvatski"
            "sr" -> "Српски"
            "sl" -> "Slovenščina"
            "et" -> "Eesti"
            "lv" -> "Latviešu"
            "lt" -> "Lietuvių"
            "mt" -> "Malti"
            "ga" -> "Gaeilge"
            "cy" -> "Cymraeg"
            "eu" -> "Euskera"
            "ca" -> "Català"
            "gl" -> "Galego"
            "is" -> "Íslenska"
            "mk" -> "Македонски"
            "sq" -> "Shqip"
            "he" -> "עברית"
            "fa" -> "فارسی"
            "ur" -> "اردو"
            "bn" -> "বাংলা"
            "ta" -> "தமிழ்"
            "te" -> "తెలుగు"
            "ml" -> "മലയാളം"
            "kn" -> "ಕನ್ನಡ"
            "gu" -> "ગુજરાતી"
            "pa" -> "ਪੰਜਾਬੀ"
            "or" -> "ଓଡ଼ିଆ"
            "as" -> "অসমীয়া"
            "ne" -> "नेपाली"
            "si" -> "සිංහල"
            "my" -> "မြန်မာ"
            "km" -> "ខ្មែរ"
            "lo" -> "ລາວ"
            "ka" -> "ქართული"
            "am" -> "አማርኛ"
            "sw" -> "Kiswahili"
            "zu" -> "isiZulu"
            "af" -> "Afrikaans"
            "xh" -> "isiXhosa"
            "yo" -> "Yorùbá"
            "ig" -> "Igbo"
            "ha" -> "Hausa"
            else -> languageCode.uppercase()
        }
    }

    /**
     * 获取地区名称
     */
    private fun getRegionName(regionCode: String): String {
        return when (regionCode) {
            "CN" -> "中国大陆"
            "TW" -> "台湾"
            "HK" -> "香港"
            "MO" -> "澳门"
            "SG" -> "新加坡"
            "MY" -> "马来西亚"
            "US" -> "美国"
            "UK" -> "英国"
            "CA" -> "加拿大"
            "AU" -> "澳大利亚"
            "NZ" -> "新西兰"
            "IE" -> "爱尔兰"
            "ZA" -> "南非"
            "IN" -> "印度"
            "PH" -> "菲律宾"
            "JP" -> "日本"
            "KR" -> "韩国"
            "KP" -> "朝鲜"
            "ES" -> "西班牙"
            "MX" -> "墨西哥"
            "AR" -> "阿根廷"
            "CO" -> "哥伦比亚"
            "PE" -> "秘鲁"
            "CL" -> "智利"
            "VE" -> "委内瑞拉"
            "EC" -> "厄瓜多尔"
            "BO" -> "玻利维亚"
            "PY" -> "巴拉圭"
            "UY" -> "乌拉圭"
            "CR" -> "哥斯达黎加"
            "GT" -> "危地马拉"
            "HN" -> "洪都拉斯"
            "SV" -> "萨尔瓦多"
            "NI" -> "尼加拉瓜"
            "PA" -> "巴拿马"
            "CU" -> "古巴"
            "DO" -> "多米尼加"
            "PR" -> "波多黎各"
            "FR" -> "法国"
            "BE" -> "比利时"
            "CH" -> "瑞士"
            "LU" -> "卢森堡"
            "MC" -> "摩纳哥"
            "BR" -> "巴西"
            "PT" -> "葡萄牙"
            "AO" -> "安哥拉"
            "MZ" -> "莫桑比克"
            "CV" -> "佛得角"
            "GW" -> "几内亚比绍"
            "ST" -> "圣多美和普林西比"
            "TL" -> "东帝汶"
            "DE" -> "德国"
            "AT" -> "奥地利"
            "LI" -> "列支敦士登"
            "IT" -> "意大利"
            "SM" -> "圣马力诺"
            "VA" -> "梵蒂冈"
            "MT" -> "马耳他"
            "RU" -> "俄罗斯"
            "BY" -> "白俄罗斯"
            "KZ" -> "哈萨克斯坦"
            "KG" -> "吉尔吉斯斯坦"
            "TJ" -> "塔吉克斯坦"
            "UZ" -> "乌兹别克斯坦"
            "TM" -> "土库曼斯坦"
            "SA" -> "沙特阿拉伯"
            "AE" -> "阿联酋"
            "QA" -> "卡塔尔"
            "BH" -> "巴林"
            "KW" -> "科威特"
            "OM" -> "阿曼"
            "YE" -> "也门"
            "JO" -> "约旦"
            "LB" -> "黎巴嫩"
            "SY" -> "叙利亚"
            "IQ" -> "伊拉克"
            "IR" -> "伊朗"
            "AF" -> "阿富汗"
            "PK" -> "巴基斯坦"
            "BD" -> "孟加拉国"
            "LK" -> "斯里兰卡"
            "MV" -> "马尔代夫"
            "NP" -> "尼泊尔"
            "BT" -> "不丹"
            "MM" -> "缅甸"
            "TH" -> "泰国"
            "LA" -> "老挝"
            "KH" -> "柬埔寨"
            "VN" -> "越南"
            "ID" -> "印度尼西亚"
            "BN" -> "文莱"
            "TR" -> "土耳其"
            "CY" -> "塞浦路斯"
            "GE" -> "格鲁吉亚"
            "AM" -> "亚美尼亚"
            "AZ" -> "阿塞拜疆"
            "EG" -> "埃及"
            "LY" -> "利比亚"
            "TN" -> "突尼斯"
            "DZ" -> "阿尔及利亚"
            "MA" -> "摩洛哥"
            "SD" -> "苏丹"
            "SS" -> "南苏丹"
            "ET" -> "埃塞俄比亚"
            "ER" -> "厄立特里亚"
            "DJ" -> "吉布提"
            "SO" -> "索马里"
            "KE" -> "肯尼亚"
            "UG" -> "乌干达"
            "TZ" -> "坦桑尼亚"
            "RW" -> "卢旺达"
            "BI" -> "布隆迪"
            "MW" -> "马拉维"
            "ZM" -> "赞比亚"
            "ZW" -> "津巴布韦"
            "BW" -> "博茨瓦纳"
            "NA" -> "纳米比亚"
            "SZ" -> "斯威士兰"
            "LS" -> "莱索托"
            "MG" -> "马达加斯加"
            "MU" -> "毛里求斯"
            "SC" -> "塞舌尔"
            "KM" -> "科摩罗"
            else -> regionCode
        }
    }
}
