package com.cadence.core.performance.di

import android.content.Context
import com.cadence.core.performance.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 性能模块的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object PerformanceModule {
    
    @Provides
    @Singleton
    fun provideStartupTimeTracker(): StartupTimeTracker {
        return StartupTimeTracker()
    }
    
    @Provides
    @Singleton
    fun provideLazyInitializationManager(): LazyInitializationManager {
        return LazyInitializationManager()
    }
    
    @Provides
    @Singleton
    fun provideMemoryManager(@ApplicationContext context: Context): MemoryManager {
        return MemoryManager(context)
    }
    
    @Provides
    @Singleton
    fun provideNetworkPerformanceOptimizer(@ApplicationContext context: Context): NetworkPerformanceOptimizer {
        return NetworkPerformanceOptimizer(context)
    }
    
    @Provides
    @Singleton
    fun provideComposePerformanceOptimizer(): ComposePerformanceOptimizer {
        return ComposePerformanceOptimizer()
    }
    
    @Provides
    @Singleton
    fun providePerformanceMonitor(
        @ApplicationContext context: Context,
        startupTimeTracker: StartupTimeTracker,
        memoryManager: MemoryManager,
        networkPerformanceOptimizer: NetworkPerformanceOptimizer
    ): PerformanceMonitor {
        return PerformanceMonitor(context, startupTimeTracker, memoryManager, networkPerformanceOptimizer)
    }
}
