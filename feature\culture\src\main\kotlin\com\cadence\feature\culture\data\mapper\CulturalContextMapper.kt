package com.cadence.feature.culture.data.mapper

import com.cadence.core.database.entity.culture.*
import com.cadence.domain.model.culture.*

/**
 * 文化背景数据映射器
 * 负责Entity和Domain Model之间的转换
 */
object CulturalContextMapper {

    /**
     * 将数据库实体转换为领域模型
     */
    fun mapToDomain(
        contextEntity: CulturalContextEntity,
        usageContexts: List<UsageContextEntity>,
        regionalDifferences: List<RegionalDifferenceEntity>,
        examples: List<CulturalExampleEntity>,
        recommendations: List<CulturalRecommendationEntity>,
        learningProgress: CulturalLearningProgressEntity?
    ): CulturalContext {
        return CulturalContext(
            id = contextEntity.id,
            word = contextEntity.word,
            sourceLanguage = contextEntity.sourceLanguage,
            targetLanguage = contextEntity.targetLanguage,
            region = contextEntity.region,
            culturalMeaning = contextEntity.culturalMeaning,
            historicalBackground = contextEntity.historicalBackground,
            usageContext = usageContexts.map { mapUsageContextToDomain(it) },
            regionalDifferences = regionalDifferences.map { mapRegionalDifferenceToDomain(it) },
            examples = examples.map { mapCulturalExampleToDomain(it) },
            difficulty = CulturalDifficulty.valueOf(contextEntity.difficulty),
            knowledgeType = CulturalKnowledgeType.valueOf(contextEntity.knowledgeType),
            tags = contextEntity.tags.split(",").filter { it.isNotBlank() },
            recommendations = recommendations.map { mapCulturalRecommendationToDomain(it) },
            learningProgress = learningProgress?.let { mapLearningProgressToDomain(it) },
            createdAt = contextEntity.createdAt,
            updatedAt = contextEntity.updatedAt
        )
    }

    /**
     * 将领域模型转换为数据库实体
     */
    fun mapToEntity(domain: CulturalContext): CulturalContextEntity {
        return CulturalContextEntity(
            id = domain.id,
            word = domain.word,
            sourceLanguage = domain.sourceLanguage,
            targetLanguage = domain.targetLanguage,
            region = domain.region,
            culturalMeaning = domain.culturalMeaning,
            historicalBackground = domain.historicalBackground,
            difficulty = domain.difficulty.name,
            knowledgeType = domain.knowledgeType.name,
            tags = domain.tags.joinToString(","),
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt
        )
    }

    /**
     * 映射使用场景到领域模型
     */
    private fun mapUsageContextToDomain(entity: UsageContextEntity): UsageContext {
        return UsageContext(
            id = entity.id,
            context = entity.context,
            description = entity.description,
            appropriateness = AppropriatenessLevel.valueOf(entity.appropriateness),
            examples = entity.examples.split("|").filter { it.isNotBlank() }
        )
    }

    /**
     * 映射地域差异到领域模型
     */
    private fun mapRegionalDifferenceToDomain(entity: RegionalDifferenceEntity): RegionalDifference {
        return RegionalDifference(
            id = entity.id,
            region = entity.region,
            difference = entity.difference,
            explanation = entity.explanation
        )
    }

    /**
     * 映射文化示例到领域模型
     */
    private fun mapCulturalExampleToDomain(entity: CulturalExampleEntity): CulturalExample {
        return CulturalExample(
            id = entity.id,
            scenario = entity.scenario,
            originalText = entity.originalText,
            translatedText = entity.translatedText,
            culturalNote = entity.culturalNote
        )
    }

    /**
     * 映射文化推荐到领域模型
     */
    fun mapCulturalRecommendationToDomain(entity: CulturalRecommendationEntity): CulturalRecommendation {
        return CulturalRecommendation(
            id = entity.id,
            title = entity.title,
            description = entity.description,
            type = CulturalKnowledgeType.valueOf(entity.type),
            difficulty = CulturalDifficulty.valueOf(entity.difficulty),
            region = entity.region,
            tags = entity.tags.split(",").filter { it.isNotBlank() },
            popularity = entity.popularity,
            createdAt = entity.createdAt
        )
    }

    /**
     * 映射学习进度到领域模型
     */
    private fun mapLearningProgressToDomain(entity: CulturalLearningProgressEntity): CulturalLearningProgress {
        return CulturalLearningProgress(
            id = entity.id,
            contextId = entity.contextId,
            isLearned = entity.isLearned,
            isBookmarked = entity.isBookmarked,
            learningNotes = entity.learningNotes,
            lastStudiedAt = entity.lastStudiedAt,
            studyCount = entity.studyCount,
            masteryLevel = entity.masteryLevel
        )
    }

    /**
     * 将使用场景领域模型转换为实体
     */
    fun mapUsageContextToEntity(domain: UsageContext, contextId: String): UsageContextEntity {
        return UsageContextEntity(
            id = domain.id,
            contextId = contextId,
            context = domain.context,
            description = domain.description,
            appropriateness = domain.appropriateness.name,
            examples = domain.examples.joinToString("|")
        )
    }

    /**
     * 将地域差异领域模型转换为实体
     */
    fun mapRegionalDifferenceToEntity(domain: RegionalDifference, contextId: String): RegionalDifferenceEntity {
        return RegionalDifferenceEntity(
            id = domain.id,
            contextId = contextId,
            region = domain.region,
            difference = domain.difference,
            explanation = domain.explanation
        )
    }

    /**
     * 将文化示例领域模型转换为实体
     */
    fun mapCulturalExampleToEntity(domain: CulturalExample, contextId: String): CulturalExampleEntity {
        return CulturalExampleEntity(
            id = domain.id,
            contextId = contextId,
            scenario = domain.scenario,
            originalText = domain.originalText,
            translatedText = domain.translatedText,
            culturalNote = domain.culturalNote
        )
    }

    /**
     * 将文化推荐领域模型转换为实体
     */
    fun mapCulturalRecommendationToEntity(domain: CulturalRecommendation, contextId: String): CulturalRecommendationEntity {
        return CulturalRecommendationEntity(
            id = domain.id,
            contextId = contextId,
            title = domain.title,
            description = domain.description,
            type = domain.type.name,
            difficulty = domain.difficulty.name,
            region = domain.region,
            tags = domain.tags.joinToString(","),
            popularity = domain.popularity,
            createdAt = domain.createdAt
        )
    }

    /**
     * 将学习进度领域模型转换为实体
     */
    fun mapLearningProgressToEntity(domain: CulturalLearningProgress): CulturalLearningProgressEntity {
        return CulturalLearningProgressEntity(
            id = domain.id,
            contextId = domain.contextId,
            isLearned = domain.isLearned,
            isBookmarked = domain.isBookmarked,
            learningNotes = domain.learningNotes,
            lastStudiedAt = domain.lastStudiedAt,
            studyCount = domain.studyCount,
            masteryLevel = domain.masteryLevel
        )
    }

    /**
     * 批量映射文化背景实体到领域模型
     */
    fun mapToDomainList(
        contextEntities: List<CulturalContextEntity>,
        usageContextsMap: Map<String, List<UsageContextEntity>>,
        regionalDifferencesMap: Map<String, List<RegionalDifferenceEntity>>,
        examplesMap: Map<String, List<CulturalExampleEntity>>,
        recommendationsMap: Map<String, List<CulturalRecommendationEntity>>,
        learningProgressMap: Map<String, CulturalLearningProgressEntity>
    ): List<CulturalContext> {
        return contextEntities.map { contextEntity ->
            mapToDomain(
                contextEntity = contextEntity,
                usageContexts = usageContextsMap[contextEntity.id] ?: emptyList(),
                regionalDifferences = regionalDifferencesMap[contextEntity.id] ?: emptyList(),
                examples = examplesMap[contextEntity.id] ?: emptyList(),
                recommendations = recommendationsMap[contextEntity.id] ?: emptyList(),
                learningProgress = learningProgressMap[contextEntity.id]
            )
        }
    }
}