package com.cadence.core.database.entity.learning

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 学习进度数据库实体
 */
@Entity(
    tableName = "learning_progress",
    foreignKeys = [
        ForeignKey(
            entity = WordEntity::class,
            parentColumns = ["id"],
            childColumns = ["word_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["word_id"]),
        Index(value = ["user_id", "word_id"], unique = true),
        Index(value = ["next_review_at"]),
        Index(value = ["mastery_level"])
    ]
)
data class LearningProgressEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "user_id")
    val userId: String,
    
    @ColumnInfo(name = "word_id")
    val wordId: String,
    
    @ColumnInfo(name = "mastery_level")
    val masteryLevel: String,
    
    @ColumnInfo(name = "correct_answers")
    val correctAnswers: Int = 0,
    
    @ColumnInfo(name = "total_attempts")
    val totalAttempts: Int = 0,
    
    @ColumnInfo(name = "last_studied_at")
    val lastStudiedAt: Long? = null,
    
    @ColumnInfo(name = "next_review_at")
    val nextReviewAt: Long? = null,
    
    @ColumnInfo(name = "study_streak")
    val studyStreak: Int = 0,
    
    @ColumnInfo(name = "is_bookmarked")
    val isBookmarked: Boolean = false,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long,
    
    @ColumnInfo(name = "updated_at")
    val updatedAt: Long
)