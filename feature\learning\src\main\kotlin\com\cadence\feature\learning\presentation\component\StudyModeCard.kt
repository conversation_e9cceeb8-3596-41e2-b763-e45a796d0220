package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.feature.learning.presentation.viewmodel.StudyMode

/**
 * 学习模式选择卡片
 * 提供不同的学习方式选择
 */
@Composable
fun StudyModeCard(
    onModeSelected: (StudyMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "选择学习方式",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(StudyMode.values()) { mode ->
                    StudyModeItem(
                        mode = mode,
                        onClick = { onModeSelected(mode) }
                    )
                }
            }
        }
    }
}

/**
 * 学习模式项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun StudyModeItem(
    mode: StudyMode,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier.width(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = getStudyModeIcon(mode),
                contentDescription = mode.displayName,
                modifier = Modifier.size(32.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = mode.displayName,
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

/**
 * 获取学习模式对应的图标
 */
private fun getStudyModeIcon(mode: StudyMode): ImageVector {
    return when (mode) {
        StudyMode.FLASHCARD -> Icons.Default.CreditCard
        StudyMode.MULTIPLE_CHOICE -> Icons.Default.Quiz
        StudyMode.TYPING -> Icons.Default.Keyboard
        StudyMode.LISTENING -> Icons.Default.Headphones
    }
}