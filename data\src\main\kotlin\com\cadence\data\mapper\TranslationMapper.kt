package com.cadence.data.mapper

import com.cadence.core.database.entity.TranslationEntity
import com.cadence.domain.model.*

/**
 * 翻译数据映射器
 * 负责数据库实体与领域模型之间的转换
 */
object TranslationMapper {
    
    /**
     * 将数据库实体转换为领域模型
     * 注意：标签信息需要通过单独的查询获取
     */
    fun TranslationEntity.toDomain(tags: List<Tag> = emptyList()): Translation {
        return Translation(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = Language(
                code = sourceLanguage,
                name = getLanguageName(sourceLanguage),
                region = sourceRegion?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = getRegionName(regionCode),
                        dialectName = null,
                        culturalInfo = null
                    )
                }
            ),
            targetLanguage = Language(
                code = targetLanguage,
                name = getLanguageName(targetLanguage),
                region = targetRegion?.let { regionCode ->
                    Region(
                        code = regionCode,
                        name = getRegionName(regionCode),
                        dialectName = null,
                        culturalInfo = null
                    )
                }
            ),
            confidenceScore = confidenceScore,
            isFavorite = isFavorite,
            createdAt = createdAt,
            updatedAt = updatedAt,
            translationType = TranslationType.valueOf(translationType.uppercase()),
            culturalContext = culturalContext,
            tags = tags
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     * 注意：标签信息需要通过单独的操作保存
     */
    fun Translation.toEntity(): TranslationEntity {
        return TranslationEntity(
            id = id,
            sourceText = sourceText,
            translatedText = translatedText,
            sourceLanguage = sourceLanguage.code,
            targetLanguage = targetLanguage.code,
            sourceRegion = sourceLanguage.region?.code,
            targetRegion = targetLanguage.region?.code,
            confidenceScore = confidenceScore,
            isFavorite = isFavorite,
            createdAt = createdAt,
            updatedAt = updatedAt,
            translationType = translationType.name.lowercase(),
            culturalContext = culturalContext
        )
    }
    
    /**
     * 批量转换数据库实体列表为领域模型列表
     */
    fun List<TranslationEntity>.toDomainList(): List<Translation> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换领域模型列表为数据库实体列表
     */
    fun List<Translation>.toEntityList(): List<TranslationEntity> {
        return map { it.toEntity() }
    }
    
    /**
     * 创建翻译统计信息
     */
    fun createTranslationStatistics(
        totalTranslations: Int,
        favoriteCount: Int,
        languagePairUsages: List<LanguagePairUsage>,
        translationsByType: Map<String, Int>,
        averageConfidenceScore: Float
    ): TranslationStatistics {
        return TranslationStatistics(
            totalTranslations = totalTranslations,
            favoriteCount = favoriteCount,
            mostUsedLanguagePairs = languagePairUsages,
            translationsByType = translationsByType.mapKeys { TranslationType.valueOf(it.key) },
            averageConfidenceScore = averageConfidenceScore
        )
    }

    /**
     * 根据语言代码获取语言名称
     */
    private fun getLanguageName(languageCode: String): String {
        return when (languageCode.lowercase()) {
            "zh" -> "中文"
            "en" -> "English"
            "ja" -> "日本語"
            "ko" -> "한국어"
            "fr" -> "Français"
            "de" -> "Deutsch"
            "es" -> "Español"
            "it" -> "Italiano"
            "pt" -> "Português"
            "ru" -> "Русский"
            "ar" -> "العربية"
            "hi" -> "हिन्दी"
            "th" -> "ไทย"
            "vi" -> "Tiếng Việt"
            "ms" -> "Bahasa Melayu"
            "id" -> "Bahasa Indonesia"
            "tr" -> "Türkçe"
            "pl" -> "Polski"
            "nl" -> "Nederlands"
            "sv" -> "Svenska"
            "da" -> "Dansk"
            "no" -> "Norsk"
            "fi" -> "Suomi"
            "cs" -> "Čeština"
            "sk" -> "Slovenčina"
            "hu" -> "Magyar"
            "ro" -> "Română"
            "bg" -> "Български"
            "hr" -> "Hrvatski"
            "sr" -> "Српски"
            "sl" -> "Slovenščina"
            "et" -> "Eesti"
            "lv" -> "Latviešu"
            "lt" -> "Lietuvių"
            "uk" -> "Українська"
            "be" -> "Беларуская"
            "mk" -> "Македонски"
            "sq" -> "Shqip"
            "mt" -> "Malti"
            "ga" -> "Gaeilge"
            "cy" -> "Cymraeg"
            "is" -> "Íslenska"
            "fo" -> "Føroyskt"
            "eu" -> "Euskera"
            "ca" -> "Català"
            "gl" -> "Galego"
            "oc" -> "Occitan"
            "co" -> "Corsu"
            "br" -> "Brezhoneg"
            "gd" -> "Gàidhlig"
            "kw" -> "Kernewek"
            "gv" -> "Gaelg"
            else -> languageCode.uppercase()
        }
    }

    /**
     * 根据地区代码获取地区名称
     */
    private fun getRegionName(regionCode: String): String {
        return when (regionCode.uppercase()) {
            "CN" -> "中国大陆"
            "TW" -> "台湾"
            "HK" -> "香港"
            "MO" -> "澳门"
            "SG" -> "新加坡"
            "MY" -> "马来西亚"
            "US" -> "美国"
            "GB" -> "英国"
            "CA" -> "加拿大"
            "AU" -> "澳大利亚"
            "NZ" -> "新西兰"
            "IE" -> "爱尔兰"
            "ZA" -> "南非"
            "IN" -> "印度"
            "PH" -> "菲律宾"
            "JP" -> "日本"
            "KR" -> "韩国"
            "KP" -> "朝鲜"
            "FR" -> "法国"
            "BE" -> "比利时"
            "CH" -> "瑞士"
            "LU" -> "卢森堡"
            "MC" -> "摩纳哥"
            "DE" -> "德国"
            "AT" -> "奥地利"
            "LI" -> "列支敦士登"
            "ES" -> "西班牙"
            "MX" -> "墨西哥"
            "AR" -> "阿根廷"
            "CO" -> "哥伦比亚"
            "PE" -> "秘鲁"
            "VE" -> "委内瑞拉"
            "CL" -> "智利"
            "EC" -> "厄瓜多尔"
            "GT" -> "危地马拉"
            "CU" -> "古巴"
            "DO" -> "多米尼加"
            "HN" -> "洪都拉斯"
            "NI" -> "尼加拉瓜"
            "CR" -> "哥斯达黎加"
            "PA" -> "巴拿马"
            "SV" -> "萨尔瓦多"
            "PY" -> "巴拉圭"
            "UY" -> "乌拉圭"
            "BO" -> "玻利维亚"
            "IT" -> "意大利"
            "SM" -> "圣马力诺"
            "VA" -> "梵蒂冈"
            "PT" -> "葡萄牙"
            "BR" -> "巴西"
            "AO" -> "安哥拉"
            "MZ" -> "莫桑比克"
            "GW" -> "几内亚比绍"
            "CV" -> "佛得角"
            "ST" -> "圣多美和普林西比"
            "TL" -> "东帝汶"
            "RU" -> "俄罗斯"
            "BY" -> "白俄罗斯"
            "KZ" -> "哈萨克斯坦"
            "KG" -> "吉尔吉斯斯坦"
            "TJ" -> "塔吉克斯坦"
            "UZ" -> "乌兹别克斯坦"
            "TM" -> "土库曼斯坦"
            "EG" -> "埃及"
            "SA" -> "沙特阿拉伯"
            "AE" -> "阿联酋"
            "QA" -> "卡塔尔"
            "KW" -> "科威特"
            "BH" -> "巴林"
            "OM" -> "阿曼"
            "YE" -> "也门"
            "JO" -> "约旦"
            "LB" -> "黎巴嫩"
            "SY" -> "叙利亚"
            "IQ" -> "伊拉克"
            "IR" -> "伊朗"
            "AF" -> "阿富汗"
            "PK" -> "巴基斯坦"
            "BD" -> "孟加拉国"
            "LK" -> "斯里兰卡"
            "MV" -> "马尔代夫"
            "NP" -> "尼泊尔"
            "BT" -> "不丹"
            "TH" -> "泰国"
            "VN" -> "越南"
            "LA" -> "老挝"
            "KH" -> "柬埔寨"
            "MM" -> "缅甸"
            "ID" -> "印度尼西亚"
            "BN" -> "文莱"
            "TR" -> "土耳其"
            "CY" -> "塞浦路斯"
            "GE" -> "格鲁吉亚"
            "AM" -> "亚美尼亚"
            "AZ" -> "阿塞拜疆"
            else -> regionCode
        }
    }
}
