package com.cadence.core.performance

import android.app.Application
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 延迟初始化管理器
 * 负责管理非关键组件的延迟初始化，优化应用启动速度
 */
@Singleton
class LazyInitializationManager @Inject constructor() {
    
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private val initializedComponents = ConcurrentHashMap<String, Boolean>()
    private val initializationTasks = ConcurrentHashMap<String, suspend () -> Unit>()
    private val dependencies = ConcurrentHashMap<String, List<String>>()
    
    private val isApplicationReady = AtomicBoolean(false)
    
    /**
     * 注册延迟初始化任务
     */
    fun registerTask(
        componentName: String,
        dependencies: List<String> = emptyList(),
        task: suspend () -> Unit
    ) {
        initializationTasks[componentName] = task
        this.dependencies[componentName] = dependencies
        
        Timber.d("注册延迟初始化任务: $componentName, 依赖: $dependencies")
    }
    
    /**
     * 标记应用已准备就绪，开始执行延迟初始化
     */
    fun markApplicationReady() {
        if (isApplicationReady.compareAndSet(false, true)) {
            Timber.d("应用已准备就绪，开始执行延迟初始化任务")
            scope.launch {
                executeAllTasks()
            }
        }
    }
    
    /**
     * 立即初始化指定组件
     */
    suspend fun initializeComponent(componentName: String): Boolean {
        return withContext(Dispatchers.Default) {
            if (initializedComponents[componentName] == true) {
                Timber.d("组件 $componentName 已经初始化")
                return@withContext true
            }
            
            try {
                // 先初始化依赖组件
                val componentDependencies = dependencies[componentName] ?: emptyList()
                for (dependency in componentDependencies) {
                    if (!initializeComponent(dependency)) {
                        Timber.e("初始化依赖组件失败: $dependency")
                        return@withContext false
                    }
                }
                
                // 执行初始化任务
                val task = initializationTasks[componentName]
                if (task != null) {
                    val startTime = System.currentTimeMillis()
                    task.invoke()
                    val duration = System.currentTimeMillis() - startTime
                    
                    initializedComponents[componentName] = true
                    Timber.d("组件 $componentName 初始化完成，耗时: ${duration}ms")
                    return@withContext true
                } else {
                    Timber.w("未找到组件 $componentName 的初始化任务")
                    return@withContext false
                }
            } catch (e: Exception) {
                Timber.e(e, "初始化组件 $componentName 失败")
                return@withContext false
            }
        }
    }
    
    /**
     * 检查组件是否已初始化
     */
    fun isComponentInitialized(componentName: String): Boolean {
        return initializedComponents[componentName] == true
    }
    
    /**
     * 等待组件初始化完成
     */
    suspend fun waitForComponent(componentName: String, timeoutMs: Long = 5000): Boolean {
        return withTimeoutOrNull(timeoutMs) {
            while (!isComponentInitialized(componentName)) {
                delay(50)
            }
            true
        } ?: false
    }
    
    /**
     * 获取初始化状态报告
     */
    fun getInitializationReport(): InitializationReport {
        val totalTasks = initializationTasks.size
        val completedTasks = initializedComponents.values.count { it }
        val pendingTasks = initializationTasks.keys.filter { !isComponentInitialized(it) }
        
        return InitializationReport(
            totalTasks = totalTasks,
            completedTasks = completedTasks,
            pendingTasks = pendingTasks,
            isApplicationReady = isApplicationReady.get()
        )
    }
    
    /**
     * 执行所有延迟初始化任务
     */
    private suspend fun executeAllTasks() {
        val startTime = System.currentTimeMillis()
        Timber.d("开始执行延迟初始化任务，总数: ${initializationTasks.size}")
        
        // 按依赖关系排序任务
        val sortedTasks = topologicalSort()
        
        // 并行执行无依赖的任务
        val jobs = mutableListOf<Job>()
        
        for (componentName in sortedTasks) {
            val job = scope.launch {
                initializeComponent(componentName)
            }
            jobs.add(job)
        }
        
        // 等待所有任务完成
        jobs.joinAll()
        
        val duration = System.currentTimeMillis() - startTime
        val report = getInitializationReport()
        
        Timber.i("延迟初始化完成，耗时: ${duration}ms")
        Timber.i("成功初始化: ${report.completedTasks}/${report.totalTasks} 个组件")
        
        if (report.pendingTasks.isNotEmpty()) {
            Timber.w("未完成初始化的组件: ${report.pendingTasks}")
        }
    }
    
    /**
     * 拓扑排序，确保按依赖关系初始化
     */
    private fun topologicalSort(): List<String> {
        val result = mutableListOf<String>()
        val visited = mutableSetOf<String>()
        val visiting = mutableSetOf<String>()
        
        fun visit(node: String) {
            if (node in visiting) {
                Timber.w("检测到循环依赖: $node")
                return
            }
            if (node in visited) return
            
            visiting.add(node)
            
            val nodeDependencies = dependencies[node] ?: emptyList()
            for (dependency in nodeDependencies) {
                visit(dependency)
            }
            
            visiting.remove(node)
            visited.add(node)
            result.add(node)
        }
        
        for (componentName in initializationTasks.keys) {
            visit(componentName)
        }
        
        return result
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        initializedComponents.clear()
        initializationTasks.clear()
        dependencies.clear()
        isApplicationReady.set(false)
        Timber.d("延迟初始化管理器已清理")
    }
}

/**
 * 初始化报告数据类
 */
data class InitializationReport(
    val totalTasks: Int,
    val completedTasks: Int,
    val pendingTasks: List<String>,
    val isApplicationReady: Boolean
)

/**
 * 延迟初始化任务优先级
 */
enum class InitializationPriority {
    HIGH,      // 高优先级，应用启动后立即执行
    MEDIUM,    // 中优先级，用户交互前执行
    LOW,       // 低优先级，空闲时执行
    BACKGROUND // 后台优先级，完全后台执行
}

/**
 * 延迟初始化任务配置
 */
data class LazyInitTask(
    val name: String,
    val priority: InitializationPriority,
    val dependencies: List<String> = emptyList(),
    val timeoutMs: Long = 10000,
    val retryCount: Int = 3,
    val task: suspend () -> Unit
)
