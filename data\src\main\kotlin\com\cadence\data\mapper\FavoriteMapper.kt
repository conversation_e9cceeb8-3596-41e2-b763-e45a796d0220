package com.cadence.data.mapper

import com.cadence.core.database.entity.*
import com.cadence.domain.model.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

/**
 * 收藏功能数据转换器
 * 负责领域模型与数据库实体之间的转换
 */
object FavoriteMapper {
    
    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
    }
    
    // ========== FavoriteFolder 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteFolderEntity.toDomain(): FavoriteFolder {
        return FavoriteFolder(
            id = id,
            name = name,
            description = description,
            color = color,
            icon = icon,
            isDefault = isDefault,
            sortOrder = sortOrder,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteFolder.toEntity(): FavoriteFolderEntity {
        return FavoriteFolderEntity(
            id = id,
            name = name,
            description = description,
            color = color,
            icon = icon,
            isDefault = isDefault,
            sortOrder = sortOrder,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    // ========== FavoriteItem 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteItemEntity.toDomain(): FavoriteItem {
        return FavoriteItem(
            id = id,
            translationId = translationId,
            folderId = folderId,
            note = note,
            tags = parseTagsFromJson(tags),
            priority = FavoritePriority.valueOf(priority),
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteItem.toEntity(): FavoriteItemEntity {
        return FavoriteItemEntity(
            id = id,
            translationId = translationId,
            folderId = folderId,
            note = note,
            tags = encodeTagsToJson(tags),
            priority = priority.name,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    // ========== FavoriteConfig 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteConfigEntity.toDomain(): FavoriteConfig {
        return FavoriteConfig(
            autoSync = autoSync,
            defaultFolderId = defaultFolderId,
            maxItemsPerFolder = maxItemsPerFolder,
            enableNotifications = enableNotifications,
            showItemCount = showItemCount,
            compactView = compactView,
            enableTags = enableTags,
            enablePriority = enablePriority
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteConfig.toEntity(): FavoriteConfigEntity {
        val currentTime = System.currentTimeMillis()
        return FavoriteConfigEntity(
            id = "default_config",
            autoSync = autoSync,
            defaultFolderId = defaultFolderId,
            maxItemsPerFolder = maxItemsPerFolder,
            enableNotifications = enableNotifications,
            showItemCount = showItemCount,
            compactView = compactView,
            enableTags = enableTags,
            enablePriority = enablePriority,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== FavoriteOperation 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteOperationEntity.toDomain(): FavoriteOperation {
        return FavoriteOperation(
            id = id,
            operationType = FavoriteOperationType.valueOf(operationType),
            targetId = targetId,
            details = details,
            timestamp = timestamp
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteOperation.toEntity(): FavoriteOperationEntity {
        return FavoriteOperationEntity(
            id = id,
            operationType = operationType.name,
            targetId = targetId,
            details = details,
            timestamp = timestamp
        )
    }
    
    // ========== FavoriteSyncInfo 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteSyncInfoEntity.toDomain(): FavoriteSyncInfo {
        return FavoriteSyncInfo(
            itemId = itemId,
            itemType = itemType,
            status = FavoriteSyncStatus.valueOf(status),
            lastSyncTime = lastSyncTime,
            syncError = syncError,
            version = version
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteSyncInfo.toEntity(): FavoriteSyncInfoEntity {
        val currentTime = System.currentTimeMillis()
        return FavoriteSyncInfoEntity(
            id = "${itemId}_${itemType}",
            itemId = itemId,
            itemType = itemType,
            status = status.name,
            lastSyncTime = lastSyncTime,
            syncError = syncError,
            version = version,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== FavoriteUsageStats 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteUsageStatsEntity.toDomain(): FavoriteUsageStats {
        return FavoriteUsageStats(
            folderId = folderId,
            accessCount = accessCount,
            lastAccessTime = lastAccessTime,
            averageSessionDuration = averageSessionDuration,
            mostUsedTags = parseTagsFromJson(mostUsedTags),
            peakUsageHour = peakUsageHour
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteUsageStats.toEntity(): FavoriteUsageStatsEntity {
        val currentTime = System.currentTimeMillis()
        return FavoriteUsageStatsEntity(
            id = folderId,
            folderId = folderId,
            accessCount = accessCount,
            lastAccessTime = lastAccessTime,
            averageSessionDuration = averageSessionDuration,
            mostUsedTags = encodeTagsToJson(mostUsedTags),
            peakUsageHour = peakUsageHour,
            createdAt = currentTime,
            updatedAt = currentTime
        )
    }
    
    // ========== FavoriteSearchSuggestion 映射 ==========
    
    /**
     * 将数据库实体转换为领域模型
     */
    fun FavoriteSearchSuggestionEntity.toDomain(): FavoriteSearchSuggestion {
        return FavoriteSearchSuggestion(
            type = type,
            value = value,
            count = count,
            lastUsed = lastUsed
        )
    }
    
    /**
     * 将领域模型转换为数据库实体
     */
    fun FavoriteSearchSuggestion.toEntity(): FavoriteSearchSuggestionEntity {
        return FavoriteSearchSuggestionEntity(
            id = "${type}_${value}",
            type = type,
            value = value,
            count = count,
            lastUsed = lastUsed,
            createdAt = lastUsed
        )
    }
    
    // ========== 复合对象映射 ==========
    
    /**
     * 将FavoriteItemWithTranslation转换为Pair<FavoriteItem, Translation?>
     */
    fun FavoriteItemWithTranslation.toDomainPair(): Pair<FavoriteItem, Translation?> {
        val favoriteItem = favoriteItem.toDomain()
        val translation = translation?.let { TranslationMapper.toDomain(it) }
        return Pair(favoriteItem, translation)
    }
    
    /**
     * 将FavoriteFolderWithItemCount转换为Pair<FavoriteFolder, Int>
     */
    fun FavoriteFolderWithItemCount.toDomainPair(): Pair<FavoriteFolder, Int> {
        return Pair(folder.toDomain(), itemCount)
    }
    
    // ========== 统计信息映射 ==========
    
    /**
     * 创建收藏夹统计信息
     */
    fun createFavoriteStatistics(
        totalFolders: Int,
        totalItems: Int,
        folderItemCounts: List<FolderItemCount>,
        priorityItemCounts: List<PriorityItemCount>,
        recentlyAddedCount: Int
    ): FavoriteStatistics {
        val itemsByFolder = folderItemCounts.associate { it.id to it.count }
        val itemsByPriority = priorityItemCounts.associate { 
            FavoritePriority.valueOf(it.priority) to it.count 
        }
        
        return FavoriteStatistics(
            totalFolders = totalFolders,
            totalItems = totalItems,
            itemsByFolder = itemsByFolder,
            itemsByPriority = itemsByPriority,
            itemsByTag = emptyMap(), // 需要单独计算
            recentlyAddedCount = recentlyAddedCount,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    // ========== 批量转换方法 ==========
    
    /**
     * 批量转换收藏夹实体列表为领域模型列表
     */
    fun List<FavoriteFolderEntity>.toDomainList(): List<FavoriteFolder> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换收藏夹领域模型列表为实体列表
     */
    fun List<FavoriteFolder>.toEntityList(): List<FavoriteFolderEntity> {
        return map { it.toEntity() }
    }
    
    /**
     * 批量转换收藏项实体列表为领域模型列表
     */
    fun List<FavoriteItemEntity>.toDomainList(): List<FavoriteItem> {
        return map { it.toDomain() }
    }
    
    /**
     * 批量转换收藏项领域模型列表为实体列表
     */
    fun List<FavoriteItem>.toEntityList(): List<FavoriteItemEntity> {
        return map { it.toEntity() }
    }
    
    /**
     * 批量转换FavoriteItemWithTranslation列表
     */
    fun List<FavoriteItemWithTranslation>.toDomainPairList(): List<Pair<FavoriteItem, Translation?>> {
        return map { it.toDomainPair() }
    }
    
    /**
     * 批量转换FavoriteFolderWithItemCount列表
     */
    fun List<FavoriteFolderWithItemCount>.toDomainPairList(): List<Pair<FavoriteFolder, Int>> {
        return map { it.toDomainPair() }
    }
    
    /**
     * 批量转换搜索建议实体列表为领域模型列表
     */
    fun List<FavoriteSearchSuggestionEntity>.toDomainList(): List<FavoriteSearchSuggestion> {
        return map { it.toDomain() }
    }
    
    // ========== 辅助方法 ==========
    
    /**
     * 将标签列表编码为JSON字符串
     */
    private fun encodeTagsToJson(tags: List<String>): String {
        return try {
            json.encodeToString(tags)
        } catch (e: Exception) {
            "[]"
        }
    }
    
    /**
     * 从JSON字符串解析标签列表
     */
    private fun parseTagsFromJson(tagsJson: String): List<String> {
        return try {
            json.decodeFromString<List<String>>(tagsJson)
        } catch (e: Exception) {
            emptyList()
        }
    }
}
