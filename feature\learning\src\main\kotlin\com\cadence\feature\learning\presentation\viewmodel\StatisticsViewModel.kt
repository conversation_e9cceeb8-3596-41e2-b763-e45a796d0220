package com.cadence.feature.learning.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.learning.*
import com.cadence.domain.repository.LearningRepository
import com.cadence.feature.learning.presentation.screen.TimePeriod
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 学习统计ViewModel
 * 管理学习统计分析界面的状态和数据
 */
@HiltViewModel
class StatisticsViewModel @Inject constructor(
    private val learningRepository: LearningRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(StatisticsUiState())
    val uiState: StateFlow<StatisticsUiState> = _uiState.asStateFlow()
    
    private val _overallStats = MutableStateFlow<LearningStatistics?>(null)
    val overallStats: StateFlow<LearningStatistics?> = _overallStats.asStateFlow()
    
    private val _weeklyProgress = MutableStateFlow<List<DailyProgress>>(emptyList())
    val weeklyProgress: StateFlow<List<DailyProgress>> = _weeklyProgress.asStateFlow()
    
    private val _monthlyProgress = MutableStateFlow<List<DailyProgress>>(emptyList())
    val monthlyProgress: StateFlow<List<DailyProgress>> = _monthlyProgress.asStateFlow()
    
    private val _learningTrends = MutableStateFlow<List<DailyProgress>>(emptyList())
    val learningTrends: StateFlow<List<DailyProgress>> = _learningTrends.asStateFlow()
    
    private val _categoryAnalysis = MutableStateFlow<Map<WordCategory, CategoryAnalysis>>(emptyMap())
    val categoryAnalysis: StateFlow<Map<WordCategory, CategoryAnalysis>> = _categoryAnalysis.asStateFlow()
    
    private val _achievements = MutableStateFlow<List<Achievement>>(emptyList())
    val achievements: StateFlow<List<Achievement>> = _achievements.asStateFlow()
    
    /**
     * 加载统计数据
     */
    fun loadStatisticsData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                val userId = "current_user"
                
                // 加载总体统计
                val stats = learningRepository.getLearningStatistics(userId)
                _overallStats.value = stats
                
                // 加载不同时间段的进度数据
                loadProgressData(userId)
                
                // 加载分类分析
                loadCategoryAnalysis(userId)
                
                // 加载成就数据
                loadAchievements(userId)
                
                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "加载统计数据失败"
                )
            }
        }
    }
    
    /**
     * 刷新统计数据
     */
    fun refreshStatistics() {
        loadStatisticsData()
    }
    
    /**
     * 改变时间段
     */
    fun changeTimePeriod(period: TimePeriod) {
        _uiState.value = _uiState.value.copy(selectedTimePeriod = period)
    }
    
    /**
     * 导出统计数据
     */
    fun exportStatistics() {
        viewModelScope.launch {
            try {
                // 实现统计数据导出功能
                // 可以导出为CSV、PDF等格式
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "导出统计数据失败"
                )
            }
        }
    }
    
    /**
     * 计算学习效率
     */
    fun calculateLearningEfficiency(): LearningEfficiency {
        val stats = _overallStats.value ?: return LearningEfficiency()
        val weeklyData = _weeklyProgress.value
        
        // 计算各种效率指标
        val wordsPerHour = if (stats.totalStudyTime > 0) {
            (stats.totalWordsLearned * 3600000.0) / stats.totalStudyTime
        } else 0.0
        
        val averageDailyWords = if (weeklyData.isNotEmpty()) {
            weeklyData.map { it.wordsStudied }.average()
        } else 0.0
        
        val consistencyScore = calculateConsistencyScore(weeklyData)
        val improvementRate = calculateImprovementRate(weeklyData)
        
        return LearningEfficiency(
            wordsPerHour = wordsPerHour,
            averageDailyWords = averageDailyWords,
            consistencyScore = consistencyScore,
            improvementRate = improvementRate,
            overallEfficiency = (wordsPerHour + consistencyScore + improvementRate) / 3
        )
    }
    
    /**
     * 获取学习建议
     */
    fun getRecommendations(): List<LearningRecommendation> {
        val stats = _overallStats.value ?: return emptyList()
        val efficiency = calculateLearningEfficiency()
        val recommendations = mutableListOf<LearningRecommendation>()
        
        // 基于统计数据生成个性化建议
        if (stats.averageAccuracy < 0.7f) {
            recommendations.add(
                LearningRecommendation(
                    type = RecommendationType.ACCURACY,
                    title = "提高学习准确率",
                    description = "您的平均正确率为${(stats.averageAccuracy * 100).toInt()}%，建议放慢学习节奏，加强基础练习。",
                    priority = RecommendationPriority.HIGH
                )
            )
        }
        
        if (efficiency.consistencyScore < 0.6) {
            recommendations.add(
                LearningRecommendation(
                    type = RecommendationType.CONSISTENCY,
                    title = "保持学习连续性",
                    description = "建议每天坚持学习，即使时间较短也比间断学习效果更好。",
                    priority = RecommendationPriority.MEDIUM
                )
            )
        }
        
        if (stats.wordsToReview > 20) {
            recommendations.add(
                LearningRecommendation(
                    type = RecommendationType.REVIEW,
                    title = "及时复习单词",
                    description = "您有${stats.wordsToReview}个单词待复习，建议优先完成复习再学习新单词。",
                    priority = RecommendationPriority.HIGH
                )
            )
        }
        
        if (efficiency.wordsPerHour < 5.0) {
            recommendations.add(
                LearningRecommendation(
                    type = RecommendationType.EFFICIENCY,
                    title = "提高学习效率",
                    description = "尝试在精力充沛的时间段学习，减少干扰因素。",
                    priority = RecommendationPriority.MEDIUM
                )
            )
        }
        
        return recommendations
    }
    
    /**
     * 获取详细数据
     */
    fun getDetailedData(): List<DetailedWordData> {
        // 返回详细的单词学习数据
        return emptyList() // 简化实现
    }
    
    /**
     * 加载进度数据
     */
    private suspend fun loadProgressData(userId: String) {
        // 加载本周数据
        val weekly = learningRepository.getDailyProgress(userId, 7)
        _weeklyProgress.value = weekly
        
        // 加载本月数据
        val monthly = learningRepository.getDailyProgress(userId, 30)
        _monthlyProgress.value = monthly
        
        // 加载年度数据
        val yearly = learningRepository.getDailyProgress(userId, 365)
        _learningTrends.value = yearly
    }
    
    /**
     * 加载分类分析
     */
    private suspend fun loadCategoryAnalysis(userId: String) {
        val categoryProgress = learningRepository.getCategoryProgress(userId)
        val analysis = mutableMapOf<WordCategory, CategoryAnalysis>()
        
        categoryProgress.forEach { (category, progress) ->
            val masteryRate = if (progress.totalWords > 0) {
                progress.masteredWords.toFloat() / progress.totalWords
            } else 0f
            
            val difficulty = when {
                progress.averageAccuracy < 0.6f -> AnalysisDifficulty.HARD
                progress.averageAccuracy < 0.8f -> AnalysisDifficulty.MEDIUM
                else -> AnalysisDifficulty.EASY
            }
            
            analysis[category] = CategoryAnalysis(
                totalWords = progress.totalWords,
                masteredWords = progress.masteredWords,
                masteryRate = masteryRate,
                averageAccuracy = progress.averageAccuracy,
                totalStudyTime = progress.totalStudyTime,
                difficulty = difficulty,
                recommendation = generateCategoryRecommendation(category, masteryRate, progress.averageAccuracy)
            )
        }
        
        _categoryAnalysis.value = analysis
    }
    
    /**
     * 加载成就数据
     */
    private suspend fun loadAchievements(userId: String) {
        val stats = _overallStats.value ?: return
        val achievements = mutableListOf<Achievement>()
        
        // 基于统计数据生成成就
        if (stats.totalWordsLearned >= 100) {
            achievements.add(
                Achievement(
                    id = "words_100",
                    title = "百词斩",
                    description = "学习了100个单词",
                    icon = "🎯",
                    unlockedAt = System.currentTimeMillis(),
                    category = AchievementCategory.LEARNING
                )
            )
        }
        
        if (stats.currentStreak >= 7) {
            achievements.add(
                Achievement(
                    id = "streak_7",
                    title = "坚持一周",
                    description = "连续学习7天",
                    icon = "🔥",
                    unlockedAt = System.currentTimeMillis(),
                    category = AchievementCategory.CONSISTENCY
                )
            )
        }
        
        if (stats.averageAccuracy >= 0.9f) {
            achievements.add(
                Achievement(
                    id = "accuracy_90",
                    title = "精准射手",
                    description = "平均正确率达到90%",
                    icon = "🎯",
                    unlockedAt = System.currentTimeMillis(),
                    category = AchievementCategory.ACCURACY
                )
            )
        }
        
        _achievements.value = achievements
    }
    
    /**
     * 计算一致性分数
     */
    private fun calculateConsistencyScore(dailyProgress: List<DailyProgress>): Double {
        if (dailyProgress.isEmpty()) return 0.0
        
        val studyDays = dailyProgress.count { it.wordsStudied > 0 }
        return studyDays.toDouble() / dailyProgress.size
    }
    
    /**
     * 计算改进率
     */
    private fun calculateImprovementRate(dailyProgress: List<DailyProgress>): Double {
        if (dailyProgress.size < 2) return 0.0
        
        val recent = dailyProgress.takeLast(3).map { it.wordsStudied }.average()
        val earlier = dailyProgress.take(3).map { it.wordsStudied }.average()
        
        return if (earlier > 0) (recent - earlier) / earlier else 0.0
    }
    
    /**
     * 生成分类建议
     */
    private fun generateCategoryRecommendation(
        category: WordCategory,
        masteryRate: Float,
        accuracy: Float
    ): String {
        return when {
            masteryRate < 0.3f -> "建议加强${category.displayName}类单词的学习"
            accuracy < 0.7f -> "${category.displayName}类单词准确率较低，建议多做练习"
            masteryRate > 0.8f -> "${category.displayName}类单词掌握良好，可以学习更高难度的内容"
            else -> "继续保持${category.displayName}类单词的学习进度"
        }
    }
}

/**
 * 统计界面UI状态
 */
data class StatisticsUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val selectedTimePeriod: TimePeriod = TimePeriod.WEEK
)

/**
 * 分类分析数据
 */
data class CategoryAnalysis(
    val totalWords: Int,
    val masteredWords: Int,
    val masteryRate: Float,
    val averageAccuracy: Float,
    val totalStudyTime: Long,
    val difficulty: AnalysisDifficulty,
    val recommendation: String
)

/**
 * 分析难度
 */
enum class AnalysisDifficulty {
    EASY, MEDIUM, HARD
}

/**
 * 学习效率数据
 */
data class LearningEfficiency(
    val wordsPerHour: Double = 0.0,
    val averageDailyWords: Double = 0.0,
    val consistencyScore: Double = 0.0,
    val improvementRate: Double = 0.0,
    val overallEfficiency: Double = 0.0
)

/**
 * 学习建议
 */
data class LearningRecommendation(
    val type: RecommendationType,
    val title: String,
    val description: String,
    val priority: RecommendationPriority
)

/**
 * 建议类型
 */
enum class RecommendationType {
    ACCURACY, CONSISTENCY, REVIEW, EFFICIENCY, DIFFICULTY
}

/**
 * 建议优先级
 */
enum class RecommendationPriority {
    LOW, MEDIUM, HIGH
}

/**
 * 成就数据
 */
data class Achievement(
    val id: String,
    val title: String,
    val description: String,
    val icon: String,
    val unlockedAt: Long,
    val category: AchievementCategory
)

/**
 * 成就分类
 */
enum class AchievementCategory {
    LEARNING, CONSISTENCY, ACCURACY, SPEED, MILESTONE
}

/**
 * 详细单词数据
 */
data class DetailedWordData(
    val wordId: String,
    val word: String,
    val category: WordCategory,
    val masteryLevel: MasteryLevel,
    val accuracy: Float,
    val attempts: Int,
    val lastStudied: Long
)