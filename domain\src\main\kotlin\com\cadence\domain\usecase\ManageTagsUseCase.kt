package com.cadence.domain.usecase

import com.cadence.domain.model.*
import com.cadence.domain.repository.TagRepository
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject

/**
 * 标签管理用例
 * 处理标签相关的业务逻辑
 */
class ManageTagsUseCase @Inject constructor(
    private val tagRepository: TagRepository
) {
    
    /**
     * 获取所有标签
     * @return 标签列表流
     */
    fun getAllTags(): Flow<List<Tag>> {
        return tagRepository.getAllTags()
    }
    
    /**
     * 搜索标签
     * @param query 搜索关键词
     * @return 匹配的标签列表流
     */
    fun searchTags(query: String): Flow<List<Tag>> {
        return if (query.isBlank()) {
            getAllTags()
        } else {
            tagRepository.searchTags(query.trim())
        }
    }
    
    /**
     * 获取热门标签
     * @param limit 限制数量
     * @return 热门标签列表流
     */
    fun getPopularTags(limit: Int = 10): Flow<List<Tag>> {
        return tagRepository.getPopularTags(limit)
    }
    
    /**
     * 创建标签
     * @param request 创建请求
     * @return 创建的标签
     */
    suspend fun createTag(request: CreateTagRequest): Result<Tag> {
        return try {
            // 验证标签名称
            if (request.name.isBlank()) {
                return Result.failure(IllegalArgumentException("标签名称不能为空"))
            }
            
            if (request.name.length > 20) {
                return Result.failure(IllegalArgumentException("标签名称不能超过20个字符"))
            }
            
            // 检查是否已存在同名标签
            val existingTag = tagRepository.getTagByName(request.name)
            if (existingTag.isSuccess && existingTag.getOrNull() != null) {
                return Result.failure(IllegalArgumentException("标签名称已存在"))
            }
            
            val result = tagRepository.createTag(request)
            
            if (result.isSuccess) {
                Timber.d("创建标签成功: ${request.name}")
            } else {
                Timber.e("创建标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "创建标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 更新标签
     * @param tagId 标签ID
     * @param request 更新请求
     * @return 更新的标签
     */
    suspend fun updateTag(tagId: String, request: UpdateTagRequest): Result<Tag> {
        return try {
            if (tagId.isBlank()) {
                return Result.failure(IllegalArgumentException("标签ID不能为空"))
            }
            
            // 验证标签名称（如果提供）
            if (request.name != null) {
                if (request.name.isBlank()) {
                    return Result.failure(IllegalArgumentException("标签名称不能为空"))
                }
                
                if (request.name.length > 20) {
                    return Result.failure(IllegalArgumentException("标签名称不能超过20个字符"))
                }
                
                // 检查是否已存在同名标签（排除当前标签）
                val existingTag = tagRepository.getTagByName(request.name)
                if (existingTag.isSuccess && existingTag.getOrNull()?.id != tagId) {
                    return Result.failure(IllegalArgumentException("标签名称已存在"))
                }
            }
            
            val result = tagRepository.updateTag(tagId, request)
            
            if (result.isSuccess) {
                Timber.d("更新标签成功: $tagId")
            } else {
                Timber.e("更新标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "更新标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 删除标签
     * @param tagId 标签ID
     * @return 操作结果
     */
    suspend fun deleteTag(tagId: String): Result<Unit> {
        return try {
            if (tagId.isBlank()) {
                return Result.failure(IllegalArgumentException("标签ID不能为空"))
            }
            
            val result = tagRepository.deleteTag(tagId)
            
            if (result.isSuccess) {
                Timber.d("删除标签成功: $tagId")
            } else {
                Timber.e("删除标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "删除标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取翻译的标签
     * @param translationId 翻译ID
     * @return 标签列表流
     */
    fun getTagsForTranslation(translationId: String): Flow<List<Tag>> {
        return tagRepository.getTagsForTranslation(translationId)
    }
    
    /**
     * 为翻译添加标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     * @return 操作结果
     */
    suspend fun addTagToTranslation(translationId: String, tagId: String): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            if (tagId.isBlank()) {
                return Result.failure(IllegalArgumentException("标签ID不能为空"))
            }
            
            val result = tagRepository.addTagToTranslation(translationId, tagId)
            
            if (result.isSuccess) {
                Timber.d("为翻译添加标签成功: $translationId -> $tagId")
            } else {
                Timber.e("为翻译添加标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "为翻译添加标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 批量为翻译添加标签
     * @param translationId 翻译ID
     * @param tagIds 标签ID列表
     * @return 操作结果
     */
    suspend fun addTagsToTranslation(translationId: String, tagIds: List<String>): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            if (tagIds.isEmpty()) {
                return Result.failure(IllegalArgumentException("标签列表不能为空"))
            }
            
            val result = tagRepository.addTagsToTranslation(translationId, tagIds)
            
            if (result.isSuccess) {
                Timber.d("批量为翻译添加标签成功: $translationId -> ${tagIds.size}个标签")
            } else {
                Timber.e("批量为翻译添加标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "批量为翻译添加标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 从翻译中移除标签
     * @param translationId 翻译ID
     * @param tagId 标签ID
     * @return 操作结果
     */
    suspend fun removeTagFromTranslation(translationId: String, tagId: String): Result<Unit> {
        return try {
            if (translationId.isBlank()) {
                return Result.failure(IllegalArgumentException("翻译ID不能为空"))
            }
            
            if (tagId.isBlank()) {
                return Result.failure(IllegalArgumentException("标签ID不能为空"))
            }
            
            val result = tagRepository.removeTagFromTranslation(translationId, tagId)
            
            if (result.isSuccess) {
                Timber.d("从翻译中移除标签成功: $translationId -> $tagId")
            } else {
                Timber.e("从翻译中移除标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "从翻译中移除标签过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 获取标签统计信息
     * @return 统计信息
     */
    suspend fun getTagStatistics(): Result<TagStatistics> {
        return try {
            val result = tagRepository.getTagStatistics()
            
            if (result.isSuccess) {
                Timber.d("获取标签统计信息成功")
            } else {
                Timber.e("获取标签统计信息失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "获取标签统计信息过程中发生异常")
            Result.failure(e)
        }
    }
    
    /**
     * 清理未使用的标签
     * @return 操作结果
     */
    suspend fun cleanupUnusedTags(): Result<Unit> {
        return try {
            val result = tagRepository.cleanupUnusedTags()
            
            if (result.isSuccess) {
                Timber.d("清理未使用标签成功")
            } else {
                Timber.e("清理未使用标签失败: ${result.exceptionOrNull()?.message}")
            }
            
            result
        } catch (e: Exception) {
            Timber.e(e, "清理未使用标签过程中发生异常")
            Result.failure(e)
        }
    }
}
