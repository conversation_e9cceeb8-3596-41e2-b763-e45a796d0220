package com.cadence.core.speech

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.io.File
import java.security.MessageDigest
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 语音缓存管理器
 * 管理TTS音频缓存和语音识别结果缓存
 */
@Singleton
class SpeechCacheManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val json: Json
) {
    
    private val cacheScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 内存缓存
    private val memoryCache = ConcurrentHashMap<String, CacheEntry>()
    
    // 缓存目录
    private val cacheDir by lazy {
        File(context.cacheDir, "speech_cache").apply {
            if (!exists()) mkdirs()
        }
    }
    
    private val ttsAudioDir by lazy {
        File(cacheDir, "tts_audio").apply {
            if (!exists()) mkdirs()
        }
    }
    
    private val speechResultDir by lazy {
        File(cacheDir, "speech_results").apply {
            if (!exists()) mkdirs()
        }
    }
    
    companion object {
        private const val MAX_MEMORY_CACHE_SIZE = 100
        private const val MAX_DISK_CACHE_SIZE_MB = 50L
        private const val CACHE_EXPIRY_DAYS = 7L
        private const val CLEANUP_INTERVAL_HOURS = 24L
    }
    
    init {
        // 启动定期清理任务
        startPeriodicCleanup()
    }
    
    /**
     * 缓存TTS音频文件
     */
    suspend fun cacheTtsAudio(
        text: String,
        language: String,
        audioData: ByteArray,
        config: TtsConfig = TtsConfig()
    ): String? {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = generateTtsCacheKey(text, language, config)
                val audioFile = File(ttsAudioDir, "$cacheKey.wav")
                
                // 写入音频文件
                audioFile.writeBytes(audioData)
                
                // 创建缓存条目
                val cacheEntry = CacheEntry(
                    key = cacheKey,
                    type = CacheType.TTS_AUDIO,
                    filePath = audioFile.absolutePath,
                    metadata = TtsCacheMetadata(text, language, config),
                    createdAt = System.currentTimeMillis(),
                    lastAccessedAt = System.currentTimeMillis(),
                    size = audioData.size.toLong()
                )
                
                // 保存到内存缓存
                memoryCache[cacheKey] = cacheEntry
                
                // 保存元数据
                saveCacheMetadata(cacheEntry)
                
                Timber.d("TTS音频已缓存: $cacheKey")
                cacheKey
            } catch (e: Exception) {
                Timber.e(e, "TTS音频缓存失败")
                null
            }
        }
    }
    
    /**
     * 获取缓存的TTS音频
     */
    suspend fun getCachedTtsAudio(
        text: String,
        language: String,
        config: TtsConfig = TtsConfig()
    ): ByteArray? {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = generateTtsCacheKey(text, language, config)
                val cacheEntry = memoryCache[cacheKey] ?: loadCacheMetadata(cacheKey)
                
                if (cacheEntry != null && isCacheValid(cacheEntry)) {
                    val audioFile = File(cacheEntry.filePath)
                    if (audioFile.exists()) {
                        // 更新访问时间
                        cacheEntry.lastAccessedAt = System.currentTimeMillis()
                        memoryCache[cacheKey] = cacheEntry
                        
                        Timber.d("TTS音频缓存命中: $cacheKey")
                        return@withContext audioFile.readBytes()
                    }
                }
                
                // 缓存未命中或已过期
                memoryCache.remove(cacheKey)
                null
            } catch (e: Exception) {
                Timber.e(e, "获取TTS音频缓存失败")
                null
            }
        }
    }
    
    /**
     * 缓存语音识别结果
     */
    suspend fun cacheSpeechResult(
        audioHash: String,
        language: String,
        result: String,
        confidence: Float
    ): String? {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = generateSpeechResultCacheKey(audioHash, language)
                
                val cacheEntry = CacheEntry(
                    key = cacheKey,
                    type = CacheType.SPEECH_RESULT,
                    filePath = "",
                    metadata = SpeechResultCacheMetadata(audioHash, language, result, confidence),
                    createdAt = System.currentTimeMillis(),
                    lastAccessedAt = System.currentTimeMillis(),
                    size = result.length.toLong()
                )
                
                // 保存到内存缓存
                memoryCache[cacheKey] = cacheEntry
                
                // 保存元数据
                saveCacheMetadata(cacheEntry)
                
                Timber.d("语音识别结果已缓存: $cacheKey")
                cacheKey
            } catch (e: Exception) {
                Timber.e(e, "语音识别结果缓存失败")
                null
            }
        }
    }
    
    /**
     * 获取缓存的语音识别结果
     */
    suspend fun getCachedSpeechResult(
        audioHash: String,
        language: String
    ): SpeechResultCacheMetadata? {
        return withContext(Dispatchers.IO) {
            try {
                val cacheKey = generateSpeechResultCacheKey(audioHash, language)
                val cacheEntry = memoryCache[cacheKey] ?: loadCacheMetadata(cacheKey)
                
                if (cacheEntry != null && isCacheValid(cacheEntry)) {
                    // 更新访问时间
                    cacheEntry.lastAccessedAt = System.currentTimeMillis()
                    memoryCache[cacheKey] = cacheEntry
                    
                    Timber.d("语音识别结果缓存命中: $cacheKey")
                    return@withContext cacheEntry.metadata as? SpeechResultCacheMetadata
                }
                
                // 缓存未命中或已过期
                memoryCache.remove(cacheKey)
                null
            } catch (e: Exception) {
                Timber.e(e, "获取语音识别结果缓存失败")
                null
            }
        }
    }
    
    /**
     * 生成TTS缓存键
     */
    private fun generateTtsCacheKey(text: String, language: String, config: TtsConfig): String {
        val input = "$text|$language|${config.speechRate}|${config.pitch}"
        return generateHash(input)
    }
    
    /**
     * 生成语音识别结果缓存键
     */
    private fun generateSpeechResultCacheKey(audioHash: String, language: String): String {
        return generateHash("$audioHash|$language")
    }
    
    /**
     * 生成哈希值
     */
    private fun generateHash(input: String): String {
        val digest = MessageDigest.getInstance("MD5")
        val hashBytes = digest.digest(input.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }
    
    /**
     * 保存缓存元数据
     */
    private suspend fun saveCacheMetadata(cacheEntry: CacheEntry) {
        try {
            val metadataFile = File(cacheDir, "${cacheEntry.key}.meta")
            val metadataJson = json.encodeToString(cacheEntry)
            metadataFile.writeText(metadataJson)
        } catch (e: Exception) {
            Timber.e(e, "保存缓存元数据失败")
        }
    }
    
    /**
     * 加载缓存元数据
     */
    private suspend fun loadCacheMetadata(cacheKey: String): CacheEntry? {
        return try {
            val metadataFile = File(cacheDir, "$cacheKey.meta")
            if (metadataFile.exists()) {
                val metadataJson = metadataFile.readText()
                json.decodeFromString<CacheEntry>(metadataJson)
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "加载缓存元数据失败")
            null
        }
    }
    
    /**
     * 检查缓存是否有效
     */
    private fun isCacheValid(cacheEntry: CacheEntry): Boolean {
        val now = System.currentTimeMillis()
        val expiryTime = cacheEntry.createdAt + (CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000)
        return now < expiryTime
    }
    
    /**
     * 启动定期清理任务
     */
    private fun startPeriodicCleanup() {
        cacheScope.launch {
            while (isActive) {
                try {
                    cleanupExpiredCache()
                    delay(CLEANUP_INTERVAL_HOURS * 60 * 60 * 1000) // 24小时
                } catch (e: Exception) {
                    Timber.e(e, "缓存清理任务失败")
                    delay(60 * 60 * 1000) // 1小时后重试
                }
            }
        }
    }
    
    /**
     * 清理过期缓存
     */
    suspend fun cleanupExpiredCache() {
        withContext(Dispatchers.IO) {
            try {
                val now = System.currentTimeMillis()
                val expiredKeys = mutableListOf<String>()
                
                // 检查内存缓存
                memoryCache.forEach { (key, entry) ->
                    if (!isCacheValid(entry)) {
                        expiredKeys.add(key)
                    }
                }
                
                // 删除过期缓存
                expiredKeys.forEach { key ->
                    deleteCacheEntry(key)
                }
                
                // 检查磁盘缓存大小
                val totalSize = calculateCacheSize()
                if (totalSize > MAX_DISK_CACHE_SIZE_MB * 1024 * 1024) {
                    cleanupLRUCache()
                }
                
                Timber.d("缓存清理完成，删除了 ${expiredKeys.size} 个过期条目")
            } catch (e: Exception) {
                Timber.e(e, "缓存清理失败")
            }
        }
    }
    
    /**
     * LRU缓存清理
     */
    private suspend fun cleanupLRUCache() {
        val sortedEntries = memoryCache.values.sortedBy { it.lastAccessedAt }
        val targetSize = MAX_DISK_CACHE_SIZE_MB * 1024 * 1024 * 0.8 // 清理到80%
        
        var currentSize = calculateCacheSize()
        var deletedCount = 0
        
        for (entry in sortedEntries) {
            if (currentSize <= targetSize) break
            
            deleteCacheEntry(entry.key)
            currentSize -= entry.size
            deletedCount++
        }
        
        Timber.d("LRU清理完成，删除了 $deletedCount 个条目")
    }
    
    /**
     * 删除缓存条目
     */
    private suspend fun deleteCacheEntry(cacheKey: String) {
        try {
            val cacheEntry = memoryCache.remove(cacheKey)
            
            // 删除音频文件
            if (cacheEntry?.filePath?.isNotEmpty() == true) {
                File(cacheEntry.filePath).delete()
            }
            
            // 删除元数据文件
            File(cacheDir, "$cacheKey.meta").delete()
        } catch (e: Exception) {
            Timber.e(e, "删除缓存条目失败: $cacheKey")
        }
    }
    
    /**
     * 计算缓存大小
     */
    private fun calculateCacheSize(): Long {
        return try {
            cacheDir.walkTopDown()
                .filter { it.isFile }
                .sumOf { it.length() }
        } catch (e: Exception) {
            Timber.e(e, "计算缓存大小失败")
            0L
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): CacheStats {
        val totalEntries = memoryCache.size
        val totalSize = calculateCacheSize()
        val ttsEntries = memoryCache.values.count { it.type == CacheType.TTS_AUDIO }
        val speechResultEntries = memoryCache.values.count { it.type == CacheType.SPEECH_RESULT }
        
        return CacheStats(
            totalEntries = totalEntries,
            totalSizeBytes = totalSize,
            ttsAudioEntries = ttsEntries,
            speechResultEntries = speechResultEntries,
            hitRate = 0f // 需要额外统计
        )
    }
    
    /**
     * 清空所有缓存
     */
    suspend fun clearAllCache() {
        withContext(Dispatchers.IO) {
            try {
                memoryCache.clear()
                cacheDir.deleteRecursively()
                cacheDir.mkdirs()
                ttsAudioDir.mkdirs()
                speechResultDir.mkdirs()
                
                Timber.d("所有缓存已清空")
            } catch (e: Exception) {
                Timber.e(e, "清空缓存失败")
            }
        }
    }
}

/**
 * 缓存条目
 */
@Serializable
data class CacheEntry(
    val key: String,
    val type: CacheType,
    val filePath: String,
    val metadata: CacheMetadata,
    val createdAt: Long,
    var lastAccessedAt: Long,
    val size: Long
)

/**
 * 缓存类型
 */
@Serializable
enum class CacheType {
    TTS_AUDIO,
    SPEECH_RESULT
}

/**
 * 缓存元数据基类
 */
@Serializable
sealed class CacheMetadata

/**
 * TTS缓存元数据
 */
@Serializable
data class TtsCacheMetadata(
    val text: String,
    val language: String,
    val config: TtsConfig
) : CacheMetadata()

/**
 * 语音识别结果缓存元数据
 */
@Serializable
data class SpeechResultCacheMetadata(
    val audioHash: String,
    val language: String,
    val result: String,
    val confidence: Float
) : CacheMetadata()

/**
 * TTS配置
 */
@Serializable
data class TtsConfig(
    val speechRate: Float = 1.0f,
    val pitch: Float = 1.0f,
    val volume: Float = 1.0f
)

/**
 * 缓存统计信息
 */
data class CacheStats(
    val totalEntries: Int,
    val totalSizeBytes: Long,
    val ttsAudioEntries: Int,
    val speechResultEntries: Int,
    val hitRate: Float
) {
    val totalSizeMB: Float
        get() = totalSizeBytes / (1024f * 1024f)
}
