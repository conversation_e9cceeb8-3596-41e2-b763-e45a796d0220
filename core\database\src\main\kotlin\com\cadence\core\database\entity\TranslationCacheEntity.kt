package com.cadence.core.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable

/**
 * 翻译缓存数据库实体
 * 用于缓存翻译结果，提高响应速度和离线体验
 */
@Entity(
    tableName = "translation_cache",
    indices = [
        Index(value = ["cache_key"], unique = true),
        Index(value = ["source_language_code", "target_language_code"]),
        Index(value = ["created_at"]),
        Index(value = ["access_count"])
    ]
)
@Serializable
data class TranslationCacheEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,

    @ColumnInfo(name = "cache_key")
    val cacheKey: String,

    @ColumnInfo(name = "translation_id")
    val translationId: String,

    @ColumnInfo(name = "source_text")
    val sourceText: String,

    @ColumnInfo(name = "translated_text")
    val translatedText: String,

    @ColumnInfo(name = "source_language_code")
    val sourceLanguageCode: String,

    @ColumnInfo(name = "source_language_name")
    val sourceLanguageName: String,

    @ColumnInfo(name = "source_region_code")
    val sourceRegionCode: String? = null,

    @ColumnInfo(name = "source_region_name")
    val sourceRegionName: String? = null,

    @ColumnInfo(name = "target_language_code")
    val targetLanguageCode: String,

    @ColumnInfo(name = "target_language_name")
    val targetLanguageName: String,

    @ColumnInfo(name = "target_region_code")
    val targetRegionCode: String? = null,

    @ColumnInfo(name = "target_region_name")
    val targetRegionName: String? = null,

    @ColumnInfo(name = "confidence_score")
    val confidenceScore: Float? = null,

    @ColumnInfo(name = "cultural_context")
    val culturalContext: String? = null,

    @ColumnInfo(name = "translation_source")
    val translationSource: String, // "online", "offline", "hybrid"

    @ColumnInfo(name = "processing_time_ms")
    val processingTimeMs: Long = 0L,

    @ColumnInfo(name = "access_count")
    val accessCount: Int = 0,

    @ColumnInfo(name = "last_accessed_at")
    val lastAccessedAt: Long,

    @ColumnInfo(name = "created_at")
    val createdAt: Long,

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long,

    @ColumnInfo(name = "expires_at")
    val expiresAt: Long, // 缓存过期时间

    @ColumnInfo(name = "is_favorite")
    val isFavorite: Boolean = false,

    @ColumnInfo(name = "cache_size_bytes")
    val cacheSizeBytes: Int = 0, // 缓存条目大小，用于缓存管理

    @ColumnInfo(name = "cache_priority")
    val cachePriority: Int = 0, // 缓存优先级，用于LRU清理

    @ColumnInfo(name = "is_pinned")
    val isPinned: Boolean = false // 是否固定缓存（不会被自动清理）
) {
    companion object {
        // 缓存优先级常量
        const val PRIORITY_LOW = 0
        const val PRIORITY_NORMAL = 1
        const val PRIORITY_HIGH = 2
        const val PRIORITY_CRITICAL = 3

        // 缓存来源常量
        const val SOURCE_ONLINE = "online"
        const val SOURCE_OFFLINE = "offline"
        const val SOURCE_HYBRID = "hybrid"

        // 默认缓存过期时间（24小时）
        const val DEFAULT_CACHE_DURATION_MS = 24 * 60 * 60 * 1000L

        // 高频使用缓存过期时间（7天）
        const val HIGH_USAGE_CACHE_DURATION_MS = 7 * 24 * 60 * 60 * 1000L

        // 收藏缓存过期时间（30天）
        const val FAVORITE_CACHE_DURATION_MS = 30 * 24 * 60 * 60 * 1000L
    }

    /**
     * 检查缓存是否过期
     */
    fun isExpired(): Boolean {
        return System.currentTimeMillis() > expiresAt
    }

    /**
     * 检查是否为高频使用缓存
     */
    fun isHighUsage(): Boolean {
        return accessCount >= 5
    }

    /**
     * 计算缓存年龄（小时）
     */
    fun getCacheAgeHours(): Long {
        return (System.currentTimeMillis() - createdAt) / (60 * 60 * 1000L)
    }

    /**
     * 计算最后访问时间间隔（小时）
     */
    fun getLastAccessAgeHours(): Long {
        return (System.currentTimeMillis() - lastAccessedAt) / (60 * 60 * 1000L)
    }

    /**
     * 获取缓存权重（用于LRU算法）
     */
    fun getCacheWeight(): Float {
        val ageWeight = 1.0f / (getCacheAgeHours() + 1)
        val accessWeight = accessCount.toFloat() / 10.0f
        val priorityWeight = cachePriority.toFloat() / 3.0f
        val favoriteWeight = if (isFavorite) 2.0f else 1.0f
        val pinnedWeight = if (isPinned) 5.0f else 1.0f

        return ageWeight * accessWeight * priorityWeight * favoriteWeight * pinnedWeight
    }

    /**
     * 创建更新的缓存实体（增加访问次数）
     */
    fun withIncrementedAccess(): TranslationCacheEntity {
        val currentTime = System.currentTimeMillis()
        return copy(
            accessCount = accessCount + 1,
            lastAccessedAt = currentTime,
            updatedAt = currentTime,
            // 高频使用的缓存延长过期时间
            expiresAt = if (accessCount >= 5) {
                currentTime + HIGH_USAGE_CACHE_DURATION_MS
            } else {
                expiresAt
            }
        )
    }

    /**
     * 创建收藏状态更新的缓存实体
     */
    fun withFavoriteStatus(favorite: Boolean): TranslationCacheEntity {
        val currentTime = System.currentTimeMillis()
        return copy(
            isFavorite = favorite,
            updatedAt = currentTime,
            // 收藏的缓存延长过期时间
            expiresAt = if (favorite) {
                currentTime + FAVORITE_CACHE_DURATION_MS
            } else {
                currentTime + DEFAULT_CACHE_DURATION_MS
            },
            cachePriority = if (favorite) PRIORITY_HIGH else PRIORITY_NORMAL
        )
    }

    /**
     * 创建固定状态更新的缓存实体
     */
    fun withPinnedStatus(pinned: Boolean): TranslationCacheEntity {
        return copy(
            isPinned = pinned,
            updatedAt = System.currentTimeMillis(),
            cachePriority = if (pinned) PRIORITY_CRITICAL else cachePriority
        )
    }
}
