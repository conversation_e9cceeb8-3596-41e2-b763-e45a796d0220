package com.cadence.feature.offline

import kotlinx.serialization.Serializable

/**
 * 同步状态
 */
@Serializable
data class SyncState(
    val isInitializing: <PERSON><PERSON><PERSON> = false,
    val isReady: <PERSON><PERSON>an = false,
    val isSyncing: Boolean = false,
    val lastSyncTime: Long = 0L,
    val pendingItemsCount: Int = 0,
    val error: String? = null
)

/**
 * 同步项
 */
@Serializable
data class SyncItem(
    val id: String,
    val dataType: SyncDataType,
    val itemId: String,
    val data: String,
    val operation: SyncOperation,
    val timestamp: Long,
    val status: SyncStatus = SyncStatus.PENDING,
    val retryCount: Int = 0,
    val error: String? = null
)

/**
 * 同步数据类型
 */
@Serializable
enum class SyncDataType {
    TRANSLATION_HISTORY,    // 翻译历史
    FAVORITES,             // 收藏夹
    LEARNING_PROGRESS,     // 学习进度
    USER_SETTINGS,         // 用户设置
    CUSTOM_DICTIONARY      // 自定义词典
}

/**
 * 同步操作类型
 */
@Serializable
enum class SyncOperation {
    CREATE,    // 创建
    UPDATE,    // 更新
    DELETE     // 删除
}

/**
 * 同步状态
 */
@Serializable
enum class SyncStatus {
    PENDING,     // 待同步
    SYNCING,     // 同步中
    COMPLETED,   // 已完成
    FAILED,      // 失败
    CONFLICT     // 冲突
}

/**
 * 同步结果
 */
data class SyncResult(
    val success: Boolean,
    val syncedCount: Int = 0,
    val conflictCount: Int = 0,
    val errorCount: Int = 0,
    val duration: Long = 0L,
    val message: String? = null,
    val error: String? = null
)

/**
 * 单个同步项结果
 */
data class SyncItemResult(
    val status: SyncStatus,
    val error: String? = null,
    val conflictData: ConflictData? = null
)

/**
 * 服务器数据
 */
data class ServerData(
    val data: String,
    val timestamp: Long,
    val version: Int = 1
)

/**
 * 冲突数据
 */
data class ConflictData(
    val localData: String,
    val serverData: String,
    val localTimestamp: Long,
    val serverTimestamp: Long
)

/**
 * 冲突日志
 */
@Serializable
data class ConflictLog(
    val id: String,
    val syncItemId: String,
    val dataType: SyncDataType,
    val itemId: String,
    val localData: String,
    val serverData: String,
    val localTimestamp: Long,
    val serverTimestamp: Long,
    val detectedTime: Long,
    val status: ConflictStatus = ConflictStatus.PENDING,
    val resolvedTime: Long? = null,
    val resolution: ConflictResolution? = null
)

/**
 * 冲突状态
 */
@Serializable
enum class ConflictStatus {
    PENDING,    // 待解决
    RESOLVED    // 已解决
}

/**
 * 冲突解决方案
 */
@Serializable
enum class ConflictResolution {
    USE_LOCAL,    // 使用本地数据
    USE_SERVER,   // 使用服务器数据
    MERGE         // 合并数据
}

/**
 * 冲突解决策略
 */
enum class ConflictResolutionStrategy {
    ASK_USER,      // 询问用户
    LOCAL_WINS,    // 本地优先
    SERVER_WINS,   // 服务器优先
    LATEST_WINS    // 最新优先
}

/**
 * 同步元数据
 */
@Serializable
data class SyncMetadata(
    val lastSyncTime: Long = 0L,
    val version: Int = 1
)

/**
 * 增量同步数据
 */
@Serializable
data class IncrementalSyncData(
    val dataType: SyncDataType,
    val lastSyncTimestamp: Long,
    val changes: List<SyncChange>
)

/**
 * 同步变更
 */
@Serializable
data class SyncChange(
    val itemId: String,
    val operation: SyncOperation,
    val data: String?,
    val timestamp: Long,
    val checksum: String? = null
)

/**
 * 同步配置
 */
@Serializable
data class SyncConfig(
    val autoSyncEnabled: Boolean = true,
    val syncInterval: Long = 300000L, // 5分钟
    val conflictResolutionStrategy: ConflictResolutionStrategy = ConflictResolutionStrategy.ASK_USER,
    val maxRetryCount: Int = 3,
    val syncTimeout: Long = 30000L, // 30秒
    val enableIncrementalSync: Boolean = true,
    val enableConflictLogging: Boolean = true
)

/**
 * 同步统计
 */
data class SyncStatistics(
    val totalSyncCount: Int = 0,
    val successfulSyncCount: Int = 0,
    val failedSyncCount: Int = 0,
    val conflictCount: Int = 0,
    val averageSyncDuration: Long = 0L,
    val lastSyncTime: Long = 0L,
    val dataTypeCounts: Map<SyncDataType, Int> = emptyMap()
)

/**
 * 网络状态
 */
enum class NetworkStatus {
    CONNECTED,      // 已连接
    DISCONNECTED,   // 已断开
    LIMITED,        // 受限连接
    UNKNOWN         // 未知状态
}

/**
 * 同步优先级
 */
enum class SyncPriority {
    HIGH,      // 高优先级
    MEDIUM,    // 中优先级
    LOW        // 低优先级
}

/**
 * 批量同步请求
 */
data class BatchSyncRequest(
    val items: List<SyncItem>,
    val priority: SyncPriority = SyncPriority.MEDIUM,
    val timeout: Long = 30000L
)

/**
 * 批量同步响应
 */
data class BatchSyncResponse(
    val results: List<SyncItemResult>,
    val overallSuccess: Boolean,
    val duration: Long,
    val errors: List<String> = emptyList()
)

/**
 * 同步队列状态
 */
data class SyncQueueStatus(
    val totalItems: Int,
    val pendingItems: Int,
    val syncingItems: Int,
    val failedItems: Int,
    val conflictItems: Int,
    val estimatedCompletionTime: Long? = null
)

/**
 * 数据完整性检查结果
 */
data class DataIntegrityResult(
    val isValid: Boolean,
    val corruptedItems: List<String> = emptyList(),
    val missingItems: List<String> = emptyList(),
    val checksumMismatches: List<String> = emptyList(),
    val repairSuggestions: List<String> = emptyList()
)
