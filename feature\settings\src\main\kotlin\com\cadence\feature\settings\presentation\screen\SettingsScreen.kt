package com.cadence.feature.settings.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.settings.presentation.component.*
import com.cadence.feature.settings.presentation.viewmodel.SettingsViewModel
import timber.log.Timber

/**
 * 设置主界面
 * 显示所有设置选项的入口
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateToThemeSettings: () -> Unit = {},
    onNavigateToLanguageSettings: () -> Unit = {},
    onNavigateToNotificationSettings: () -> Unit = {},
    onNavigateToPrivacySettings: () -> Unit = {},
    onNavigateToAbout: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    val userPreference by viewModel.userPreference.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 显示错误提示
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            Timber.e("设置界面错误: $error")
            // TODO: 显示Snackbar或Toast
            viewModel.clearError()
        }
    }

    // 显示成功消息
    LaunchedEffect(uiState.message) {
        uiState.message?.let { message ->
            Timber.d("设置界面消息: $message")
            // TODO: 显示Snackbar
            viewModel.clearMessage()
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "设置",
                        fontWeight = FontWeight.Bold
                    )
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            // 外观设置分组
            item {
                SettingsGroupTitle(title = "外观")
            }
            
            item {
                SettingsSelector(
                    title = "主题",
                    subtitle = "选择应用主题",
                    icon = Icons.Default.Palette,
                    selectedValue = when (userPreference?.themeMode) {
                        com.cadence.domain.model.ThemeMode.LIGHT -> "浅色"
                        com.cadence.domain.model.ThemeMode.DARK -> "深色"
                        com.cadence.domain.model.ThemeMode.SYSTEM -> "跟随系统"
                        else -> "跟随系统"
                    },
                    onClick = onNavigateToThemeSettings
                )
            }
            
            item {
                SettingsSelector(
                    title = "字体大小",
                    subtitle = "调整应用字体大小",
                    icon = Icons.Default.TextFields,
                    selectedValue = when (userPreference?.fontSize) {
                        com.cadence.domain.model.FontSize.SMALL -> "小"
                        com.cadence.domain.model.FontSize.MEDIUM -> "中"
                        com.cadence.domain.model.FontSize.LARGE -> "大"
                        else -> "中"
                    },
                    onClick = {
                        // TODO: 实现字体大小选择对话框
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 语言设置分组
            item {
                SettingsGroupTitle(title = "语言")
            }
            
            item {
                SettingsItem(
                    title = "语言偏好",
                    subtitle = "设置默认翻译语言",
                    icon = Icons.Default.Language,
                    onClick = onNavigateToLanguageSettings
                )
            }
            
            item {
                SettingsSwitch(
                    title = "自动检测语言",
                    subtitle = "自动识别输入文本的语言",
                    icon = Icons.Default.AutoAwesome,
                    checked = userPreference?.autoDetectLanguage ?: true,
                    onCheckedChange = { viewModel.toggleAutoDetectLanguage() }
                )
            }

            item {
                SettingsDivider()
            }

            // 功能设置分组
            item {
                SettingsGroupTitle(title = "功能")
            }
            
            item {
                SettingsSwitch(
                    title = "保存翻译历史",
                    subtitle = "自动保存翻译记录",
                    icon = Icons.Default.History,
                    checked = userPreference?.saveTranslationHistory ?: true,
                    onCheckedChange = { viewModel.toggleSaveTranslationHistory() }
                )
            }
            
            item {
                SettingsSwitch(
                    title = "文化背景解释",
                    subtitle = "显示翻译的文化背景信息",
                    icon = Icons.Default.Public,
                    checked = userPreference?.enableCulturalContext ?: true,
                    onCheckedChange = { viewModel.toggleEnableCulturalContext() }
                )
            }
            
            item {
                SettingsSwitch(
                    title = "语音输入",
                    subtitle = "启用语音转文字功能",
                    icon = Icons.Default.Mic,
                    checked = userPreference?.enableVoiceInput ?: true,
                    onCheckedChange = { 
                        // TODO: 实现语音输入开关
                    }
                )
            }
            
            item {
                SettingsSwitch(
                    title = "语音输出",
                    subtitle = "启用文字转语音功能",
                    icon = Icons.Default.VolumeUp,
                    checked = userPreference?.enableVoiceOutput ?: true,
                    onCheckedChange = { 
                        // TODO: 实现语音输出开关
                    }
                )
            }
            
            item {
                SettingsSwitch(
                    title = "OCR识别",
                    subtitle = "启用图片文字识别功能",
                    icon = Icons.Default.CameraAlt,
                    checked = userPreference?.enableOcr ?: true,
                    onCheckedChange = { 
                        // TODO: 实现OCR开关
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 通知设置分组
            item {
                SettingsGroupTitle(title = "通知")
            }
            
            item {
                SettingsItem(
                    title = "通知设置",
                    subtitle = "管理应用通知",
                    icon = Icons.Default.Notifications,
                    onClick = onNavigateToNotificationSettings
                )
            }

            item {
                SettingsDivider()
            }

            // 隐私与安全分组
            item {
                SettingsGroupTitle(title = "隐私与安全")
            }
            
            item {
                SettingsItem(
                    title = "隐私设置",
                    subtitle = "管理数据和隐私",
                    icon = Icons.Default.Security,
                    onClick = onNavigateToPrivacySettings
                )
            }
            
            item {
                SettingsItem(
                    title = "数据清理",
                    subtitle = "清理缓存和历史数据",
                    icon = Icons.Default.CleaningServices,
                    onClick = {
                        // TODO: 实现数据清理功能
                    }
                )
            }

            item {
                SettingsDivider()
            }

            // 其他设置分组
            item {
                SettingsGroupTitle(title = "其他")
            }
            
            item {
                SettingsItem(
                    title = "关于应用",
                    subtitle = "版本信息和帮助",
                    icon = Icons.Default.Info,
                    onClick = onNavigateToAbout
                )
            }
            
            item {
                SettingsItem(
                    title = "重置设置",
                    subtitle = "恢复所有设置为默认值",
                    icon = Icons.Default.RestartAlt,
                    onClick = {
                        // TODO: 显示确认对话框
                        viewModel.resetToDefault()
                    }
                )
            }

            // 底部间距
            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }

    // 加载指示器
    if (uiState.isLoading) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = androidx.compose.ui.Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}
