package com.cadence.core.database.di

import android.content.Context
import androidx.room.Room
import com.cadence.core.database.CadenceDatabase
import com.cadence.core.database.dao.*
import com.cadence.core.database.dao.learning.*
import com.cadence.core.database.dao.culture.*
import com.cadence.core.database.migration.MIGRATION_4_5
import com.cadence.core.database.migration.MIGRATION_5_6
import com.cadence.core.database.migration.MIGRATION_6_7
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import timber.log.Timber

/**
 * 数据库依赖注入模块
 * 提供数据库和DAO的依赖注入配置
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * 提供数据库实例
     */
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): CadenceDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            CadenceDatabase::class.java,
            CadenceDatabase.DATABASE_NAME
        )
            .addCallback(DatabaseInitCallback())
            .addMigrations(MIGRATION_4_5, MIGRATION_5_6, MIGRATION_6_7)
            // 性能优化配置
            .setQueryCallback(DatabaseQueryCallback(), null)
            .setJournalMode(RoomDatabase.JournalMode.WAL) // 使用WAL模式提高并发性能
            .build()
    }
    
    /**
     * 提供翻译记录DAO
     */
    @Provides
    fun provideTranslationDao(database: CadenceDatabase): TranslationDao {
        return database.translationDao()
    }
    
    /**
     * 提供语言区域DAO
     */
    @Provides
    fun provideLanguageRegionDao(database: CadenceDatabase): LanguageRegionDao {
        return database.languageRegionDao()
    }
    
    /**
     * 提供用户偏好DAO
     */
    @Provides
    fun provideUserPreferenceDao(database: CadenceDatabase): UserPreferenceDao {
        return database.userPreferenceDao()
    }
    
    /**
     * 提供翻译缓存DAO
     */
    @Provides
    fun provideTranslationCacheDao(database: CadenceDatabase): TranslationCacheDao {
        return database.translationCacheDao()
    }

    /**
     * 提供标签DAO
     */
    @Provides
    fun provideTagDao(database: CadenceDatabase): TagDao {
        return database.tagDao()
    }

    /**
     * 提供同步DAO
     */
    @Provides
    fun provideSyncDao(database: CadenceDatabase): SyncDao {
        return database.syncDao()
    }

    /**
     * 提供离线词典DAO
     */
    @Provides
    fun provideOfflineDictionaryDao(database: CadenceDatabase): OfflineDictionaryDao {
        return database.offlineDictionaryDao()
    }

    /**
     * 提供收藏DAO
     */
    @Provides
    fun provideFavoriteDao(database: CadenceDatabase): FavoriteDao {
        return database.favoriteDao()
    }

    /**
     * 提供单词DAO
     */
    @Provides
    fun provideWordDao(database: CadenceDatabase): WordDao {
        return database.wordDao()
    }

    /**
     * 提供学习进度DAO
     */
    @Provides
    fun provideLearningProgressDao(database: CadenceDatabase): LearningProgressDao {
        return database.learningProgressDao()
    }

    /**
     * 提供学习会话DAO
     */
    @Provides
    fun provideStudySessionDao(database: CadenceDatabase): StudySessionDao {
        return database.studySessionDao()
    }

    /**
     * 提供文化背景DAO
     */
    @Provides
    fun provideCulturalContextDao(database: CadenceDatabase): CulturalContextDao {
        return database.culturalContextDao()
    }

    /**
     * 提供使用上下文DAO
     */
    @Provides
    fun provideUsageContextDao(database: CadenceDatabase): UsageContextDao {
        return database.usageContextDao()
    }

    /**
     * 提供区域差异DAO
     */
    @Provides
    fun provideRegionalDifferenceDao(database: CadenceDatabase): RegionalDifferenceDao {
        return database.regionalDifferenceDao()
    }

    /**
     * 提供数据库缓存管理器
     */
    @Provides
    @Singleton
    fun provideDatabaseCacheManager(
        translationCacheDao: TranslationCacheDao,
        userPreferenceDao: UserPreferenceDao
    ): com.cadence.core.database.cache.DatabaseCacheManager {
        return com.cadence.core.database.cache.DatabaseCacheManager(
            translationCacheDao,
            userPreferenceDao
        )
    }

    /**
     * 提供数据库健康检查器
     */
    @Provides
    @Singleton
    fun provideDatabaseHealthChecker(
        database: CadenceDatabase,
        translationDao: TranslationDao,
        translationCacheDao: TranslationCacheDao,
        userPreferenceDao: UserPreferenceDao,
        languageRegionDao: LanguageRegionDao
    ): com.cadence.core.database.health.DatabaseHealthChecker {
        return com.cadence.core.database.health.DatabaseHealthChecker(
            database,
            translationDao,
            translationCacheDao,
            userPreferenceDao,
            languageRegionDao
        )
    }
}

/**
 * 数据库初始化回调
 * 在数据库创建时执行初始化操作
 */
private class DatabaseInitCallback : androidx.room.RoomDatabase.Callback() {

    override fun onCreate(db: androidx.sqlite.db.SupportSQLiteDatabase) {
        super.onCreate(db)

        // 预填充语言区域数据
        initializeLanguageRegions(db)

        // 预填充用户偏好默认值
        initializeUserPreferences(db)

        // 预填充同步配置默认值
        initializeSyncConfig(db)
    }

    /**
     * 初始化语言区域数据
     */
    private fun initializeLanguageRegions(db: androidx.sqlite.db.SupportSQLiteDatabase) {
        val languageRegions = listOf(
            // 中文
            "('zh', 'CN', '中文（简体）', '中国大陆', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('zh', 'TW', '中文（繁体）', '台湾', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('zh', 'HK', '中文（繁体）', '香港', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('zh', 'SG', '中文（简体）', '新加坡', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 英文
            "('en', 'US', 'English', 'United States', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('en', 'GB', 'English', 'United Kingdom', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('en', 'AU', 'English', 'Australia', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('en', 'CA', 'English', 'Canada', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 日文
            "('ja', 'JP', '日本語', '日本', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 韩文
            "('ko', 'KR', '한국어', '대한민국', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 法文
            "('fr', 'FR', 'Français', 'France', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('fr', 'CA', 'Français', 'Canada', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 德文
            "('de', 'DE', 'Deutsch', 'Deutschland', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 西班牙文
            "('es', 'ES', 'Español', 'España', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",
            "('es', 'MX', 'Español', 'México', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 阿拉伯文
            "('ar', 'SA', 'العربية', 'المملكة العربية السعودية', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})",

            // 俄文
            "('ru', 'RU', 'Русский', 'Россия', 1, ${System.currentTimeMillis()}, ${System.currentTimeMillis()})"
        )

        val insertSql = """
            INSERT INTO language_regions (language_code, region_code, display_name, region_name, is_active, created_at, updated_at)
            VALUES ${languageRegions.joinToString(", ")}
        """.trimIndent()

        db.execSQL(insertSql)
    }

    /**
     * 初始化用户偏好默认值
     */
    private fun initializeUserPreferences(db: androidx.sqlite.db.SupportSQLiteDatabase) {
        val currentTime = System.currentTimeMillis()
        val defaultPreferences = """
            INSERT INTO user_preferences (key, value, created_at, updated_at) VALUES
            ('default_source_language', 'zh-CN', $currentTime, $currentTime),
            ('default_target_language', 'en-US', $currentTime, $currentTime),
            ('auto_detect_language', 'true', $currentTime, $currentTime),
            ('enable_voice_input', 'true', $currentTime, $currentTime),
            ('enable_voice_output', 'true', $currentTime, $currentTime),
            ('translation_history_limit', '1000', $currentTime, $currentTime),
            ('cache_expiry_hours', '24', $currentTime, $currentTime),
            ('enable_cultural_context', 'true', $currentTime, $currentTime),
            ('theme_mode', 'system', $currentTime, $currentTime),
            ('enable_notifications', 'true', $currentTime, $currentTime)
        """.trimIndent()

        db.execSQL(defaultPreferences)
    }

    /**
     * 初始化同步配置默认值
     */
    private fun initializeSyncConfig(db: androidx.sqlite.db.SupportSQLiteDatabase) {
        val currentTime = System.currentTimeMillis()
        val defaultSyncConfig = """
            INSERT INTO sync_config (id, isEnabled, autoSyncEnabled, syncInterval, lastSyncTime, conflictResolution, maxRetries, createdAt, updatedAt) VALUES
            ('default', 0, 0, 3600000, 0, 'LOCAL_WINS', 3, $currentTime, $currentTime)
        """.trimIndent()

        db.execSQL(defaultSyncConfig)
    }
}

/**
 * 数据库查询性能监控回调
 * 用于监控慢查询和数据库性能
 */
private class DatabaseQueryCallback : androidx.room.RoomDatabase.QueryCallback {

    override fun onQuery(sqlQuery: String, bindArgs: List<Any?>) {
        val startTime = System.currentTimeMillis()

        // 记录查询开始时间（可以在实际项目中使用更精确的计时）
        Timber.d("数据库查询开始: $sqlQuery")

        // 在实际实现中，可以在这里添加查询性能统计
        // 例如：记录查询时间、检测慢查询等
    }
}
