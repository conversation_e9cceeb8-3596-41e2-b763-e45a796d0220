package com.cadence.feature.learning.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.feature.learning.presentation.viewmodel.StatisticsViewModel
import com.cadence.feature.learning.presentation.component.*

/**
 * 学习统计分析界面
 * 显示详细的学习数据分析和趋势
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StatisticsScreen(
    onNavigateBack: () -> Unit,
    onNavigateToWordDetail: (String) -> Unit,
    viewModel: StatisticsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val overallStats by viewModel.overallStats.collectAsStateWithLifecycle()
    val weeklyProgress by viewModel.weeklyProgress.collectAsStateWithLifecycle()
    val monthlyProgress by viewModel.monthlyProgress.collectAsStateWithLifecycle()
    val categoryAnalysis by viewModel.categoryAnalysis.collectAsStateWithLifecycle()
    val learningTrends by viewModel.learningTrends.collectAsStateWithLifecycle()
    val achievements by viewModel.achievements.collectAsStateWithLifecycle()
    
    LaunchedEffect(Unit) {
        viewModel.loadStatisticsData()
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { Text("学习统计") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            actions = {
                IconButton(
                    onClick = { viewModel.refreshStatistics() }
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新"
                    )
                }
                
                IconButton(
                    onClick = { viewModel.exportStatistics() }
                ) {
                    Icon(
                        imageVector = Icons.Default.FileDownload,
                        contentDescription = "导出"
                    )
                }
            }
        )
        
        if (uiState.isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 总体统计概览
                item {
                    overallStats?.let { stats ->
                        OverallStatisticsCard(statistics = stats)
                    }
                }
                
                // 学习成就
                if (achievements.isNotEmpty()) {
                    item {
                        AchievementsSection(
                            achievements = achievements,
                            onAchievementClick = { /* 查看成就详情 */ }
                        )
                    }
                }
                
                // 时间段选择器
                item {
                    TimePeriodSelector(
                        selectedPeriod = uiState.selectedTimePeriod,
                        onPeriodChange = { period ->
                            viewModel.changeTimePeriod(period)
                        }
                    )
                }
                
                // 学习趋势图表
                item {
                    when (uiState.selectedTimePeriod) {
                        TimePeriod.WEEK -> {
                            LearningTrendsChart(
                                title = "本周学习趋势",
                                data = weeklyProgress,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                        TimePeriod.MONTH -> {
                            LearningTrendsChart(
                                title = "本月学习趋势",
                                data = monthlyProgress,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                        TimePeriod.YEAR -> {
                            LearningTrendsChart(
                                title = "年度学习趋势",
                                data = learningTrends,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                }
                
                // 分类分析
                item {
                    Text(
                        text = "分类学习分析",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                items(categoryAnalysis.entries.toList()) { (category, analysis) ->
                    CategoryAnalysisCard(
                        category = category,
                        analysis = analysis,
                        onCategoryClick = { /* 查看分类详情 */ }
                    )
                }
                
                // 学习效率分析
                item {
                    LearningEfficiencyCard(
                        efficiency = viewModel.calculateLearningEfficiency(),
                        recommendations = viewModel.getRecommendations()
                    )
                }
                
                // 详细数据表格
                item {
                    DetailedDataTable(
                        data = viewModel.getDetailedData(),
                        onRowClick = { wordId ->
                            onNavigateToWordDetail(wordId)
                        }
                    )
                }
            }
        }
        
        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误提示
            }
        }
    }
}

/**
 * 总体统计卡片
 */
@Composable
private fun OverallStatisticsCard(
    statistics: com.cadence.domain.model.learning.LearningStatistics,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "学习概览",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 主要指标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    icon = Icons.Default.School,
                    label = "总学习",
                    value = "${statistics.totalWordsLearned}",
                    unit = "单词",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    icon = Icons.Default.Star,
                    label = "已掌握",
                    value = "${statistics.masteredWords}",
                    unit = "单词",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    icon = Icons.Default.TrendingUp,
                    label = "正确率",
                    value = "${(statistics.averageAccuracy * 100).toInt()}",
                    unit = "%",
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    icon = Icons.Default.AccessTime,
                    label = "学习时长",
                    value = formatStudyTime(statistics.totalStudyTime),
                    unit = "",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    icon = Icons.Default.LocalFireDepartment,
                    label = "连续天数",
                    value = "${statistics.currentStreak}",
                    unit = "天",
                    modifier = Modifier.weight(1f)
                )
                
                StatisticItem(
                    icon = Icons.Default.Refresh,
                    label = "待复习",
                    value = "${statistics.wordsToReview}",
                    unit = "单词",
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 统计项组件
 */
@Composable
private fun StatisticItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(28.dp),
            tint = MaterialTheme.colorScheme.onPrimaryContainer
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = value,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
        
        if (unit.isNotEmpty()) {
            Text(
                text = unit,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = label,
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
        )
    }
}

/**
 * 时间段选择器
 */
@Composable
private fun TimePeriodSelector(
    selectedPeriod: TimePeriod,
    onPeriodChange: (TimePeriod) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        TimePeriod.values().forEach { period ->
            FilterChip(
                onClick = { onPeriodChange(period) },
                label = { Text(period.displayName) },
                selected = selectedPeriod == period,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 格式化学习时长
 */
private fun formatStudyTime(timeInMillis: Long): String {
    val hours = timeInMillis / (1000 * 60 * 60)
    val minutes = (timeInMillis % (1000 * 60 * 60)) / (1000 * 60)
    
    return when {
        hours > 0 -> "${hours}h${minutes}m"
        minutes > 0 -> "${minutes}m"
        else -> "< 1m"
    }
}

/**
 * 时间段枚举
 */
enum class TimePeriod(val displayName: String) {
    WEEK("本周"),
    MONTH("本月"),
    YEAR("本年")
}