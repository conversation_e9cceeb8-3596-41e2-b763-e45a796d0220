package com.cadence.feature.culture.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.domain.model.culture.*
import com.cadence.feature.culture.presentation.component.*
import com.cadence.feature.culture.presentation.viewmodel.CulturalRecommendationViewModel

/**
 * 文化推荐界面
 * 显示个性化的文化知识推荐
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalRecommendationScreen(
    onNavigateBack: () -> Unit,
    onNavigateToDetail: (String) -> Unit,
    onNavigateToSettings: () -> Unit,
    viewModel: CulturalRecommendationViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 初始化加载数据
    LaunchedEffect(Unit) {
        viewModel.loadRecommendations()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = "文化知识推荐",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshRecommendations() }) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "刷新"
                        )
                    }
                    IconButton(onClick = onNavigateToSettings) {
                        Icon(
                            imageVector = Icons.Default.Settings,
                            contentDescription = "设置"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.error != null -> {
                    ErrorMessage(
                        error = uiState.error,
                        onRetry = { viewModel.loadRecommendations() },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                else -> {
                    RecommendationContent(
                        uiState = uiState,
                        onRecommendationClick = onNavigateToDetail,
                        onTypeFilterChange = { type -> viewModel.filterByType(type) },
                        onDifficultyFilterChange = { difficulty -> viewModel.filterByDifficulty(difficulty) },
                        onRegionFilterChange = { region -> viewModel.filterByRegion(region) },
                        onLikeRecommendation = { id -> viewModel.likeRecommendation(id) },
                        onBookmarkRecommendation = { id -> viewModel.bookmarkRecommendation(id) },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 推荐内容区域
 */
@Composable
private fun RecommendationContent(
    uiState: CulturalRecommendationUiState,
    onRecommendationClick: (String) -> Unit,
    onTypeFilterChange: (CulturalKnowledgeType?) -> Unit,
    onDifficultyFilterChange: (CulturalDifficulty?) -> Unit,
    onRegionFilterChange: (String?) -> Unit,
    onLikeRecommendation: (String) -> Unit,
    onBookmarkRecommendation: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 筛选器区域
        item {
            RecommendationFilters(
                selectedType = uiState.selectedType,
                selectedDifficulty = uiState.selectedDifficulty,
                selectedRegion = uiState.selectedRegion,
                onTypeChange = onTypeFilterChange,
                onDifficultyChange = onDifficultyFilterChange,
                onRegionChange = onRegionFilterChange
            )
        }
        
        // 热门推荐
        if (uiState.popularRecommendations.isNotEmpty()) {
            item {
                Text(
                    text = "热门推荐",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            item {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(
                        items = uiState.popularRecommendations,
                        key = { it.id }
                    ) { recommendation ->
                        PopularRecommendationCard(
                            recommendation = recommendation,
                            onClick = { onRecommendationClick(recommendation.id) },
                            onLike = { onLikeRecommendation(recommendation.id) },
                            onBookmark = { onBookmarkRecommendation(recommendation.id) }
                        )
                    }
                }
            }
        }
        
        // 个性化推荐
        if (uiState.personalizedRecommendations.isNotEmpty()) {
            item {
                Text(
                    text = "为你推荐",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            items(
                items = uiState.personalizedRecommendations,
                key = { it.id }
            ) { recommendation ->
                RecommendationCard(
                    recommendation = recommendation,
                    onClick = { onRecommendationClick(recommendation.id) },
                    onLike = { onLikeRecommendation(recommendation.id) },
                    onBookmark = { onBookmarkRecommendation(recommendation.id) }
                )
            }
        }
        
        // 分类推荐
        CulturalKnowledgeType.values().forEach { type ->
            val typeRecommendations = uiState.recommendationsByType[type] ?: emptyList()
            if (typeRecommendations.isNotEmpty()) {
                item {
                    Text(
                        text = getTypeDisplayName(type),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                items(
                    items = typeRecommendations.take(3), // 每个分类显示前3个
                    key = { "${type.name}_${it.id}" }
                ) { recommendation ->
                    RecommendationCard(
                        recommendation = recommendation,
                        onClick = { onRecommendationClick(recommendation.id) },
                        onLike = { onLikeRecommendation(recommendation.id) },
                        onBookmark = { onBookmarkRecommendation(recommendation.id) }
                    )
                }
                
                if (typeRecommendations.size > 3) {
                    item {
                        TextButton(
                            onClick = { onTypeFilterChange(type) },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text("查看更多${getTypeDisplayName(type)}")
                            Icon(
                                imageVector = Icons.Default.ArrowForward,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                }
            }
        }
        
        // 底部间距
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 获取文化知识类型的显示名称
 */
private fun getTypeDisplayName(type: CulturalKnowledgeType): String {
    return when (type) {
        CulturalKnowledgeType.IDIOM -> "习语表达"
        CulturalKnowledgeType.PROVERB -> "谚语俗语"
        CulturalKnowledgeType.SLANG -> "俚语口语"
        CulturalKnowledgeType.ETIQUETTE -> "礼仪文化"
        CulturalKnowledgeType.TRADITION -> "传统习俗"
        CulturalKnowledgeType.HISTORY -> "历史背景"
        CulturalKnowledgeType.RELIGION -> "宗教文化"
        CulturalKnowledgeType.FOOD -> "饮食文化"
        CulturalKnowledgeType.FESTIVAL -> "节日庆典"
        CulturalKnowledgeType.ART -> "艺术文化"
        CulturalKnowledgeType.LITERATURE -> "文学典故"
        CulturalKnowledgeType.BUSINESS -> "商务文化"
        CulturalKnowledgeType.SOCIAL -> "社交文化"
        CulturalKnowledgeType.TABOO -> "禁忌文化"
        CulturalKnowledgeType.HUMOR -> "幽默文化"
    }
}

/**
 * 加载指示器
 */
@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator()
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "正在加载推荐内容...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 错误消息
 */
@Composable
private fun ErrorMessage(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}