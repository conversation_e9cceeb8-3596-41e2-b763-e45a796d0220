package com.cadence.core.database.dao.culture

import androidx.room.*
import com.cadence.core.database.entity.culture.CulturalRecommendationEntity
import kotlinx.coroutines.flow.Flow

/**
 * 文化推荐DAO
 */
@Dao
interface CulturalRecommendationDao {
    
    // 基础CRUD操作
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecommendation(recommendation: CulturalRecommendationEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecommendations(recommendations: List<CulturalRecommendationEntity>)
    
    @Update
    suspend fun updateRecommendation(recommendation: CulturalRecommendationEntity)
    
    @Delete
    suspend fun deleteRecommendation(recommendation: CulturalRecommendationEntity)
    
    @Query("DELETE FROM cultural_recommendations WHERE id = :recommendationId")
    suspend fun deleteRecommendationById(recommendationId: String)
    
    // 查询操作
    @Query("SELECT * FROM cultural_recommendations WHERE id = :recommendationId")
    suspend fun getRecommendationById(recommendationId: String): CulturalRecommendationEntity?
    
    @Query("SELECT * FROM cultural_recommendations WHERE type = :type ORDER BY popularity DESC")
    suspend fun getRecommendationsByType(type: String): List<CulturalRecommendationEntity>
    
    @Query("SELECT * FROM cultural_recommendations WHERE difficulty = :difficulty ORDER BY popularity DESC")
    suspend fun getRecommendationsByDifficulty(difficulty: String): List<CulturalRecommendationEntity>
    
    @Query("SELECT * FROM cultural_recommendations WHERE region = :region ORDER BY popularity DESC")
    suspend fun getRecommendationsByRegion(region: String): List<CulturalRecommendationEntity>
    
    @Query("SELECT * FROM cultural_recommendations WHERE language = :language ORDER BY popularity DESC")
    suspend fun getRecommendationsByLanguage(language: String): List<CulturalRecommendationEntity>
    
    @Query("""
        SELECT * FROM cultural_recommendations 
        WHERE title LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        OR content LIKE '%' || :query || '%'
        ORDER BY popularity DESC
    """)
    suspend fun searchRecommendations(query: String): List<CulturalRecommendationEntity>
    
    @Query("""
        SELECT * FROM cultural_recommendations 
        ORDER BY popularity DESC 
        LIMIT :limit OFFSET :offset
    """)
    suspend fun getRecommendationsPaged(limit: Int, offset: Int): List<CulturalRecommendationEntity>
    
    @Query("""
        SELECT * FROM cultural_recommendations 
        ORDER BY popularity DESC 
        LIMIT :limit
    """)
    suspend fun getTopRecommendations(limit: Int): List<CulturalRecommendationEntity>
    
    @Query("""
        SELECT * FROM cultural_recommendations 
        WHERE type = :type 
        AND difficulty = :difficulty 
        ORDER BY popularity DESC 
        LIMIT :limit
    """)
    suspend fun getRecommendationsByTypeAndDifficulty(
        type: String,
        difficulty: String,
        limit: Int
    ): List<CulturalRecommendationEntity>
    
    // Flow查询
    @Query("SELECT * FROM cultural_recommendations ORDER BY popularity DESC")
    fun getAllRecommendationsFlow(): Flow<List<CulturalRecommendationEntity>>
    
    @Query("""
        SELECT * FROM cultural_recommendations 
        WHERE type = :type 
        ORDER BY popularity DESC
    """)
    fun getRecommendationsByTypeFlow(type: String): Flow<List<CulturalRecommendationEntity>>
    
    // 统计查询
    @Query("SELECT COUNT(*) FROM cultural_recommendations")
    suspend fun getRecommendationCount(): Int
    
    @Query("SELECT COUNT(*) FROM cultural_recommendations WHERE type = :type")
    suspend fun getRecommendationCountByType(type: String): Int
    
    @Query("""
        SELECT type, COUNT(*) as count 
        FROM cultural_recommendations 
        GROUP BY type 
        ORDER BY count DESC
    """)
    suspend fun getRecommendationCountByTypes(): List<TypeCount>
    
    // 更新热度
    @Query("UPDATE cultural_recommendations SET popularity = popularity + 1 WHERE id = :recommendationId")
    suspend fun incrementPopularity(recommendationId: String)
}

/**
 * 类型统计数据
 */
data class TypeCount(
    val type: String,
    val count: Int
)