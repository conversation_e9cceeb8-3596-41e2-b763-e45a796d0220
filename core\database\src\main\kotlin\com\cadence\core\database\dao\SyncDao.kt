package com.cadence.core.database.dao

import androidx.room.*
import com.cadence.core.database.entity.*
import kotlinx.coroutines.flow.Flow

/**
 * 同步数据访问对象
 * 定义同步相关的数据库操作
 */
@Dao
interface SyncDao {
    
    // ========== 同步配置相关 ==========
    
    /**
     * 获取同步配置
     */
    @Query("SELECT * FROM sync_config WHERE id = 'default'")
    fun getSyncConfig(): Flow<SyncConfigEntity?>
    
    /**
     * 插入或更新同步配置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateSyncConfig(config: SyncConfigEntity)
    
    /**
     * 更新最后同步时间
     */
    @Query("UPDATE sync_config SET lastSyncTime = :timestamp, updatedAt = :updatedAt WHERE id = 'default'")
    suspend fun updateLastSyncTime(timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    // ========== 同步历史相关 ==========
    
    /**
     * 获取同步历史记录
     */
    @Query("SELECT * FROM sync_history ORDER BY startTime DESC LIMIT :limit")
    fun getSyncHistory(limit: Int): Flow<List<SyncHistoryEntity>>
    
    /**
     * 根据同步类型获取历史记录
     */
    @Query("SELECT * FROM sync_history WHERE syncType = :syncType ORDER BY startTime DESC LIMIT :limit")
    fun getSyncHistoryByType(syncType: String, limit: Int): Flow<List<SyncHistoryEntity>>
    
    /**
     * 插入同步历史记录
     */
    @Insert
    suspend fun insertSyncHistory(history: SyncHistoryEntity)
    
    /**
     * 删除指定时间之前的同步历史
     */
    @Query("DELETE FROM sync_history WHERE startTime < :timestamp")
    suspend fun deleteSyncHistoryBefore(timestamp: Long)
    
    /**
     * 获取最近的同步记录
     */
    @Query("SELECT * FROM sync_history WHERE syncType = :syncType ORDER BY startTime DESC LIMIT 1")
    suspend fun getLatestSyncHistory(syncType: String): SyncHistoryEntity?
    
    // ========== 同步冲突相关 ==========
    
    /**
     * 获取未解决的冲突
     */
    @Query("SELECT * FROM sync_conflicts WHERE resolved = 0 ORDER BY createdAt ASC")
    fun getUnresolvedConflicts(): Flow<List<SyncConflictEntity>>
    
    /**
     * 根据项目ID获取冲突
     */
    @Query("SELECT * FROM sync_conflicts WHERE itemId = :itemId AND resolved = 0")
    suspend fun getConflictsByItemId(itemId: String): List<SyncConflictEntity>
    
    /**
     * 插入同步冲突
     */
    @Insert
    suspend fun insertSyncConflict(conflict: SyncConflictEntity)
    
    /**
     * 标记冲突为已解决
     */
    @Query("UPDATE sync_conflicts SET resolved = 1, resolution = :resolution, resolvedAt = :resolvedAt WHERE id = :conflictId")
    suspend fun markConflictResolved(conflictId: String, resolution: String, resolvedAt: Long = System.currentTimeMillis())
    
    /**
     * 删除已解决的冲突
     */
    @Query("DELETE FROM sync_conflicts WHERE resolved = 1 AND resolvedAt < :timestamp")
    suspend fun deleteResolvedConflictsBefore(timestamp: Long)
    
    // ========== 设备信息相关 ==========
    
    /**
     * 获取所有设备信息
     */
    @Query("SELECT * FROM device_info ORDER BY lastActiveTime DESC")
    fun getAllDevices(): Flow<List<DeviceInfoEntity>>
    
    /**
     * 获取当前设备信息
     */
    @Query("SELECT * FROM device_info WHERE isCurrentDevice = 1 LIMIT 1")
    suspend fun getCurrentDevice(): DeviceInfoEntity?
    
    /**
     * 根据设备ID获取设备信息
     */
    @Query("SELECT * FROM device_info WHERE deviceId = :deviceId")
    suspend fun getDeviceById(deviceId: String): DeviceInfoEntity?
    
    /**
     * 插入或更新设备信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateDevice(device: DeviceInfoEntity)
    
    /**
     * 删除设备信息
     */
    @Query("DELETE FROM device_info WHERE deviceId = :deviceId")
    suspend fun deleteDevice(deviceId: String)
    
    /**
     * 更新设备活跃时间
     */
    @Query("UPDATE device_info SET lastActiveTime = :timestamp, updatedAt = :updatedAt WHERE deviceId = :deviceId")
    suspend fun updateDeviceActiveTime(deviceId: String, timestamp: Long, updatedAt: Long = System.currentTimeMillis())
    
    // ========== 同步队列相关 ==========
    
    /**
     * 获取待同步的队列项
     */
    @Query("SELECT * FROM sync_queue ORDER BY priority DESC, createdAt ASC LIMIT :limit")
    suspend fun getPendingSyncItems(limit: Int): List<SyncQueueEntity>
    
    /**
     * 根据类型获取待同步项
     */
    @Query("SELECT * FROM sync_queue WHERE itemType = :itemType ORDER BY priority DESC, createdAt ASC")
    suspend fun getPendingSyncItemsByType(itemType: String): List<SyncQueueEntity>
    
    /**
     * 插入同步队列项
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSyncQueueItem(item: SyncQueueEntity)
    
    /**
     * 删除同步队列项
     */
    @Query("DELETE FROM sync_queue WHERE id = :itemId")
    suspend fun deleteSyncQueueItem(itemId: String)
    
    /**
     * 更新重试次数
     */
    @Query("UPDATE sync_queue SET retryCount = retryCount + 1, lastError = :error, updatedAt = :updatedAt WHERE id = :itemId")
    suspend fun incrementRetryCount(itemId: String, error: String?, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 删除超过最大重试次数的项
     */
    @Query("DELETE FROM sync_queue WHERE retryCount >= maxRetries")
    suspend fun deleteFailedSyncItems()
    
    // ========== 同步统计相关 ==========
    
    /**
     * 获取同步统计信息
     */
    @Query("SELECT * FROM sync_statistics WHERE id = 'default'")
    suspend fun getSyncStatistics(): SyncStatisticsEntity?
    
    /**
     * 插入或更新同步统计
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateSyncStatistics(statistics: SyncStatisticsEntity)
    
    /**
     * 增加同步次数
     */
    @Query("UPDATE sync_statistics SET totalSyncs = totalSyncs + 1, updatedAt = :updatedAt WHERE id = 'default'")
    suspend fun incrementTotalSyncs(updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 增加成功同步次数
     */
    @Query("UPDATE sync_statistics SET successfulSyncs = successfulSyncs + 1, lastSyncTime = :syncTime, updatedAt = :updatedAt WHERE id = 'default'")
    suspend fun incrementSuccessfulSyncs(syncTime: Long, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 增加失败同步次数
     */
    @Query("UPDATE sync_statistics SET failedSyncs = failedSyncs + 1, updatedAt = :updatedAt WHERE id = 'default'")
    suspend fun incrementFailedSyncs(updatedAt: Long = System.currentTimeMillis())
    
    // ========== 数据版本相关 ==========
    
    /**
     * 获取数据版本信息
     */
    @Query("SELECT * FROM data_versions WHERE dataType = :dataType AND itemId = :itemId")
    suspend fun getDataVersion(dataType: String, itemId: String): DataVersionEntity?
    
    /**
     * 插入或更新数据版本
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateDataVersion(version: DataVersionEntity)
    
    /**
     * 获取指定类型的所有数据版本
     */
    @Query("SELECT * FROM data_versions WHERE dataType = :dataType ORDER BY lastModified DESC")
    suspend fun getDataVersionsByType(dataType: String): List<DataVersionEntity>
    
    /**
     * 删除数据版本记录
     */
    @Query("DELETE FROM data_versions WHERE dataType = :dataType AND itemId = :itemId")
    suspend fun deleteDataVersion(dataType: String, itemId: String)
    
    /**
     * 清理旧的数据版本记录
     */
    @Query("DELETE FROM data_versions WHERE lastModified < :timestamp")
    suspend fun cleanupOldDataVersions(timestamp: Long)
    
    // ========== 清理操作 ==========
    
    /**
     * 清理所有同步相关数据
     */
    @Transaction
    suspend fun cleanupAllSyncData() {
        val thirtyDaysAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000L)
        deleteSyncHistoryBefore(thirtyDaysAgo)
        deleteResolvedConflictsBefore(thirtyDaysAgo)
        deleteFailedSyncItems()
        cleanupOldDataVersions(thirtyDaysAgo)
    }
}
