package com.cadence.core.logging.di

import android.content.Context
import com.cadence.core.logging.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 日志模块的依赖注入配置
 * 提供所有日志相关的依赖
 */
@Module
@InstallIn(SingletonComponent::class)
object LoggingModule {
    
    /**
     * 提供日志压缩管理器
     */
    @Provides
    @Singleton
    fun provideLogCompressionManager(
        @ApplicationContext context: Context
    ): LogCompressionManager {
        return LogCompressionManager(context)
    }
    
    /**
     * 提供日志轮转管理器
     */
    @Provides
    @Singleton
    fun provideLogRotationManager(
        @ApplicationContext context: Context,
        logCompressionManager: LogCompressionManager
    ): LogRotationManager {
        return LogRotationManager(context, logCompressionManager)
    }
    
    /**
     * 提供日志清理管理器
     */
    @Provides
    @Singleton
    fun provideLogCleanupManager(
        @ApplicationContext context: Context
    ): LogCleanupManager {
        return LogCleanupManager(context)
    }
    
    /**
     * 提供日志文件管理器
     */
    @Provides
    @Singleton
    fun provideLogFileManager(
        @ApplicationContext context: Context
    ): LogFileManager {
        return LogFileManager(context)
    }
    
    /**
     * 提供日志格式化器
     */
    @Provides
    @Singleton
    fun provideLogFormatter(): LogFormatter {
        return LogFormatter()
    }
    
    /**
     * 提供结构化日志器
     */
    @Provides
    @Singleton
    fun provideStructuredLogger(
        @ApplicationContext context: Context,
        logFormatter: LogFormatter,
        logFileManager: LogFileManager
    ): StructuredLogger {
        return StructuredLogger(context, logFormatter, logFileManager)
    }
    
    /**
     * 提供错误分析器
     */
    @Provides
    @Singleton
    fun provideErrorAnalyzer(
        @ApplicationContext context: Context
    ): ErrorAnalyzer {
        return ErrorAnalyzer(context)
    }
    
    /**
     * 提供错误报告器
     */
    @Provides
    @Singleton
    fun provideErrorReporter(
        @ApplicationContext context: Context,
        errorAnalyzer: ErrorAnalyzer,
        structuredLogger: StructuredLogger
    ): ErrorReporter {
        return ErrorReporter(context, errorAnalyzer, structuredLogger)
    }
    
    /**
     * 提供错误分类器
     */
    @Provides
    @Singleton
    fun provideErrorClassifier(): ErrorClassifier {
        return ErrorClassifier()
    }
    
    /**
     * 提供错误恢复管理器
     */
    @Provides
    @Singleton
    fun provideErrorRecoveryManager(
        @ApplicationContext context: Context,
        structuredLogger: StructuredLogger
    ): ErrorRecoveryManager {
        return ErrorRecoveryManager(context, structuredLogger)
    }
    
    /**
     * 提供崩溃报告管理器
     */
    @Provides
    @Singleton
    fun provideCrashReportManager(
        @ApplicationContext context: Context,
        structuredLogger: StructuredLogger
    ): CrashReportManager {
        return CrashReportManager(context, structuredLogger)
    }
    
    /**
     * 提供全局异常处理器
     */
    @Provides
    @Singleton
    fun provideGlobalExceptionHandler(
        @ApplicationContext context: Context,
        errorClassifier: ErrorClassifier,
        errorRecoveryManager: ErrorRecoveryManager,
        crashReportManager: CrashReportManager,
        structuredLogger: StructuredLogger
    ): GlobalExceptionHandler {
        return GlobalExceptionHandler(
            context,
            errorClassifier,
            errorRecoveryManager,
            crashReportManager,
            structuredLogger
        )
    }
}
