package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志压缩管理器
 * 负责日志文件的压缩、解压和压缩策略管理
 */
@Singleton
class LogCompressionManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val COMPRESSION_BUFFER_SIZE = 8192
        private const val MIN_FILE_SIZE_FOR_COMPRESSION = 1024 // 1KB
        private const val COMPRESSION_LEVEL = 6 // 平衡压缩率和速度
        private const val MAX_CONCURRENT_COMPRESSIONS = 2
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 压缩状态管理
    private val _compressionState = MutableStateFlow(CompressionState())
    val compressionState: StateFlow<CompressionState> = _compressionState.asStateFlow()
    
    // 压缩队列
    private val compressionQueue = mutableListOf<File>()
    private val compressionLock = Any()
    
    // 活跃压缩任务计数
    private var activeCompressions = 0
    
    init {
        startCompressionProcessor()
        Timber.d("日志压缩管理器已初始化")
    }
    
    /**
     * 压缩日志文件
     */
    suspend fun compressLogFile(file: File): CompressionResult = withContext(Dispatchers.IO) {
        try {
            if (!file.exists()) {
                return@withContext CompressionResult(
                    success = false,
                    originalFile = file.name,
                    error = "文件不存在"
                )
            }
            
            if (file.length() < MIN_FILE_SIZE_FOR_COMPRESSION) {
                return@withContext CompressionResult(
                    success = false,
                    originalFile = file.name,
                    error = "文件太小，不需要压缩"
                )
            }
            
            val compressedFile = File(file.parent, "${file.name}.gz")
            
            val startTime = System.currentTimeMillis()
            val originalSize = file.length()
            
            // 执行压缩
            val success = performCompression(file, compressedFile)
            
            if (success) {
                val compressedSize = compressedFile.length()
                val compressionRatio = (originalSize - compressedSize).toDouble() / originalSize * 100
                val duration = System.currentTimeMillis() - startTime
                
                // 删除原文件
                if (file.delete()) {
                    val result = CompressionResult(
                        success = true,
                        originalFile = file.name,
                        compressedFile = compressedFile.name,
                        originalSize = originalSize,
                        compressedSize = compressedSize,
                        compressionRatio = compressionRatio,
                        duration = duration
                    )
                    
                    updateCompressionState(result)
                    
                    Timber.d("文件压缩成功: ${file.name} -> ${compressedFile.name}, 压缩率: ${String.format("%.1f", compressionRatio)}%")
                    
                    result
                } else {
                    // 删除原文件失败，清理压缩文件
                    compressedFile.delete()
                    CompressionResult(
                        success = false,
                        originalFile = file.name,
                        error = "删除原文件失败"
                    )
                }
            } else {
                CompressionResult(
                    success = false,
                    originalFile = file.name,
                    error = "压缩过程失败"
                )
            }
            
        } catch (e: Exception) {
            Timber.e(e, "压缩文件失败: ${file.name}")
            CompressionResult(
                success = false,
                originalFile = file.name,
                error = e.message ?: "未知错误"
            )
        }
    }
    
    /**
     * 解压日志文件
     */
    suspend fun decompressLogFile(compressedFile: File): DecompressionResult = withContext(Dispatchers.IO) {
        try {
            if (!compressedFile.exists()) {
                return@withContext DecompressionResult(
                    success = false,
                    compressedFile = compressedFile.name,
                    error = "压缩文件不存在"
                )
            }
            
            if (!compressedFile.name.endsWith(".gz")) {
                return@withContext DecompressionResult(
                    success = false,
                    compressedFile = compressedFile.name,
                    error = "不是有效的压缩文件"
                )
            }
            
            val originalFileName = compressedFile.name.removeSuffix(".gz")
            val decompressedFile = File(compressedFile.parent, originalFileName)
            
            val startTime = System.currentTimeMillis()
            val compressedSize = compressedFile.length()
            
            // 执行解压
            val success = performDecompression(compressedFile, decompressedFile)
            
            if (success) {
                val decompressedSize = decompressedFile.length()
                val duration = System.currentTimeMillis() - startTime
                
                val result = DecompressionResult(
                    success = true,
                    compressedFile = compressedFile.name,
                    decompressedFile = decompressedFile.name,
                    compressedSize = compressedSize,
                    decompressedSize = decompressedSize,
                    duration = duration
                )
                
                Timber.d("文件解压成功: ${compressedFile.name} -> ${decompressedFile.name}")
                
                result
            } else {
                DecompressionResult(
                    success = false,
                    compressedFile = compressedFile.name,
                    error = "解压过程失败"
                )
            }
            
        } catch (e: Exception) {
            Timber.e(e, "解压文件失败: ${compressedFile.name}")
            DecompressionResult(
                success = false,
                compressedFile = compressedFile.name,
                error = e.message ?: "未知错误"
            )
        }
    }
    
    /**
     * 添加文件到压缩队列
     */
    fun queueForCompression(file: File) {
        synchronized(compressionLock) {
            if (!compressionQueue.contains(file)) {
                compressionQueue.add(file)
                Timber.d("文件已添加到压缩队列: ${file.name}")
            }
        }
    }
    
    /**
     * 批量压缩文件
     */
    suspend fun compressFiles(files: List<File>): List<CompressionResult> = withContext(Dispatchers.IO) {
        val results = mutableListOf<CompressionResult>()
        
        files.forEach { file ->
            val result = compressLogFile(file)
            results.add(result)
        }
        
        results
    }
    
    /**
     * 执行压缩操作
     */
    private suspend fun performCompression(inputFile: File, outputFile: File): Boolean = withContext(Dispatchers.IO) {
        try {
            FileInputStream(inputFile).use { fis ->
                BufferedInputStream(fis, COMPRESSION_BUFFER_SIZE).use { bis ->
                    FileOutputStream(outputFile).use { fos ->
                        BufferedOutputStream(fos, COMPRESSION_BUFFER_SIZE).use { bos ->
                            GZIPOutputStream(bos).use { gzos ->
                                val buffer = ByteArray(COMPRESSION_BUFFER_SIZE)
                                var bytesRead: Int
                                
                                while (bis.read(buffer).also { bytesRead = it } != -1) {
                                    gzos.write(buffer, 0, bytesRead)
                                }
                                
                                gzos.finish()
                            }
                        }
                    }
                }
            }
            true
        } catch (e: Exception) {
            Timber.e(e, "压缩操作失败")
            // 清理可能创建的不完整文件
            if (outputFile.exists()) {
                outputFile.delete()
            }
            false
        }
    }
    
    /**
     * 执行解压操作
     */
    private suspend fun performDecompression(inputFile: File, outputFile: File): Boolean = withContext(Dispatchers.IO) {
        try {
            FileInputStream(inputFile).use { fis ->
                BufferedInputStream(fis, COMPRESSION_BUFFER_SIZE).use { bis ->
                    GZIPInputStream(bis).use { gzis ->
                        FileOutputStream(outputFile).use { fos ->
                            BufferedOutputStream(fos, COMPRESSION_BUFFER_SIZE).use { bos ->
                                val buffer = ByteArray(COMPRESSION_BUFFER_SIZE)
                                var bytesRead: Int
                                
                                while (gzis.read(buffer).also { bytesRead = it } != -1) {
                                    bos.write(buffer, 0, bytesRead)
                                }
                            }
                        }
                    }
                }
            }
            true
        } catch (e: Exception) {
            Timber.e(e, "解压操作失败")
            // 清理可能创建的不完整文件
            if (outputFile.exists()) {
                outputFile.delete()
            }
            false
        }
    }

    /**
     * 启动压缩处理器
     */
    private fun startCompressionProcessor() {
        scope.launch {
            while (isActive) {
                try {
                    delay(30000L) // 每30秒检查一次
                    processCompressionQueue()
                } catch (e: Exception) {
                    Timber.e(e, "压缩处理器错误")
                }
            }
        }
    }

    /**
     * 处理压缩队列
     */
    private suspend fun processCompressionQueue() = withContext(Dispatchers.IO) {
        synchronized(compressionLock) {
            if (compressionQueue.isEmpty() || activeCompressions >= MAX_CONCURRENT_COMPRESSIONS) {
                return@withContext
            }

            val filesToProcess = compressionQueue.take(MAX_CONCURRENT_COMPRESSIONS - activeCompressions)
            compressionQueue.removeAll(filesToProcess)

            filesToProcess.forEach { file ->
                scope.launch {
                    activeCompressions++
                    try {
                        compressLogFile(file)
                    } finally {
                        activeCompressions--
                    }
                }
            }
        }
    }

    /**
     * 读取压缩文件内容
     */
    suspend fun readCompressedFile(compressedFile: File): String? = withContext(Dispatchers.IO) {
        try {
            if (!compressedFile.exists() || !compressedFile.name.endsWith(".gz")) {
                return@withContext null
            }

            FileInputStream(compressedFile).use { fis ->
                BufferedInputStream(fis).use { bis ->
                    GZIPInputStream(bis).use { gzis ->
                        BufferedReader(InputStreamReader(gzis)).use { reader ->
                            reader.readText()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "读取压缩文件失败: ${compressedFile.name}")
            null
        }
    }

    /**
     * 获取压缩统计信息
     */
    suspend fun getCompressionStatistics(): CompressionStatistics = withContext(Dispatchers.IO) {
        val currentState = _compressionState.value
        val queueSize = synchronized(compressionLock) { compressionQueue.size }

        CompressionStatistics(
            totalCompressions = currentState.totalCompressions,
            totalOriginalSize = currentState.totalOriginalSize,
            totalCompressedSize = currentState.totalCompressedSize,
            averageCompressionRatio = currentState.averageCompressionRatio,
            queueSize = queueSize,
            activeCompressions = activeCompressions,
            lastCompressionTime = currentState.lastCompressionTime
        )
    }

    /**
     * 更新压缩状态
     */
    private fun updateCompressionState(result: CompressionResult) {
        val currentState = _compressionState.value
        val newTotalCompressions = currentState.totalCompressions + 1
        val newTotalOriginalSize = currentState.totalOriginalSize + result.originalSize
        val newTotalCompressedSize = currentState.totalCompressedSize + result.compressedSize
        val newAverageRatio = if (newTotalOriginalSize > 0) {
            ((newTotalOriginalSize - newTotalCompressedSize).toDouble() / newTotalOriginalSize) * 100
        } else {
            0.0
        }

        _compressionState.value = currentState.copy(
            totalCompressions = newTotalCompressions,
            totalOriginalSize = newTotalOriginalSize,
            totalCompressedSize = newTotalCompressedSize,
            averageCompressionRatio = newAverageRatio,
            lastCompressionTime = System.currentTimeMillis(),
            lastCompressedFile = result.compressedFile ?: ""
        )
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        synchronized(compressionLock) {
            compressionQueue.clear()
        }
        activeCompressions = 0
        Timber.d("日志压缩管理器已清理")
    }
}

// 数据类定义
data class CompressionResult(
    val success: Boolean,
    val originalFile: String,
    val compressedFile: String? = null,
    val originalSize: Long = 0,
    val compressedSize: Long = 0,
    val compressionRatio: Double = 0.0,
    val duration: Long = 0,
    val error: String? = null
)

data class DecompressionResult(
    val success: Boolean,
    val compressedFile: String,
    val decompressedFile: String? = null,
    val compressedSize: Long = 0,
    val decompressedSize: Long = 0,
    val duration: Long = 0,
    val error: String? = null
)

data class CompressionState(
    val totalCompressions: Long = 0,
    val totalOriginalSize: Long = 0,
    val totalCompressedSize: Long = 0,
    val averageCompressionRatio: Double = 0.0,
    val lastCompressionTime: Long = 0,
    val lastCompressedFile: String = ""
)

data class CompressionStatistics(
    val totalCompressions: Long,
    val totalOriginalSize: Long,
    val totalCompressedSize: Long,
    val averageCompressionRatio: Double,
    val queueSize: Int,
    val activeCompressions: Int,
    val lastCompressionTime: Long
)
