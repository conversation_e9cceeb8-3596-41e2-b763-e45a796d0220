package com.cadence.core.logging

import android.content.Context
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 日志清理管理器
 * 负责日志文件的定期清理、存储空间管理和保留策略
 */
@Singleton
class LogCleanupManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val LOG_DIR = "logs"
        private const val ARCHIVE_DIR = "logs/archive"
        private const val ERROR_REPORTS_DIR = "error_reports"
        
        // 清理策略配置
        private const val DEFAULT_RETENTION_DAYS = 30
        private const val CRITICAL_RETENTION_DAYS = 90
        private const val MAX_TOTAL_SIZE_MB = 500
        private const val CLEANUP_CHECK_INTERVAL_MS = 3600000L // 1小时
        
        // 存储空间阈值
        private const val LOW_STORAGE_THRESHOLD_MB = 100
        private const val CRITICAL_STORAGE_THRESHOLD_MB = 50
        
        // 清理优先级
        private const val PRIORITY_DEBUG_LOGS = 1
        private const val PRIORITY_INFO_LOGS = 2
        private const val PRIORITY_WARN_LOGS = 3
        private const val PRIORITY_ERROR_LOGS = 4
        private const val PRIORITY_CRITICAL_LOGS = 5
    }
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 目录引用
    private val logDir: File by lazy {
        File(context.filesDir, LOG_DIR).apply { if (!exists()) mkdirs() }
    }
    
    private val archiveDir: File by lazy {
        File(context.filesDir, ARCHIVE_DIR).apply { if (!exists()) mkdirs() }
    }
    
    private val errorReportsDir: File by lazy {
        File(context.filesDir, ERROR_REPORTS_DIR).apply { if (!exists()) mkdirs() }
    }
    
    // 清理状态管理
    private val _cleanupState = MutableStateFlow(CleanupState())
    val cleanupState: StateFlow<CleanupState> = _cleanupState.asStateFlow()
    
    // 保留策略配置
    private val retentionPolicies = ConcurrentHashMap<LogLevel, RetentionPolicy>()
    
    init {
        initializeRetentionPolicies()
        startCleanupMonitor()
        Timber.d("日志清理管理器已初始化")
    }
    
    /**
     * 初始化保留策略
     */
    private fun initializeRetentionPolicies() {
        retentionPolicies[LogLevel.VERBOSE] = RetentionPolicy(7, PRIORITY_DEBUG_LOGS)
        retentionPolicies[LogLevel.DEBUG] = RetentionPolicy(14, PRIORITY_DEBUG_LOGS)
        retentionPolicies[LogLevel.INFO] = RetentionPolicy(30, PRIORITY_INFO_LOGS)
        retentionPolicies[LogLevel.WARN] = RetentionPolicy(60, PRIORITY_WARN_LOGS)
        retentionPolicies[LogLevel.ERROR] = RetentionPolicy(90, PRIORITY_ERROR_LOGS)
        retentionPolicies[LogLevel.FATAL] = RetentionPolicy(180, PRIORITY_CRITICAL_LOGS)
    }
    
    /**
     * 执行清理操作
     */
    suspend fun performCleanup(cleanupType: CleanupType = CleanupType.SCHEDULED): CleanupResult = withContext(Dispatchers.IO) {
        try {
            val startTime = System.currentTimeMillis()
            var totalFilesDeleted = 0
            var totalSizeFreed = 0L
            val deletedFiles = mutableListOf<String>()
            
            Timber.d("开始执行日志清理，类型: $cleanupType")
            
            // 检查存储空间
            val storageInfo = checkStorageSpace()
            val isLowStorage = storageInfo.availableSpaceMB < LOW_STORAGE_THRESHOLD_MB
            val isCriticalStorage = storageInfo.availableSpaceMB < CRITICAL_STORAGE_THRESHOLD_MB
            
            // 根据存储情况调整清理策略
            val cleanupStrategy = determineCleanupStrategy(cleanupType, isLowStorage, isCriticalStorage)
            
            // 执行不同类型的清理
            when (cleanupStrategy) {
                CleanupStrategy.CONSERVATIVE -> {
                    val result = performConservativeCleanup()
                    totalFilesDeleted += result.filesDeleted
                    totalSizeFreed += result.sizeFreed
                    deletedFiles.addAll(result.deletedFiles)
                }
                CleanupStrategy.AGGRESSIVE -> {
                    val result = performAggressiveCleanup()
                    totalFilesDeleted += result.filesDeleted
                    totalSizeFreed += result.sizeFreed
                    deletedFiles.addAll(result.deletedFiles)
                }
                CleanupStrategy.EMERGENCY -> {
                    val result = performEmergencyCleanup()
                    totalFilesDeleted += result.filesDeleted
                    totalSizeFreed += result.sizeFreed
                    deletedFiles.addAll(result.deletedFiles)
                }
            }
            
            // 清理空目录
            cleanupEmptyDirectories()
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            val result = CleanupResult(
                success = true,
                filesDeleted = totalFilesDeleted,
                sizeFreed = totalSizeFreed,
                duration = duration,
                deletedFiles = deletedFiles,
                cleanupType = cleanupType,
                cleanupStrategy = cleanupStrategy
            )
            
            updateCleanupState(result)
            
            Timber.d("日志清理完成: 删除 $totalFilesDeleted 个文件，释放 ${totalSizeFreed / 1024 / 1024} MB")
            
            result
            
        } catch (e: Exception) {
            Timber.e(e, "日志清理失败")
            CleanupResult(
                success = false,
                filesDeleted = 0,
                sizeFreed = 0,
                duration = 0,
                deletedFiles = emptyList(),
                cleanupType = cleanupType,
                cleanupStrategy = CleanupStrategy.CONSERVATIVE,
                error = e.message
            )
        }
    }
    
    /**
     * 保守清理策略
     */
    private suspend fun performConservativeCleanup(): CleanupResult = withContext(Dispatchers.IO) {
        var filesDeleted = 0
        var sizeFreed = 0L
        val deletedFiles = mutableListOf<String>()
        
        // 清理过期的日志文件
        val expiredFiles = findExpiredFiles()
        expiredFiles.forEach { file ->
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("删除过期日志文件: ${file.name}")
            }
        }
        
        // 清理临时文件
        val tempFiles = findTemporaryFiles()
        tempFiles.forEach { file ->
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("删除临时文件: ${file.name}")
            }
        }
        
        CleanupResult(
            success = true,
            filesDeleted = filesDeleted,
            sizeFreed = sizeFreed,
            duration = 0,
            deletedFiles = deletedFiles,
            cleanupType = CleanupType.SCHEDULED,
            cleanupStrategy = CleanupStrategy.CONSERVATIVE
        )
    }
    
    /**
     * 激进清理策略
     */
    private suspend fun performAggressiveCleanup(): CleanupResult = withContext(Dispatchers.IO) {
        var filesDeleted = 0
        var sizeFreed = 0L
        val deletedFiles = mutableListOf<String>()
        
        // 先执行保守清理
        val conservativeResult = performConservativeCleanup()
        filesDeleted += conservativeResult.filesDeleted
        sizeFreed += conservativeResult.sizeFreed
        deletedFiles.addAll(conservativeResult.deletedFiles)
        
        // 清理低优先级日志
        val lowPriorityFiles = findLowPriorityFiles()
        lowPriorityFiles.forEach { file ->
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("删除低优先级日志文件: ${file.name}")
            }
        }
        
        // 清理大文件
        val largeFiles = findLargeFiles()
        largeFiles.take(5).forEach { file -> // 限制删除数量
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("删除大文件: ${file.name}")
            }
        }
        
        CleanupResult(
            success = true,
            filesDeleted = filesDeleted,
            sizeFreed = sizeFreed,
            duration = 0,
            deletedFiles = deletedFiles,
            cleanupType = CleanupType.LOW_STORAGE,
            cleanupStrategy = CleanupStrategy.AGGRESSIVE
        )
    }
    
    /**
     * 紧急清理策略
     */
    private suspend fun performEmergencyCleanup(): CleanupResult = withContext(Dispatchers.IO) {
        var filesDeleted = 0
        var sizeFreed = 0L
        val deletedFiles = mutableListOf<String>()
        
        // 先执行激进清理
        val aggressiveResult = performAggressiveCleanup()
        filesDeleted += aggressiveResult.filesDeleted
        sizeFreed += aggressiveResult.sizeFreed
        deletedFiles.addAll(aggressiveResult.deletedFiles)
        
        // 清理所有非关键日志
        val nonCriticalFiles = findNonCriticalFiles()
        nonCriticalFiles.forEach { file ->
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("紧急删除非关键日志文件: ${file.name}")
            }
        }
        
        // 清理压缩文件
        val compressedFiles = findCompressedFiles()
        compressedFiles.forEach { file ->
            val size = file.length()
            if (file.delete()) {
                filesDeleted++
                sizeFreed += size
                deletedFiles.add(file.name)
                Timber.d("紧急删除压缩文件: ${file.name}")
            }
        }
        
        CleanupResult(
            success = true,
            filesDeleted = filesDeleted,
            sizeFreed = sizeFreed,
            duration = 0,
            deletedFiles = deletedFiles,
            cleanupType = CleanupType.EMERGENCY,
            cleanupStrategy = CleanupStrategy.EMERGENCY
        )
    }
    
    /**
     * 查找过期文件
     */
    private fun findExpiredFiles(): List<File> {
        val expiredFiles = mutableListOf<File>()
        val now = System.currentTimeMillis()
        
        // 检查所有日志目录
        listOf(logDir, archiveDir, errorReportsDir).forEach { dir ->
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    val logLevel = determineLogLevel(file)
                    val retentionPolicy = retentionPolicies[logLevel] ?: RetentionPolicy(DEFAULT_RETENTION_DAYS, PRIORITY_INFO_LOGS)
                    val retentionTime = retentionPolicy.retentionDays * 24 * 60 * 60 * 1000L
                    
                    if (now - file.lastModified() > retentionTime) {
                        expiredFiles.add(file)
                    }
                }
            }
        }
        
        return expiredFiles.sortedBy { it.lastModified() }
    }
    
    /**
     * 查找临时文件
     */
    private fun findTemporaryFiles(): List<File> {
        val tempFiles = mutableListOf<File>()
        
        listOf(logDir, archiveDir, errorReportsDir).forEach { dir ->
            dir.listFiles { file ->
                file.name.endsWith(".tmp") || 
                file.name.endsWith(".temp") || 
                file.name.startsWith("temp_") ||
                file.name.contains("_temp_")
            }?.let { tempFiles.addAll(it) }
        }
        
        return tempFiles
    }
    
    /**
     * 查找低优先级文件
     */
    private fun findLowPriorityFiles(): List<File> {
        val lowPriorityFiles = mutableListOf<File>()
        
        listOf(logDir, archiveDir).forEach { dir ->
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    val logLevel = determineLogLevel(file)
                    val retentionPolicy = retentionPolicies[logLevel]
                    
                    if (retentionPolicy != null && retentionPolicy.priority <= PRIORITY_DEBUG_LOGS) {
                        lowPriorityFiles.add(file)
                    }
                }
            }
        }
        
        return lowPriorityFiles.sortedBy { it.lastModified() }
    }
    
    /**
     * 查找大文件
     */
    private fun findLargeFiles(): List<File> {
        val largeFiles = mutableListOf<File>()
        val sizeThreshold = 10 * 1024 * 1024L // 10MB
        
        listOf(logDir, archiveDir).forEach { dir ->
            dir.listFiles()?.forEach { file ->
                if (file.isFile && file.length() > sizeThreshold) {
                    largeFiles.add(file)
                }
            }
        }
        
        return largeFiles.sortedByDescending { it.length() }
    }
    
    /**
     * 查找非关键文件
     */
    private fun findNonCriticalFiles(): List<File> {
        val nonCriticalFiles = mutableListOf<File>()
        
        listOf(logDir, archiveDir).forEach { dir ->
            dir.listFiles()?.forEach { file ->
                if (file.isFile) {
                    val logLevel = determineLogLevel(file)
                    val retentionPolicy = retentionPolicies[logLevel]
                    
                    if (retentionPolicy != null && retentionPolicy.priority < PRIORITY_ERROR_LOGS) {
                        nonCriticalFiles.add(file)
                    }
                }
            }
        }
        
        return nonCriticalFiles.sortedBy { it.lastModified() }
    }
    
    /**
     * 查找压缩文件
     */
    private fun findCompressedFiles(): List<File> {
        val compressedFiles = mutableListOf<File>()
        
        listOf(logDir, archiveDir).forEach { dir ->
            dir.listFiles { file ->
                file.name.endsWith(".gz") || file.name.endsWith(".zip")
            }?.let { compressedFiles.addAll(it) }
        }
        
        return compressedFiles.sortedBy { it.lastModified() }
    }
    
    /**
     * 确定日志级别
     */
    private fun determineLogLevel(file: File): LogLevel {
        return when {
            file.name.contains("verbose", ignoreCase = true) -> LogLevel.VERBOSE
            file.name.contains("debug", ignoreCase = true) -> LogLevel.DEBUG
            file.name.contains("info", ignoreCase = true) -> LogLevel.INFO
            file.name.contains("warn", ignoreCase = true) -> LogLevel.WARN
            file.name.contains("error", ignoreCase = true) -> LogLevel.ERROR
            file.name.contains("fatal", ignoreCase = true) -> LogLevel.FATAL
            else -> LogLevel.INFO // 默认级别
        }
    }
    
    /**
     * 确定清理策略
     */
    private fun determineCleanupStrategy(
        cleanupType: CleanupType,
        isLowStorage: Boolean,
        isCriticalStorage: Boolean
    ): CleanupStrategy {
        return when {
            isCriticalStorage || cleanupType == CleanupType.EMERGENCY -> CleanupStrategy.EMERGENCY
            isLowStorage || cleanupType == CleanupType.LOW_STORAGE -> CleanupStrategy.AGGRESSIVE
            else -> CleanupStrategy.CONSERVATIVE
        }
    }
    
    /**
     * 检查存储空间
     */
    private fun checkStorageSpace(): StorageInfo {
        return try {
            val stat = android.os.StatFs(context.filesDir.path)
            val availableBytes = stat.availableBytes
            val totalBytes = stat.totalBytes
            val usedBytes = totalBytes - availableBytes
            
            StorageInfo(
                totalSpaceMB = totalBytes / 1024 / 1024,
                availableSpaceMB = availableBytes / 1024 / 1024,
                usedSpaceMB = usedBytes / 1024 / 1024,
                usagePercentage = (usedBytes.toDouble() / totalBytes * 100).toInt()
            )
        } catch (e: Exception) {
            Timber.e(e, "检查存储空间失败")
            StorageInfo(0, 0, 0, 0)
        }
    }
    
    /**
     * 清理空目录
     */
    private fun cleanupEmptyDirectories() {
        try {
            listOf(logDir, archiveDir, errorReportsDir).forEach { dir ->
                dir.walkBottomUp().forEach { file ->
                    if (file.isDirectory && file != dir && file.listFiles()?.isEmpty() == true) {
                        if (file.delete()) {
                            Timber.d("删除空目录: ${file.name}")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "清理空目录失败")
        }
    }
    
    /**
     * 启动清理监控
     */
    private fun startCleanupMonitor() {
        scope.launch {
            while (isActive) {
                try {
                    delay(CLEANUP_CHECK_INTERVAL_MS)
                    
                    // 检查是否需要自动清理
                    val storageInfo = checkStorageSpace()
                    val totalLogSize = calculateTotalLogSize()
                    
                    val needsCleanup = storageInfo.availableSpaceMB < LOW_STORAGE_THRESHOLD_MB ||
                                     totalLogSize > MAX_TOTAL_SIZE_MB * 1024 * 1024
                    
                    if (needsCleanup) {
                        val cleanupType = if (storageInfo.availableSpaceMB < CRITICAL_STORAGE_THRESHOLD_MB) {
                            CleanupType.EMERGENCY
                        } else {
                            CleanupType.LOW_STORAGE
                        }
                        
                        performCleanup(cleanupType)
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "清理监控任务失败")
                }
            }
        }
    }
    
    /**
     * 计算总日志大小
     */
    private fun calculateTotalLogSize(): Long {
        return try {
            listOf(logDir, archiveDir, errorReportsDir).sumOf { dir ->
                dir.walkTopDown()
                    .filter { it.isFile }
                    .map { it.length() }
                    .sum()
            }
        } catch (e: Exception) {
            0L
        }
    }
    
    /**
     * 更新清理状态
     */
    private fun updateCleanupState(result: CleanupResult) {
        val currentState = _cleanupState.value
        _cleanupState.value = currentState.copy(
            lastCleanupTime = System.currentTimeMillis(),
            lastCleanupType = result.cleanupType,
            lastCleanupStrategy = result.cleanupStrategy,
            totalFilesDeleted = currentState.totalFilesDeleted + result.filesDeleted,
            totalSizeFreed = currentState.totalSizeFreed + result.sizeFreed,
            totalCleanups = currentState.totalCleanups + 1
        )
    }
    
    /**
     * 获取清理统计信息
     */
    suspend fun getCleanupStatistics(): CleanupStatistics = withContext(Dispatchers.IO) {
        val storageInfo = checkStorageSpace()
        val totalLogSize = calculateTotalLogSize()
        val currentState = _cleanupState.value
        
        CleanupStatistics(
            storageInfo = storageInfo,
            totalLogSizeMB = totalLogSize / 1024 / 1024,
            totalFilesDeleted = currentState.totalFilesDeleted,
            totalSizeFreedMB = currentState.totalSizeFreed / 1024 / 1024,
            totalCleanups = currentState.totalCleanups,
            lastCleanupTime = currentState.lastCleanupTime
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        retentionPolicies.clear()
        Timber.d("日志清理管理器已清理")
    }
}

// 数据类和枚举定义
enum class CleanupType {
    SCHEDULED,      // 定期清理
    MANUAL,         // 手动清理
    LOW_STORAGE,    // 低存储空间清理
    EMERGENCY       // 紧急清理
}

enum class CleanupStrategy {
    CONSERVATIVE,   // 保守策略
    AGGRESSIVE,     // 激进策略
    EMERGENCY       // 紧急策略
}

data class RetentionPolicy(
    val retentionDays: Int,
    val priority: Int
)

data class CleanupResult(
    val success: Boolean,
    val filesDeleted: Int,
    val sizeFreed: Long,
    val duration: Long,
    val deletedFiles: List<String>,
    val cleanupType: CleanupType,
    val cleanupStrategy: CleanupStrategy,
    val error: String? = null
)

data class CleanupState(
    val lastCleanupTime: Long = 0,
    val lastCleanupType: CleanupType? = null,
    val lastCleanupStrategy: CleanupStrategy? = null,
    val totalFilesDeleted: Long = 0,
    val totalSizeFreed: Long = 0,
    val totalCleanups: Long = 0
)

data class StorageInfo(
    val totalSpaceMB: Long,
    val availableSpaceMB: Long,
    val usedSpaceMB: Long,
    val usagePercentage: Int
)

data class CleanupStatistics(
    val storageInfo: StorageInfo,
    val totalLogSizeMB: Long,
    val totalFilesDeleted: Long,
    val totalSizeFreedMB: Long,
    val totalCleanups: Long,
    val lastCleanupTime: Long
)
