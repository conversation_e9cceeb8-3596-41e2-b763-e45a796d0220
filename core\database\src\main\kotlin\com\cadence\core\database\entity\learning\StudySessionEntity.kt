package com.cadence.core.database.entity.learning

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 学习会话数据库实体
 */
@Entity(
    tableName = "study_sessions",
    indices = [
        Index(value = ["user_id"]),
        Index(value = ["session_type"]),
        Index(value = ["start_time"]),
        Index(value = ["created_at"])
    ]
)
data class StudySessionEntity(
    @PrimaryKey
    val id: String,
    
    @ColumnInfo(name = "user_id")
    val userId: String,
    
    @ColumnInfo(name = "session_type")
    val sessionType: String,
    
    @ColumnInfo(name = "start_time")
    val startTime: Long,
    
    @ColumnInfo(name = "end_time")
    val endTime: Long? = null,
    
    @ColumnInfo(name = "words_studied")
    val wordsStudied: String, // JSON字符串存储单词ID列表
    
    @ColumnInfo(name = "correct_answers")
    val correctAnswers: Int = 0,
    
    @ColumnInfo(name = "total_questions")
    val totalQuestions: Int = 0,
    
    @ColumnInfo(name = "time_spent")
    val timeSpent: Long = 0,
    
    @ColumnInfo(name = "created_at")
    val createdAt: Long
)