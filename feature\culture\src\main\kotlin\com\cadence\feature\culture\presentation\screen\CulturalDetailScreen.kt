package com.cadence.feature.culture.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.domain.model.culture.CulturalContext
import com.cadence.feature.culture.presentation.component.*
import com.cadence.feature.culture.presentation.viewmodel.CulturalDetailViewModel

/**
 * 文化背景详情界面
 * 显示单个文化背景的详细信息
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CulturalDetailScreen(
    contextId: String,
    onNavigateBack: () -> Unit,
    onNavigateToRelated: (String) -> Unit,
    onNavigateToExamples: (String) -> Unit,
    viewModel: CulturalDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    // 初始化加载数据
    LaunchedEffect(contextId) {
        viewModel.loadCulturalContext(contextId)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        text = uiState.culturalContext?.word ?: "文化详情",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    uiState.culturalContext?.let { context ->
                        IconButton(
                            onClick = { viewModel.toggleBookmark() }
                        ) {
                            Icon(
                                imageVector = if (uiState.isBookmarked) {
                                    Icons.Default.Bookmark
                                } else {
                                    Icons.Default.BookmarkBorder
                                },
                                contentDescription = if (uiState.isBookmarked) "取消收藏" else "收藏",
                                tint = if (uiState.isBookmarked) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.onSurfaceVariant
                                }
                            )
                        }
                        
                        IconButton(
                            onClick = { viewModel.shareContext() }
                        ) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "分享"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                uiState.isLoading -> {
                    LoadingIndicator(
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.error != null -> {
                    ErrorMessage(
                        error = uiState.error,
                        onRetry = { viewModel.loadCulturalContext(contextId) },
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
                
                uiState.culturalContext != null -> {
                    CulturalDetailContent(
                        context = uiState.culturalContext,
                        isLearned = uiState.isLearned,
                        learningNotes = uiState.learningNotes,
                        onMarkAsLearned = { viewModel.markAsLearned() },
                        onUpdateNotes = { notes -> viewModel.updateNotes(notes) },
                        onNavigateToRelated = onNavigateToRelated,
                        onNavigateToExamples = onNavigateToExamples,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

/**
 * 文化详情内容区域
 */
@Composable
private fun CulturalDetailContent(
    context: CulturalContext,
    isLearned: Boolean,
    learningNotes: String,
    onMarkAsLearned: () -> Unit,
    onUpdateNotes: (String) -> Unit,
    onNavigateToRelated: (String) -> Unit,
    onNavigateToExamples: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 基本信息卡片
        item {
            CulturalBasicInfoCard(
                context = context,
                isLearned = isLearned,
                onMarkAsLearned = onMarkAsLearned
            )
        }
        
        // 文化含义卡片
        item {
            CulturalMeaningCard(
                culturalMeaning = context.culturalMeaning,
                historicalBackground = context.historicalBackground
            )
        }
        
        // 使用场景卡片
        if (context.usageContext.isNotEmpty()) {
            item {
                UsageContextCard(
                    usageContexts = context.usageContext
                )
            }
        }
        
        // 地域差异卡片
        if (context.regionalDifferences.isNotEmpty()) {
            item {
                RegionalDifferencesCard(
                    regionalDifferences = context.regionalDifferences
                )
            }
        }
        
        // 文化示例卡片
        if (context.examples.isNotEmpty()) {
            item {
                CulturalExamplesCard(
                    examples = context.examples,
                    onViewAllExamples = { onNavigateToExamples(context.id) }
                )
            }
        }
        
        // 学习笔记卡片
        item {
            LearningNotesCard(
                notes = learningNotes,
                onNotesChange = onUpdateNotes
            )
        }
        
        // 相关推荐卡片
        item {
            RelatedRecommendationsCard(
                contextId = context.id,
                onViewRelated = { onNavigateToRelated(context.id) }
            )
        }
        
        // 底部间距
        item {
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 加载指示器
 */
@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator()
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "正在加载详细信息...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 错误消息
 */
@Composable
private fun ErrorMessage(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.Error,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier.size(48.dp)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}