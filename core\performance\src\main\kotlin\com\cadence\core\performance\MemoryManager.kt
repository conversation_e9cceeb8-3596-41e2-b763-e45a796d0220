package com.cadence.core.performance

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import kotlinx.coroutines.*
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内存管理器
 * 负责监控和优化应用内存使用
 */
@Singleton
class MemoryManager @Inject constructor(
    private val context: Context
) {
    
    private val scope = CoroutineScope(Dispatchers.Default + SupervisorJob())
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    
    // 内存监控相关
    private var isMonitoring = false
    private var monitoringJob: Job? = null
    
    // 缓存管理
    private val cacheRegistry = ConcurrentHashMap<String, WeakReference<Any>>()
    private val cacheMetrics = ConcurrentHashMap<String, CacheMetrics>()
    
    // 内存阈值配置
    private val memoryThresholds = MemoryThresholds(
        lowMemoryThreshold = 0.8f,      // 80%内存使用率
        criticalMemoryThreshold = 0.9f,  // 90%内存使用率
        cacheCleanupThreshold = 0.85f    // 85%时清理缓存
    )
    
    /**
     * 开始内存监控
     */
    fun startMemoryMonitoring(intervalMs: Long = 30000) {
        if (isMonitoring) {
            Timber.d("内存监控已在运行")
            return
        }
        
        isMonitoring = true
        monitoringJob = scope.launch {
            while (isActive) {
                try {
                    val memoryInfo = getCurrentMemoryInfo()
                    analyzeMemoryUsage(memoryInfo)
                    
                    // 检查是否需要内存清理
                    if (memoryInfo.memoryUsageRatio > memoryThresholds.cacheCleanupThreshold) {
                        performMemoryCleanup()
                    }
                    
                    delay(intervalMs)
                } catch (e: Exception) {
                    Timber.e(e, "内存监控过程中发生错误")
                }
            }
        }
        
        Timber.d("内存监控已启动，监控间隔: ${intervalMs}ms")
    }
    
    /**
     * 停止内存监控
     */
    fun stopMemoryMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        monitoringJob = null
        Timber.d("内存监控已停止")
    }
    
    /**
     * 获取当前内存信息
     */
    fun getCurrentMemoryInfo(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory()
        
        return MemoryInfo(
            totalMemory = totalMemory,
            freeMemory = freeMemory,
            usedMemory = usedMemory,
            maxMemory = maxMemory,
            memoryUsageRatio = usedMemory.toFloat() / maxMemory.toFloat(),
            availableMemory = memoryInfo.availMem,
            isLowMemory = memoryInfo.lowMemory,
            threshold = memoryInfo.threshold
        )
    }
    
    /**
     * 注册缓存对象
     */
    fun registerCache(key: String, cacheObject: Any, sizeBytes: Long = 0) {
        cacheRegistry[key] = WeakReference(cacheObject)
        cacheMetrics[key] = CacheMetrics(
            key = key,
            sizeBytes = sizeBytes,
            createdAt = System.currentTimeMillis(),
            lastAccessedAt = System.currentTimeMillis(),
            accessCount = 0
        )
        
        Timber.d("注册缓存对象: $key, 大小: ${sizeBytes}字节")
    }
    
    /**
     * 访问缓存对象
     */
    fun accessCache(key: String): Any? {
        val cacheRef = cacheRegistry[key]
        val cacheObject = cacheRef?.get()
        
        if (cacheObject != null) {
            // 更新访问统计
            cacheMetrics[key]?.let { metrics ->
                cacheMetrics[key] = metrics.copy(
                    lastAccessedAt = System.currentTimeMillis(),
                    accessCount = metrics.accessCount + 1
                )
            }
        } else {
            // 缓存对象已被回收，清理注册信息
            cacheRegistry.remove(key)
            cacheMetrics.remove(key)
        }
        
        return cacheObject
    }
    
    /**
     * 移除缓存对象
     */
    fun removeCache(key: String) {
        cacheRegistry.remove(key)
        cacheMetrics.remove(key)
        Timber.d("移除缓存对象: $key")
    }
    
    /**
     * 执行内存清理
     */
    suspend fun performMemoryCleanup(): MemoryCleanupResult {
        return withContext(Dispatchers.Default) {
            val startTime = System.currentTimeMillis()
            val beforeMemory = getCurrentMemoryInfo()
            
            Timber.d("开始内存清理，当前内存使用率: ${(beforeMemory.memoryUsageRatio * 100).toInt()}%")
            
            var cleanedCaches = 0
            var freedBytes = 0L
            
            // 清理过期缓存
            val currentTime = System.currentTimeMillis()
            val expiredCaches = cacheMetrics.values.filter { metrics ->
                val ageMs = currentTime - metrics.lastAccessedAt
                ageMs > 300000 // 5分钟未访问的缓存
            }
            
            expiredCaches.forEach { metrics ->
                cacheRegistry.remove(metrics.key)
                cacheMetrics.remove(metrics.key)
                freedBytes += metrics.sizeBytes
                cleanedCaches++
            }
            
            // 清理弱引用已失效的缓存
            val invalidCaches = cacheRegistry.entries.filter { (_, ref) -> ref.get() == null }
            invalidCaches.forEach { (key, _) ->
                cacheRegistry.remove(key)
                cacheMetrics.remove(key)?.let { metrics ->
                    freedBytes += metrics.sizeBytes
                    cleanedCaches++
                }
            }
            
            // 强制垃圾回收
            System.gc()
            
            val afterMemory = getCurrentMemoryInfo()
            val duration = System.currentTimeMillis() - startTime
            val memoryFreed = beforeMemory.usedMemory - afterMemory.usedMemory
            
            val result = MemoryCleanupResult(
                cleanedCaches = cleanedCaches,
                freedBytes = freedBytes,
                memoryFreedBytes = memoryFreed,
                durationMs = duration,
                beforeMemoryUsage = beforeMemory.memoryUsageRatio,
                afterMemoryUsage = afterMemory.memoryUsageRatio
            )
            
            Timber.i("内存清理完成: 清理${cleanedCaches}个缓存, 释放${memoryFreed}字节, 耗时${duration}ms")
            
            result
        }
    }
    
    /**
     * 获取内存使用报告
     */
    fun getMemoryReport(): MemoryReport {
        val memoryInfo = getCurrentMemoryInfo()
        val activeCaches = cacheRegistry.size
        val totalCacheSize = cacheMetrics.values.sumOf { it.sizeBytes }
        
        return MemoryReport(
            memoryInfo = memoryInfo,
            activeCaches = activeCaches,
            totalCacheSize = totalCacheSize,
            cacheMetrics = cacheMetrics.values.toList(),
            memoryPressureLevel = calculateMemoryPressureLevel(memoryInfo)
        )
    }
    
    /**
     * 分析内存使用情况
     */
    private fun analyzeMemoryUsage(memoryInfo: MemoryInfo) {
        val usagePercent = (memoryInfo.memoryUsageRatio * 100).toInt()
        
        when {
            memoryInfo.memoryUsageRatio > memoryThresholds.criticalMemoryThreshold -> {
                Timber.w("内存使用率过高: ${usagePercent}% (临界阈值: ${(memoryThresholds.criticalMemoryThreshold * 100).toInt()}%)")
            }
            memoryInfo.memoryUsageRatio > memoryThresholds.lowMemoryThreshold -> {
                Timber.w("内存使用率较高: ${usagePercent}% (警告阈值: ${(memoryThresholds.lowMemoryThreshold * 100).toInt()}%)")
            }
            else -> {
                Timber.d("内存使用正常: ${usagePercent}%")
            }
        }
        
        if (memoryInfo.isLowMemory) {
            Timber.w("系统内存不足，建议立即清理缓存")
        }
    }
    
    /**
     * 计算内存压力等级
     */
    private fun calculateMemoryPressureLevel(memoryInfo: MemoryInfo): MemoryPressureLevel {
        return when {
            memoryInfo.memoryUsageRatio > memoryThresholds.criticalMemoryThreshold -> MemoryPressureLevel.CRITICAL
            memoryInfo.memoryUsageRatio > memoryThresholds.lowMemoryThreshold -> MemoryPressureLevel.HIGH
            memoryInfo.memoryUsageRatio > 0.6f -> MemoryPressureLevel.MEDIUM
            else -> MemoryPressureLevel.LOW
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        stopMemoryMonitoring()
        cacheRegistry.clear()
        cacheMetrics.clear()
        scope.cancel()
        Timber.d("内存管理器已清理")
    }
}

/**
 * 内存信息数据类
 */
data class MemoryInfo(
    val totalMemory: Long,
    val freeMemory: Long,
    val usedMemory: Long,
    val maxMemory: Long,
    val memoryUsageRatio: Float,
    val availableMemory: Long,
    val isLowMemory: Boolean,
    val threshold: Long
)

/**
 * 缓存指标数据类
 */
data class CacheMetrics(
    val key: String,
    val sizeBytes: Long,
    val createdAt: Long,
    val lastAccessedAt: Long,
    val accessCount: Int
)

/**
 * 内存清理结果
 */
data class MemoryCleanupResult(
    val cleanedCaches: Int,
    val freedBytes: Long,
    val memoryFreedBytes: Long,
    val durationMs: Long,
    val beforeMemoryUsage: Float,
    val afterMemoryUsage: Float
)

/**
 * 内存报告
 */
data class MemoryReport(
    val memoryInfo: MemoryInfo,
    val activeCaches: Int,
    val totalCacheSize: Long,
    val cacheMetrics: List<CacheMetrics>,
    val memoryPressureLevel: MemoryPressureLevel
)

/**
 * 内存阈值配置
 */
data class MemoryThresholds(
    val lowMemoryThreshold: Float,
    val criticalMemoryThreshold: Float,
    val cacheCleanupThreshold: Float
)

/**
 * 内存压力等级
 */
enum class MemoryPressureLevel {
    LOW, MEDIUM, HIGH, CRITICAL
}
