package com.cadence.feature.translation.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cadence.domain.model.*
import com.cadence.domain.usecase.TranslateTextUseCase
import com.cadence.domain.usecase.DetectLanguageUseCase
import com.cadence.domain.usecase.ManageFavoritesUseCase
import com.cadence.domain.repository.LanguageRepository
import com.cadence.domain.repository.UserPreferenceRepository
import com.cadence.core.speech.SpeechRecognitionService
import com.cadence.core.speech.TextToSpeechService
import com.cadence.core.speech.SpeechPermissionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 翻译功能ViewModel
 * 管理翻译界面的状态和业务逻辑
 */
@HiltViewModel
class TranslationViewModel @Inject constructor(
    private val translateTextUseCase: TranslateTextUseCase,
    private val detectLanguageUseCase: DetectLanguageUseCase,
    private val manageFavoritesUseCase: ManageFavoritesUseCase,
    private val languageRepository: LanguageRepository,
    private val userPreferenceRepository: UserPreferenceRepository,
    private val speechRecognitionService: SpeechRecognitionService,
    private val textToSpeechService: TextToSpeechService,
    private val speechPermissionManager: SpeechPermissionManager
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(TranslationUiState())
    val uiState: StateFlow<TranslationUiState> = _uiState.asStateFlow()
    
    // 支持的语言列表
    val supportedLanguages: StateFlow<List<Language>> = languageRepository
        .getSupportedLanguages()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // 用户偏好设置
    val userPreference: StateFlow<UserPreference?> = userPreferenceRepository
        .getUserPreference("default_user") // TODO: 使用实际用户ID
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    init {
        // 监听用户偏好变化，更新UI状态
        viewModelScope.launch {
            userPreference.collect { preference ->
                preference?.let {
                    _uiState.update { currentState ->
                        currentState.copy(
                            sourceLanguage = it.defaultSourceLanguage,
                            targetLanguage = it.defaultTargetLanguage,
                            autoDetectLanguage = it.autoDetectLanguage,
                            enableCulturalContext = it.enableCulturalContext
                        )
                    }
                }
            }
        }
    }
    
    /**
     * 更新源文本
     */
    fun updateSourceText(text: String) {
        _uiState.update { it.copy(sourceText = text) }
        
        // 如果启用自动检测且文本不为空，则检测语言
        if (text.isNotBlank() && _uiState.value.autoDetectLanguage) {
            detectLanguage(text)
        }
    }
    
    /**
     * 更新源语言
     */
    fun updateSourceLanguage(language: Language) {
        _uiState.update { it.copy(sourceLanguage = language) }
    }
    
    /**
     * 更新目标语言
     */
    fun updateTargetLanguage(language: Language) {
        _uiState.update { it.copy(targetLanguage = language) }
    }
    
    /**
     * 交换源语言和目标语言
     */
    fun swapLanguages() {
        val currentState = _uiState.value
        _uiState.update {
            it.copy(
                sourceLanguage = currentState.targetLanguage,
                targetLanguage = currentState.sourceLanguage,
                sourceText = currentState.translatedText,
                translatedText = currentState.sourceText
            )
        }
    }
    
    /**
     * 执行翻译
     */
    fun translate() {
        val currentState = _uiState.value
        
        if (currentState.sourceText.isBlank()) {
            _uiState.update { 
                it.copy(error = "请输入要翻译的文本") 
            }
            return
        }
        
        viewModelScope.launch {
            _uiState.update { 
                it.copy(
                    isLoading = true, 
                    error = null,
                    translatedText = ""
                ) 
            }
            
            try {
                val result = translateTextUseCase(
                    sourceText = currentState.sourceText,
                    sourceLanguage = currentState.sourceLanguage,
                    targetLanguage = currentState.targetLanguage,
                    includeCulturalContext = currentState.enableCulturalContext
                )
                
                if (result.isSuccess) {
                    val translationResult = result.getOrThrow()
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            translatedText = translationResult.translation.translatedText,
                            currentTranslation = translationResult.translation,
                            isFromCache = translationResult.isFromCache,
                            processingTime = translationResult.processingTimeMs,
                            culturalContext = translationResult.translation.culturalContext
                        )
                    }
                    
                    Timber.d("翻译成功: ${translationResult.translation.translatedText}")
                } else {
                    val error = result.exceptionOrNull()?.message ?: "翻译失败"
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            error = error
                        )
                    }
                    Timber.e("翻译失败: $error")
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "翻译过程中发生未知错误"
                    )
                }
                Timber.e(e, "翻译过程中发生异常")
            }
        }
    }
    
    /**
     * 检测语言
     */
    fun detectLanguage(text: String = _uiState.value.sourceText) {
        if (text.isBlank()) return
        
        viewModelScope.launch {
            _uiState.update { it.copy(isDetectingLanguage = true) }
            
            try {
                val result = detectLanguageUseCase(text)
                
                if (result.isSuccess) {
                    val detection = result.getOrThrow()
                    _uiState.update {
                        it.copy(
                            isDetectingLanguage = false,
                            sourceLanguage = detection.detectedLanguage,
                            detectedLanguageConfidence = detection.confidence
                        )
                    }
                    Timber.d("语言检测成功: ${detection.detectedLanguage.name}")
                } else {
                    _uiState.update { it.copy(isDetectingLanguage = false) }
                    Timber.w("语言检测失败: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                _uiState.update { it.copy(isDetectingLanguage = false) }
                Timber.e(e, "语言检测过程中发生异常")
            }
        }
    }
    
    /**
     * 切换自动检测语言
     */
    fun toggleAutoDetectLanguage() {
        val newValue = !_uiState.value.autoDetectLanguage
        _uiState.update { it.copy(autoDetectLanguage = newValue) }
        
        // 更新用户偏好
        viewModelScope.launch {
            userPreferenceRepository.updateAutoDetectLanguage(newValue, "default_user")
        }
    }
    
    /**
     * 切换文化背景解释
     */
    fun toggleCulturalContext() {
        val newValue = !_uiState.value.enableCulturalContext
        _uiState.update { it.copy(enableCulturalContext = newValue) }
        
        // 更新用户偏好
        viewModelScope.launch {
            userPreferenceRepository.updateEnableCulturalContext(newValue, "default_user")
        }
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    /**
     * 清空翻译内容
     */
    fun clearTranslation() {
        _uiState.update {
            it.copy(
                sourceText = "",
                translatedText = "",
                currentTranslation = null,
                culturalContext = null,
                error = null
            )
        }
    }
    
    /**
     * 复制翻译结果
     */
    fun copyTranslation() {
        // TODO: 实现复制到剪贴板功能
        Timber.d("复制翻译结果: ${_uiState.value.translatedText}")
    }
    
    /**
     * 分享翻译结果
     */
    fun shareTranslation() {
        // TODO: 实现分享功能
        val currentState = _uiState.value
        val shareText = "原文: ${currentState.sourceText}\n译文: ${currentState.translatedText}"
        Timber.d("分享翻译结果: $shareText")
    }

    /**
     * 切换收藏状态
     */
    fun toggleFavorite() {
        val currentTranslation = _uiState.value.currentTranslation ?: return

        viewModelScope.launch {
            try {
                if (currentTranslation.isFavorite) {
                    // 移除收藏
                    val result = manageFavoritesUseCase.removeFavoriteByTranslationId(currentTranslation.id)
                    result.fold(
                        onSuccess = {
                            _uiState.update { state ->
                                state.copy(
                                    currentTranslation = currentTranslation.copy(isFavorite = false)
                                )
                            }
                            Timber.d("移除收藏成功: ${currentTranslation.id}")
                        },
                        onFailure = { error ->
                            Timber.e(error, "移除收藏失败")
                            _uiState.update { it.copy(error = "移除收藏失败: ${error.message}") }
                        }
                    )
                } else {
                    // 添加收藏
                    val result = manageFavoritesUseCase.addTranslationToFavorites(
                        translationId = currentTranslation.id,
                        folderId = null, // 使用默认收藏夹
                        note = null,
                        tags = emptyList(),
                        priority = com.cadence.domain.model.FavoritePriority.NORMAL
                    )
                    result.fold(
                        onSuccess = { favoriteId ->
                            _uiState.update { state ->
                                state.copy(
                                    currentTranslation = currentTranslation.copy(isFavorite = true)
                                )
                            }
                            Timber.d("添加收藏成功: $favoriteId")
                        },
                        onFailure = { error ->
                            Timber.e(error, "添加收藏失败")
                            _uiState.update { it.copy(error = "添加收藏失败: ${error.message}") }
                        }
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "收藏操作异常")
                _uiState.update { it.copy(error = "收藏操作异常: ${e.message}") }
            }
        }
    }
}

/**
 * 翻译界面UI状态
 */
data class TranslationUiState(
    val sourceText: String = "",
    val translatedText: String = "",
    val sourceLanguage: Language = Language("zh", "中文"),
    val targetLanguage: Language = Language("en", "English"),
    val isLoading: Boolean = false,
    val isDetectingLanguage: Boolean = false,
    val autoDetectLanguage: Boolean = true,
    val enableCulturalContext: Boolean = true,
    val error: String? = null,
    val currentTranslation: Translation? = null,
    val isFromCache: Boolean = false,
    val processingTime: Long = 0L,
    val culturalContext: String? = null,
    val detectedLanguageConfidence: Float? = null,
    // 语音相关状态
    val isListening: Boolean = false,
    val speechResult: SpeechRecognitionService.SpeechResult? = null,
    val isPlayingSource: Boolean = false,
    val isPlayingTarget: Boolean = false,
    val ttsResult: TextToSpeechService.TtsResult? = null,
    val hasSpeechPermission: Boolean = false,
    val isSpeechAvailable: Boolean = false,
    val isTtsAvailable: Boolean = false
)

    // 语音功能初始化
    init {
        initializeSpeechServices()
    }

    /**
     * 初始化语音服务
     */
    private fun initializeSpeechServices() {
        viewModelScope.launch {
            // 检查语音识别可用性
            val speechAvailable = speechRecognitionService.isAvailable()

            // 初始化TTS并检查可用性
            val ttsAvailable = textToSpeechService.initialize()

            // 检查权限
            val hasPermission = speechPermissionManager.hasAllSpeechPermissions()

            _uiState.value = _uiState.value.copy(
                isSpeechAvailable = speechAvailable,
                isTtsAvailable = ttsAvailable,
                hasSpeechPermission = hasPermission
            )
        }
    }

    /**
     * 开始语音识别
     */
    fun startSpeechRecognition() {
        if (!_uiState.value.hasSpeechPermission) {
            // 权限不足，需要请求权限
            return
        }

        if (!_uiState.value.isSpeechAvailable) {
            _uiState.value = _uiState.value.copy(
                error = "语音识别功能不可用"
            )
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isListening = true,
                speechResult = null,
                error = null
            )

            val config = SpeechRecognitionService.SpeechConfig(
                language = speechRecognitionService.mapTranslationLanguageToSpeech(
                    _uiState.value.sourceLanguage.code
                ),
                partialResults = true
            )

            speechRecognitionService.startRecognition(config).collect { result ->
                _uiState.value = _uiState.value.copy(speechResult = result)

                when (result) {
                    is SpeechRecognitionService.SpeechResult.Success -> {
                        // 语音识别成功，更新输入文本
                        updateSourceText(result.text)
                        _uiState.value = _uiState.value.copy(isListening = false)

                        // 自动开始翻译
                        if (result.text.isNotBlank()) {
                            translateText()
                        }
                    }
                    is SpeechRecognitionService.SpeechResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isListening = false,
                            error = "语音识别失败: ${result.message}"
                        )
                    }
                    else -> {
                        // 其他状态（Ready, Listening, PartialResult）保持监听状态
                    }
                }
            }
        }
    }

    /**
     * 停止语音识别
     */
    fun stopSpeechRecognition() {
        _uiState.value = _uiState.value.copy(
            isListening = false,
            speechResult = null
        )
    }

    /**
     * 播放源文本语音
     */
    fun playSourceText() {
        val sourceText = _uiState.value.sourceText
        if (sourceText.isBlank()) return

        val language = textToSpeechService.mapTranslationLanguageToTts(
            _uiState.value.sourceLanguage.code
        )

        playText(sourceText, language, isSource = true)
    }

    /**
     * 播放目标文本语音
     */
    fun playTargetText() {
        val targetText = _uiState.value.translatedText
        if (targetText.isBlank()) return

        val language = textToSpeechService.mapTranslationLanguageToTts(
            _uiState.value.targetLanguage.code
        )

        playText(targetText, language, isSource = false)
    }

    /**
     * 播放文本语音
     */
    private fun playText(text: String, language: String, isSource: Boolean) {
        if (!_uiState.value.isTtsAvailable) {
            _uiState.value = _uiState.value.copy(
                error = "语音播放功能不可用"
            )
            return
        }

        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(
                isPlayingSource = isSource,
                isPlayingTarget = !isSource,
                ttsResult = null,
                error = null
            )

            val config = textToSpeechService.getRecommendedConfig(language)

            textToSpeechService.speak(text, config).collect { result ->
                _uiState.value = _uiState.value.copy(ttsResult = result)

                when (result) {
                    is TextToSpeechService.TtsResult.Completed,
                    is TextToSpeechService.TtsResult.Stopped -> {
                        _uiState.value = _uiState.value.copy(
                            isPlayingSource = false,
                            isPlayingTarget = false
                        )
                    }
                    is TextToSpeechService.TtsResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isPlayingSource = false,
                            isPlayingTarget = false,
                            error = "语音播放失败: ${result.message}"
                        )
                    }
                    else -> {
                        // 其他状态保持播放状态
                    }
                }
            }
        }
    }

    /**
     * 停止语音播放
     */
    fun stopSpeechPlayback() {
        textToSpeechService.stop()
        _uiState.value = _uiState.value.copy(
            isPlayingSource = false,
            isPlayingTarget = false,
            ttsResult = null
        )
    }

    /**
     * 更新语音权限状态
     */
    fun updateSpeechPermissionStatus(hasPermission: Boolean) {
        _uiState.value = _uiState.value.copy(
            hasSpeechPermission = hasPermission
        )
    }

    /**
     * 清除语音错误
     */
    fun clearSpeechError() {
        _uiState.value = _uiState.value.copy(
            speechResult = null,
            ttsResult = null,
            error = null
        )
    }

    override fun onCleared() {
        super.onCleared()
        // 清理语音服务资源
        textToSpeechService.shutdown()
    }
