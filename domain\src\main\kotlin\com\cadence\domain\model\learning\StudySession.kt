package com.cadence.domain.model.learning

/**
 * 学习会话领域模型
 * 记录用户的学习会话信息
 */
data class StudySession(
    val id: String,
    val userId: String,
    val sessionType: SessionType,
    val startTime: Long,
    val endTime: Long? = null,
    val wordsStudied: List<String> = emptyList(), // Word IDs
    val correctAnswers: Int = 0,
    val totalQuestions: Int = 0,
    val timeSpent: Long = 0, // 毫秒
    val createdAt: Long
) {
    /**
     * 会话是否已完成
     */
    val isCompleted: Boolean
        get() = endTime != null
    
    /**
     * 计算正确率
     */
    val accuracyRate: Float
        get() = if (totalQuestions > 0) correctAnswers.toFloat() / totalQuestions else 0f
    
    /**
     * 计算会话时长（分钟）
     */
    val durationMinutes: Int
        get() = (timeSpent / (1000 * 60)).toInt()
}

/**
 * 学习会话类型
 */
enum class SessionType(val displayName: String) {
    NEW_WORDS("新单词学习"),
    REVIEW("复习"),
    PRACTICE("练习"),
    QUIZ("测验"),
    QUICK_REVIEW("快速复习")
}