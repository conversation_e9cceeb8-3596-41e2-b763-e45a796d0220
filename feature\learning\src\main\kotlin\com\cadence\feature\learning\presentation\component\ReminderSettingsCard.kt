package com.cadence.feature.learning.presentation.component

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.cadence.feature.learning.presentation.viewmodel.ReminderSettings

/**
 * 提醒设置卡片组件
 * 允许用户配置复习提醒设置
 */
@Composable
fun ReminderSettingsCard(
    settings: ReminderSettings,
    onSettingsChange: (ReminderSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Notifications,
                        contentDescription = null,
                        modifier = Modifier.size(24.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = "复习提醒设置",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                IconButton(
                    onClick = { expanded = !expanded }
                ) {
                    Icon(
                        imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                        contentDescription = if (expanded) "收起" else "展开"
                    )
                }
            }
            
            // 主开关
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "启用复习提醒",
                    style = MaterialTheme.typography.bodyLarge
                )
                
                Switch(
                    checked = settings.enabled,
                    onCheckedChange = { enabled ->
                        onSettingsChange(settings.copy(enabled = enabled))
                    }
                )
            }
            
            // 详细设置（展开时显示）
            if (expanded && settings.enabled) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Divider()
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 提醒时间设置
                ReminderTimeSection(
                    time = settings.dailyReminderTime,
                    onTimeChange = { time ->
                        onSettingsChange(settings.copy(dailyReminderTime = time))
                    }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 提醒日期设置
                ReminderDaysSection(
                    selectedDays = settings.reminderDays,
                    onDaysChange = { days ->
                        onSettingsChange(settings.copy(reminderDays = days))
                    }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // 通知选项
                NotificationOptionsSection(
                    soundEnabled = settings.soundEnabled,
                    vibrationEnabled = settings.vibrationEnabled,
                    onSoundChange = { enabled ->
                        onSettingsChange(settings.copy(soundEnabled = enabled))
                    },
                    onVibrationChange = { enabled ->
                        onSettingsChange(settings.copy(vibrationEnabled = enabled))
                    }
                )
            }
        }
    }
}

/**
 * 提醒时间设置部分
 */
@Composable
private fun ReminderTimeSection(
    time: String,
    onTimeChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "每日提醒时间",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 时间选择器（简化实现）
        val timeOptions = listOf("08:00", "09:00", "10:00", "18:00", "19:00", "20:00")
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            timeOptions.forEach { timeOption ->
                FilterChip(
                    onClick = { onTimeChange(timeOption) },
                    label = { Text(timeOption) },
                    selected = time == timeOption,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 提醒日期设置部分
 */
@Composable
private fun ReminderDaysSection(
    selectedDays: Set<Int>,
    onDaysChange: (Set<Int>) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "提醒日期",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 星期选择器
        val dayNames = listOf("一", "二", "三", "四", "五", "六", "日")
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            dayNames.forEachIndexed { index, dayName ->
                val dayNumber = index + 1
                val isSelected = selectedDays.contains(dayNumber)
                
                FilterChip(
                    onClick = {
                        val newDays = if (isSelected) {
                            selectedDays - dayNumber
                        } else {
                            selectedDays + dayNumber
                        }
                        onDaysChange(newDays)
                    },
                    label = { Text(dayName) },
                    selected = isSelected
                )
            }
        }
    }
}

/**
 * 通知选项设置部分
 */
@Composable
private fun NotificationOptionsSection(
    soundEnabled: Boolean,
    vibrationEnabled: Boolean,
    onSoundChange: (Boolean) -> Unit,
    onVibrationChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "通知选项",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 声音开关
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .selectable(
                    selected = soundEnabled,
                    onClick = { onSoundChange(!soundEnabled) },
                    role = Role.Switch
                )
                .padding(vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (soundEnabled) Icons.Default.VolumeUp else Icons.Default.VolumeOff,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "提醒声音",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Switch(
                checked = soundEnabled,
                onCheckedChange = onSoundChange
            )
        }
        
        // 震动开关
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .selectable(
                    selected = vibrationEnabled,
                    onClick = { onVibrationChange(!vibrationEnabled) },
                    role = Role.Switch
                )
                .padding(vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (vibrationEnabled) Icons.Default.Vibration else Icons.Default.PhoneAndroid,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "震动提醒",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Switch(
                checked = vibrationEnabled,
                onCheckedChange = onVibrationChange
            )
        }
    }
}