package com.cadence.domain.model.learning

/**
 * 每日学习进度
 * 记录用户每天的学习情况
 */
data class DailyProgress(
    val date: Long, // 日期时间戳
    val wordsStudied: Int, // 当天学习的单词数
    val correctAnswers: Int, // 正确答案数
    val totalAnswers: Int, // 总答题数
    val studyTime: Long, // 学习时长（毫秒）
    val newWordsLearned: Int, // 新学单词数
    val reviewsCompleted: Int // 完成的复习数
) {
    /**
     * 计算当天的正确率
     */
    val accuracy: Float
        get() = if (totalAnswers > 0) (correctAnswers.toFloat() / totalAnswers) * 100 else 0f
}