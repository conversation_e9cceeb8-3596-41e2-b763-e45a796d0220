package com.cadence.core.translation

import com.cadence.domain.model.Language
import com.cadence.domain.model.Region
import com.cadence.domain.model.TranslationRequest
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 区域翻译引擎
 * 处理不同地区的语言特色和文化差异
 */
@Singleton
class RegionalTranslationEngine @Inject constructor(
    private val dialectProcessor: DialectProcessor
) {
    
    /**
     * 增强翻译请求，添加区域特色处理
     */
    fun enhanceTranslationRequest(request: TranslationRequest): EnhancedTranslationRequest {
        val sourceRegionInfo = getRegionInfo(request.sourceLanguage, request.sourceRegion)
        val targetRegionInfo = getRegionInfo(request.targetLanguage, request.targetRegion)
        
        return EnhancedTranslationRequest(
            originalRequest = request,
            sourceRegionInfo = sourceRegionInfo,
            targetRegionInfo = targetRegionInfo,
            dialectMappings = dialectProcessor.getDialectMappings(
                request.sourceLanguage, 
                request.targetLanguage,
                request.sourceRegion,
                request.targetRegion
            ),
            culturalContext = getCulturalContext(sourceRegionInfo, targetRegionInfo),
            translationStrategy = determineTranslationStrategy(sourceRegionInfo, targetRegionInfo)
        )
    }
    
    /**
     * 构建区域化翻译提示词
     */
    fun buildRegionalPrompt(enhancedRequest: EnhancedTranslationRequest): String {
        val originalRequest = enhancedRequest.originalRequest
        val sourceInfo = enhancedRequest.sourceRegionInfo
        val targetInfo = enhancedRequest.targetRegionInfo
        
        return buildString {
            // 基础翻译指令
            append("请将以下${sourceInfo.languageDisplayName}")
            if (sourceInfo.regionDisplayName.isNotBlank()) {
                append("（${sourceInfo.regionDisplayName}地区）")
            }
            append("文本翻译为${targetInfo.languageDisplayName}")
            if (targetInfo.regionDisplayName.isNotBlank()) {
                append("（${targetInfo.regionDisplayName}地区）")
            }
            append("：\n\n")
            
            // 原文
            append("原文：${originalRequest.text}\n\n")
            
            // 翻译要求
            append("翻译要求：\n")
            append("1. 保持原文的语气和风格\n")
            
            // 区域特色要求
            if (targetInfo.hasRegionalFeatures) {
                append("2. 采用${targetInfo.regionDisplayName}地区的语言特色和表达习惯\n")
                if (enhancedRequest.dialectMappings.isNotEmpty()) {
                    append("3. 注意以下方言特色：\n")
                    enhancedRequest.dialectMappings.forEach { mapping ->
                        append("   - ${mapping.description}\n")
                    }
                }
            } else {
                append("2. 使用标准的${targetInfo.languageDisplayName}表达\n")
            }
            
            // 文化背景要求
            if (enhancedRequest.culturalContext.isNotEmpty()) {
                append("${if (targetInfo.hasRegionalFeatures) 4 else 3}. 文化背景考虑：\n")
                enhancedRequest.culturalContext.forEach { context ->
                    append("   - ${context}\n")
                }
            }
            
            // 特殊策略
            when (enhancedRequest.translationStrategy) {
                TranslationStrategy.FORMAL -> {
                    append("${getNextNumber(targetInfo.hasRegionalFeatures, enhancedRequest.culturalContext.isNotEmpty())}. 使用正式、礼貌的表达方式\n")
                }
                TranslationStrategy.CASUAL -> {
                    append("${getNextNumber(targetInfo.hasRegionalFeatures, enhancedRequest.culturalContext.isNotEmpty())}. 使用轻松、自然的表达方式\n")
                }
                TranslationStrategy.TECHNICAL -> {
                    append("${getNextNumber(targetInfo.hasRegionalFeatures, enhancedRequest.culturalContext.isNotEmpty())}. 保持技术术语的准确性\n")
                }
                TranslationStrategy.LITERARY -> {
                    append("${getNextNumber(targetInfo.hasRegionalFeatures, enhancedRequest.culturalContext.isNotEmpty())}. 注重文学性和艺术表达\n")
                }
                else -> { /* 标准策略，无需额外说明 */ }
            }
            
            append("\n请只返回翻译结果，不要包含其他解释。")
        }
    }
    
    /**
     * 获取区域信息
     */
    private fun getRegionInfo(language: Language, region: Region?): RegionInfo {
        val languageCode = language.code
        val languageDisplayName = language.name
        val regionCode = region?.code ?: ""
        val regionDisplayName = region?.name ?: ""
        
        // 判断是否有区域特色
        val hasRegionalFeatures = when (languageCode) {
            "zh" -> regionCode in listOf("CN", "TW", "HK", "SG") // 中文各地区
            "en" -> regionCode in listOf("US", "UK", "AU", "CA") // 英语各地区
            "es" -> regionCode in listOf("ES", "MX", "AR", "CO") // 西班牙语各地区
            "pt" -> regionCode in listOf("BR", "PT") // 葡萄牙语
            "fr" -> regionCode in listOf("FR", "CA", "BE", "CH") // 法语各地区
            "ar" -> regionCode in listOf("SA", "EG", "AE", "MA") // 阿拉伯语各地区
            else -> false
        }
        
        return RegionInfo(
            languageCode = languageCode,
            languageDisplayName = languageDisplayName,
            regionCode = regionCode,
            regionDisplayName = regionDisplayName,
            hasRegionalFeatures = hasRegionalFeatures
        )
    }
    
    /**
     * 获取文化背景信息
     */
    private fun getCulturalContext(sourceInfo: RegionInfo, targetInfo: RegionInfo): List<String> {
        val contexts = mutableListOf<String>()
        
        // 中文相关的文化背景
        if (sourceInfo.languageCode == "zh" || targetInfo.languageCode == "zh") {
            when {
                targetInfo.regionCode == "TW" -> contexts.add("注意繁体中文的用词习惯和台湾地区的表达方式")
                targetInfo.regionCode == "HK" -> contexts.add("考虑香港地区的粤语影响和特殊用词")
                targetInfo.regionCode == "SG" -> contexts.add("注意新加坡华语的简洁特色")
                sourceInfo.regionCode == "CN" && targetInfo.languageCode != "zh" -> {
                    contexts.add("考虑中国大陆的文化背景和表达习惯")
                }
            }
        }
        
        // 英语相关的文化背景
        if (sourceInfo.languageCode == "en" || targetInfo.languageCode == "en") {
            when {
                targetInfo.regionCode == "UK" -> contexts.add("使用英式英语的拼写和表达习惯")
                targetInfo.regionCode == "US" -> contexts.add("使用美式英语的拼写和表达习惯")
                targetInfo.regionCode == "AU" -> contexts.add("注意澳大利亚英语的特色表达")
                targetInfo.regionCode == "CA" -> contexts.add("考虑加拿大英语的特点")
            }
        }
        
        // 跨文化翻译的通用建议
        if (sourceInfo.languageCode != targetInfo.languageCode) {
            contexts.add("注意避免直译，确保表达自然流畅")
            if (isEastWestCulturalGap(sourceInfo.languageCode, targetInfo.languageCode)) {
                contexts.add("注意东西方文化差异，适当调整表达方式")
            }
        }
        
        return contexts
    }
    
    /**
     * 确定翻译策略
     */
    private fun determineTranslationStrategy(sourceInfo: RegionInfo, targetInfo: RegionInfo): TranslationStrategy {
        // 根据语言对和地区特点确定策略
        return when {
            // 正式语言对（如中文到日语、韩语）
            (sourceInfo.languageCode == "zh" && targetInfo.languageCode in listOf("ja", "ko")) ||
            (sourceInfo.languageCode in listOf("ja", "ko") && targetInfo.languageCode == "zh") -> {
                TranslationStrategy.FORMAL
            }
            
            // 英语到其他语言通常使用标准策略
            sourceInfo.languageCode == "en" || targetInfo.languageCode == "en" -> {
                TranslationStrategy.STANDARD
            }
            
            // 同语言族内翻译使用自然策略
            isSameLanguageFamily(sourceInfo.languageCode, targetInfo.languageCode) -> {
                TranslationStrategy.CASUAL
            }
            
            else -> TranslationStrategy.STANDARD
        }
    }
    
    /**
     * 判断是否为东西方文化差异较大的语言对
     */
    private fun isEastWestCulturalGap(sourceLang: String, targetLang: String): Boolean {
        val eastAsianLanguages = setOf("zh", "ja", "ko")
        val westernLanguages = setOf("en", "fr", "de", "es", "it", "pt", "ru")
        
        return (sourceLang in eastAsianLanguages && targetLang in westernLanguages) ||
               (sourceLang in westernLanguages && targetLang in eastAsianLanguages)
    }
    
    /**
     * 判断是否为同语言族
     */
    private fun isSameLanguageFamily(sourceLang: String, targetLang: String): Boolean {
        val romanticLanguages = setOf("es", "pt", "fr", "it", "ro")
        val germanicLanguages = setOf("en", "de", "nl", "sv", "no", "da")
        val slavicLanguages = setOf("ru", "pl", "cs", "sk", "bg", "hr", "sr")
        
        return (sourceLang in romanticLanguages && targetLang in romanticLanguages) ||
               (sourceLang in germanicLanguages && targetLang in germanicLanguages) ||
               (sourceLang in slavicLanguages && targetLang in slavicLanguages)
    }
    
    /**
     * 获取下一个编号
     */
    private fun getNextNumber(hasRegionalFeatures: Boolean, hasCulturalContext: Boolean): Int {
        var number = 3
        if (hasRegionalFeatures) number++
        if (hasCulturalContext) number++
        return number
    }
}

/**
 * 增强的翻译请求
 */
data class EnhancedTranslationRequest(
    val originalRequest: TranslationRequest,
    val sourceRegionInfo: RegionInfo,
    val targetRegionInfo: RegionInfo,
    val dialectMappings: List<DialectMapping>,
    val culturalContext: List<String>,
    val translationStrategy: TranslationStrategy
)

/**
 * 区域信息
 */
data class RegionInfo(
    val languageCode: String,
    val languageDisplayName: String,
    val regionCode: String,
    val regionDisplayName: String,
    val hasRegionalFeatures: Boolean
)

/**
 * 翻译策略
 */
enum class TranslationStrategy {
    STANDARD,   // 标准翻译
    FORMAL,     // 正式翻译
    CASUAL,     // 随意翻译
    TECHNICAL,  // 技术翻译
    LITERARY    // 文学翻译
}
