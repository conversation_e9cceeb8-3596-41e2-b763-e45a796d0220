package com.cadence.ui.favorites

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cadence.R
import com.cadence.domain.model.FavoriteFolder
import com.cadence.ui.components.ErrorMessage
import com.cadence.ui.components.LoadingIndicator
import com.cadence.ui.components.SearchBar

/**
 * 收藏夹主界面
 * 显示收藏夹列表和收藏项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FavoritesScreen(
    onNavigateToFavoriteDetail: (String) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: FavoritesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val folders by viewModel.folders.collectAsStateWithLifecycle()
    val selectedFolder by viewModel.selectedFolder.collectAsStateWithLifecycle()
    val favoriteItems by viewModel.favoriteItems.collectAsStateWithLifecycle()
    val searchQuery by viewModel.searchQuery.collectAsStateWithLifecycle()
    val statistics by viewModel.statistics.collectAsStateWithLifecycle()
    
    var showCreateFolderDialog by remember { mutableStateOf(false) }
    var showDeleteFolderDialog by remember { mutableStateOf<FavoriteFolder?>(null) }
    
    // 显示错误或成功消息
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // 这里可以显示Snackbar或其他提示
        }
    }
    
    LaunchedEffect(uiState.message) {
        uiState.message?.let {
            // 这里可以显示Snackbar或其他提示
        }
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部应用栏
        TopAppBar(
            title = { 
                Text(
                    text = stringResource(R.string.favorites_title),
                    fontWeight = FontWeight.Bold
                ) 
            },
            actions = {
                // 统计信息
                statistics?.let { stats ->
                    Text(
                        text = "${stats.totalItems}项",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                }
                
                // 创建收藏夹按钮
                IconButton(
                    onClick = { showCreateFolderDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.CreateNewFolder,
                        contentDescription = stringResource(R.string.create_folder)
                    )
                }
            }
        )
        
        // 搜索栏
        SearchBar(
            query = searchQuery,
            onQueryChange = viewModel::searchFavoriteItems,
            placeholder = stringResource(R.string.search_favorites),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp)
        )
        
        // 主要内容
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            // 左侧收藏夹列表
            FolderListPanel(
                folders = folders,
                selectedFolder = selectedFolder,
                onFolderSelected = viewModel::selectFolder,
                onDeleteFolder = { folder -> showDeleteFolderDialog = folder },
                isLoading = uiState.isLoading,
                modifier = Modifier
                    .weight(0.3f)
                    .fillMaxHeight()
            )
            
            // 分隔线
            VerticalDivider()
            
            // 右侧收藏项列表
            FavoriteItemsPanel(
                favoriteItems = favoriteItems,
                selectedFolder = selectedFolder,
                onItemClick = { item -> 
                    onNavigateToFavoriteDetail(item.first.id)
                },
                onRemoveItem = viewModel::removeFavoriteItem,
                onMoveItem = viewModel::moveFavoriteItem,
                folders = folders,
                isLoading = uiState.isLoadingItems,
                modifier = Modifier
                    .weight(0.7f)
                    .fillMaxHeight()
            )
        }
        
        // 错误信息
        uiState.error?.let { error ->
            ErrorMessage(
                message = error,
                onDismiss = viewModel::clearError,
                modifier = Modifier.padding(16.dp)
            )
        }
    }
    
    // 创建收藏夹对话框
    if (showCreateFolderDialog) {
        CreateFolderDialog(
            onDismiss = { showCreateFolderDialog = false },
            onConfirm = { name, description, color, icon ->
                viewModel.createFolder(name, description, color, icon)
                showCreateFolderDialog = false
            }
        )
    }
    
    // 删除收藏夹确认对话框
    showDeleteFolderDialog?.let { folder ->
        DeleteFolderDialog(
            folder = folder,
            folders = folders.filter { it.id != folder.id },
            onDismiss = { showDeleteFolderDialog = null },
            onConfirm = { moveToFolderId ->
                viewModel.deleteFolder(folder.id, moveToFolderId)
                showDeleteFolderDialog = null
            }
        )
    }
}

/**
 * 收藏夹列表面板
 */
@Composable
private fun FolderListPanel(
    folders: List<FavoriteFolder>,
    selectedFolder: FavoriteFolder?,
    onFolderSelected: (FavoriteFolder) -> Unit,
    onDeleteFolder: (FavoriteFolder) -> Unit,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.folders),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            if (isLoading) {
                LoadingIndicator(
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            } else {
                LazyColumn {
                    items(folders) { folder ->
                        FolderListItem(
                            folder = folder,
                            isSelected = folder.id == selectedFolder?.id,
                            onClick = { onFolderSelected(folder) },
                            onDelete = { onDeleteFolder(folder) },
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    }
}

/**
 * 收藏项列表面板
 */
@Composable
private fun FavoriteItemsPanel(
    favoriteItems: List<Pair<com.cadence.domain.model.FavoriteItem, com.cadence.domain.model.Translation?>>,
    selectedFolder: FavoriteFolder?,
    onItemClick: (Pair<com.cadence.domain.model.FavoriteItem, com.cadence.domain.model.Translation?>) -> Unit,
    onRemoveItem: (String) -> Unit,
    onMoveItem: (String, String) -> Unit,
    folders: List<FavoriteFolder>,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.padding(8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = selectedFolder?.name ?: stringResource(R.string.select_folder),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            if (isLoading) {
                LoadingIndicator(
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            } else if (favoriteItems.isEmpty()) {
                Text(
                    text = stringResource(R.string.no_favorite_items),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.align(Alignment.CenterHorizontally)
                )
            } else {
                LazyColumn {
                    items(favoriteItems) { item ->
                        FavoriteItemCard(
                            favoriteItem = item.first,
                            translation = item.second,
                            onClick = { onItemClick(item) },
                            onRemove = { onRemoveItem(item.first.id) },
                            onMove = { targetFolderId -> 
                                onMoveItem(item.first.id, targetFolderId) 
                            },
                            folders = folders.filter { it.id != selectedFolder?.id },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}
