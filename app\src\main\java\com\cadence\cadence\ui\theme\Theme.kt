package com.cadence.cadence.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

/**
 * Cadence应用程序主题定义
 * 
 * 支持：
 * - 浅色/深色主题
 * - 动态颜色（Android 12+）
 * - 地方特色颜色方案
 */

// 深色主题颜色方案
private val DarkColorScheme = darkColorScheme(
    primary = Primary80,
    onPrimary = Neutral10,
    primaryContainer = Primary40,
    onPrimaryContainer = Primary90,
    
    secondary = Secondary80,
    onSecondary = Neutral10,
    secondaryContainer = Secondary40,
    onSecondaryContainer = Secondary90,
    
    tertiary = Tertiary80,
    onTertiary = Neutral10,
    tertiaryContainer = Tertiary40,
    onTertiaryContainer = Tertiary90,
    
    error = Error80,
    onError = Neutral10,
    errorContainer = Error40,
    onErrorContainer = Error90,
    
    background = Neutral10,
    onBackground = Neutral90,
    surface = Neutral10,
    onSurface = Neutral90,
    
    surfaceVariant = NeutralVariant30,
    onSurfaceVariant = NeutralVariant80,
    outline = NeutralVariant50,
    outlineVariant = NeutralVariant30
)

// 浅色主题颜色方案
private val LightColorScheme = lightColorScheme(
    primary = Primary40,
    onPrimary = Neutral99,
    primaryContainer = Primary90,
    onPrimaryContainer = Primary40,
    
    secondary = Secondary40,
    onSecondary = Neutral99,
    secondaryContainer = Secondary90,
    onSecondaryContainer = Secondary40,
    
    tertiary = Tertiary40,
    onTertiary = Neutral99,
    tertiaryContainer = Tertiary90,
    onTertiaryContainer = Tertiary40,
    
    error = Error40,
    onError = Neutral99,
    errorContainer = Error90,
    onErrorContainer = Error40,
    
    background = Neutral99,
    onBackground = Neutral10,
    surface = Neutral99,
    onSurface = Neutral10,
    
    surfaceVariant = NeutralVariant90,
    onSurfaceVariant = NeutralVariant30,
    outline = NeutralVariant50,
    outlineVariant = NeutralVariant80
)

/**
 * Cadence应用程序主题Composable
 *
 * @param darkTheme 是否使用深色主题
 * @param dynamicColor 是否使用动态颜色（Android 12+）
 * @param customColorScheme 自定义颜色方案
 * @param content 主题内容
 */
@Composable
fun CadenceTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = true,
    customColorScheme: androidx.compose.material3.ColorScheme? = null,
    content: @Composable () -> Unit
) {
    val colorScheme = customColorScheme ?: when {
        // 动态颜色支持（Android 12+）
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        // 标准主题
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // 使用透明状态栏以支持边到边显示
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

            // 设置状态栏图标颜色
            WindowCompat.getInsetsController(window, view).apply {
                isAppearanceLightStatusBars = !darkTheme
                isAppearanceLightNavigationBars = !darkTheme
            }

            // 启用边到边显示
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = CadenceTypography,
        shapes = CadenceShapes,
        content = content
    )
}

/**
 * 主题色彩枚举
 */
enum class ThemeColor(val displayName: String, val primaryColor: Color, val primaryVariant: Color) {
    DEFAULT("默认蓝色", Primary40, Primary80),
    CLASSIC_BLUE("经典蓝色", CustomThemeColors.ClassicBlue, CustomThemeColors.ClassicBlueVariant),
    WARM_ORANGE("温暖橙色", CustomThemeColors.WarmOrange, CustomThemeColors.WarmOrangeVariant),
    NATURE_GREEN("自然绿色", CustomThemeColors.NatureGreen, CustomThemeColors.NatureGreenVariant),
    ELEGANT_PURPLE("优雅紫色", CustomThemeColors.ElegantPurple, CustomThemeColors.ElegantPurpleVariant),
    MODERN_TEAL("现代青色", CustomThemeColors.ModernTeal, CustomThemeColors.ModernTealVariant),
    VIBRANT_PINK("活力粉色", CustomThemeColors.VibrantPink, CustomThemeColors.VibrantPinkVariant)
}

/**
 * 主题管理器
 */
object ThemeManager {

    /**
     * 根据主题色彩创建自定义颜色方案
     */
    fun createCustomColorScheme(
        themeColor: ThemeColor,
        isDark: Boolean
    ): androidx.compose.material3.ColorScheme {
        return if (isDark) {
            darkColorScheme(
                primary = themeColor.primaryVariant,
                onPrimary = Neutral10,
                primaryContainer = themeColor.primaryColor,
                onPrimaryContainer = Neutral90,

                secondary = Secondary80,
                onSecondary = Neutral10,
                secondaryContainer = Secondary40,
                onSecondaryContainer = Secondary90,

                tertiary = Tertiary80,
                onTertiary = Neutral10,
                tertiaryContainer = Tertiary40,
                onTertiaryContainer = Tertiary90,

                error = Error80,
                onError = Neutral10,
                errorContainer = Error40,
                onErrorContainer = Error90,

                background = Neutral10,
                onBackground = Neutral90,
                surface = Neutral10,
                onSurface = Neutral90,

                surfaceVariant = NeutralVariant30,
                onSurfaceVariant = NeutralVariant80,
                outline = NeutralVariant50,
                outlineVariant = NeutralVariant30
            )
        } else {
            lightColorScheme(
                primary = themeColor.primaryColor,
                onPrimary = Neutral99,
                primaryContainer = themeColor.primaryVariant,
                onPrimaryContainer = Neutral10,

                secondary = Secondary40,
                onSecondary = Neutral99,
                secondaryContainer = Secondary90,
                onSecondaryContainer = Secondary40,

                tertiary = Tertiary40,
                onTertiary = Neutral99,
                tertiaryContainer = Tertiary90,
                onTertiaryContainer = Tertiary40,

                error = Error40,
                onError = Neutral99,
                errorContainer = Error90,
                onErrorContainer = Error40,

                background = Neutral99,
                onBackground = Neutral10,
                surface = Neutral99,
                onSurface = Neutral10,

                surfaceVariant = NeutralVariant90,
                onSurfaceVariant = NeutralVariant30,
                outline = NeutralVariant50,
                outlineVariant = NeutralVariant80
            )
        }
    }
}